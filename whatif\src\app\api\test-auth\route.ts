import { NextRequest, NextResponse } from 'next/server';
import { Angel<PERSON>neClient } from '@/lib/api/angelone';

export async function POST(request: NextRequest) {
  try {
    console.log('🧪 Testing Angel One authentication...');
    
    // Initialize Angel One client with environment variables
    const angelOneClient = new AngelOneClient({
      apiKey: process.env.ANGEL_ONE_API_KEY!,
      clientId: process.env.ANGEL_ONE_CLIENT_ID!,
      password: process.env.ANGEL_ONE_PASSWORD!,
      totpSecret: process.env.ANGEL_ONE_TOTP_SECRET!,
    });

    console.log('🔐 Attempting login...');
    await angelOneClient.login();
    console.log('✅ Login successful!');

    return NextResponse.json({
      status: 'success',
      message: 'Angel One authentication successful'
    });

  } catch (error) {
    console.error('💥 Auth test error:', error);
    console.error('Error type:', error?.constructor?.name);
    console.error('Error message:', error?.message);
    
    return NextResponse.json(
      { 
        error: 'Authentication test failed', 
        details: error?.message,
        type: error?.constructor?.name
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({ message: 'Auth test endpoint ready' });
}
