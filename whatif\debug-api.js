// Debug script to test Angel One API authentication
const axios = require('axios');
const { authenticator } = require('otplib');

async function testAngelOneAuth() {
  try {
    console.log('🔍 Testing Angel One API Authentication...');
    
    const config = {
      apiKey: process.env.ANGEL_ONE_API_KEY || 'TU9sOEpR',
      clientId: process.env.ANGEL_ONE_CLIENT_ID || 'M834963',
      password: process.env.ANGEL_ONE_PASSWORD || '3318',
      totpSecret: process.env.ANGEL_ONE_TOTP_SECRET || 'CRAFUYSVQVWTWPHVWZ55KV5VJI'
    };
    
    console.log('📋 Config:', {
      apiKey: config.apiKey ? '✅ Set' : '❌ Missing',
      clientId: config.clientId ? '✅ Set' : '❌ Missing',
      password: config.password ? '✅ Set' : '❌ Missing',
      totpSecret: config.totpSecret ? '✅ Set' : '❌ Missing'
    });
    
    // Generate TOTP
    const totp = authenticator.generate(config.totpSecret);
    console.log('🔐 Generated TOTP:', totp);
    
    // Prepare login request
    const loginData = {
      clientcode: config.clientId,
      password: config.password,
      totp: totp
    };
    
    console.log('📤 Sending login request...');
    
    // Make login request
    const response = await axios.post('https://apiconnect.angelone.in/rest/auth/angelbroking/user/v1/loginByPassword', loginData, {
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'X-UserType': 'USER',
        'X-SourceID': 'WEB',
        'X-ClientLocalIP': '127.0.0.1',
        'X-ClientPublicIP': '127.0.0.1',
        'X-MACAddress': '00:00:00:00:00:00',
        'X-PrivateKey': config.apiKey
      },
      timeout: 30000
    });
    
    console.log('✅ Login Response Status:', response.status);
    console.log('📊 Response Data:', JSON.stringify(response.data, null, 2));
    
    if (response.data.status && response.data.data) {
      console.log('🎉 Authentication successful!');
      console.log('🔑 JWT Token:', response.data.data.jwtToken ? '✅ Received' : '❌ Missing');
      console.log('🔄 Refresh Token:', response.data.data.refreshToken ? '✅ Received' : '❌ Missing');
      console.log('📡 Feed Token:', response.data.data.feedToken ? '✅ Received' : '❌ Missing');
    } else {
      console.log('❌ Authentication failed:', response.data.message || 'Unknown error');
    }
    
  } catch (error) {
    console.log('💥 Error during authentication:');
    console.log('Error Type:', error.constructor.name);
    console.log('Error Message:', error.message);
    
    if (error.response) {
      console.log('HTTP Status:', error.response.status);
      console.log('Response Data:', JSON.stringify(error.response.data, null, 2));
    }
    
    if (error.code) {
      console.log('Error Code:', error.code);
    }
  }
}

// Run the test
testAngelOneAuth();
