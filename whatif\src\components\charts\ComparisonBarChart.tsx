'use client';

import React from 'react';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Cell,
} from 'recharts';
import { formatCurrency, formatPercentage } from '@/lib/utils';
import { UI_CONFIG } from '@/lib/config';

export interface ComparisonBarData {
  name: string;
  cagr: number;
  absoluteReturn: number;
  currentValue: number;
  initialValue?: number;
}

interface ComparisonBarChartProps {
  data: ComparisonBarData[];
  title?: string;
  height?: number;
  showLegend?: boolean;
  metric?: 'cagr' | 'absoluteReturn' | 'currentValue';
  highlightBest?: boolean;
}

const ComparisonBarChart: React.FC<ComparisonBarChartProps> = ({
  data,
  title = 'Investment Performance Comparison',
  height = UI_CONFIG.charts.defaultHeight,
  showLegend = true,
  metric = 'cagr',
  highlightBest = true,
}) => {
  // Sort data by the selected metric
  const sortedData = [...data].sort((a, b) => b[metric] - a[metric]);
  
  // Get colors for bars
  const getBarColor = (name: string, index: number) => {
    if (name.toLowerCase().includes('investment') || name.toLowerCase().includes('stock')) {
      return UI_CONFIG.charts.colors.investment;
    } else if (name.toLowerCase().includes('nifty')) {
      return UI_CONFIG.charts.colors.nifty;
    } else if (name.toLowerCase().includes('gold')) {
      return UI_CONFIG.charts.colors.gold;
    } else if (name.toLowerCase().includes('fd') || name.toLowerCase().includes('deposit')) {
      return UI_CONFIG.charts.colors.fd;
    }
    
    // Default colors for other items
    const defaultColors = ['#8884d8', '#82ca9d', '#ffc658', '#ff7300', '#00ff00'];
    return defaultColors[index % defaultColors.length];
  };

  // Custom tooltip formatter
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-4 border border-gray-300 rounded-lg shadow-lg">
          <p className="font-semibold text-gray-800 mb-2">{label}</p>
          <p className="text-sm text-blue-600">
            CAGR: {formatPercentage(data.cagr)}
          </p>
          <p className="text-sm text-green-600">
            Absolute Return: {formatPercentage(data.absoluteReturn)}
          </p>
          <p className="text-sm text-purple-600">
            Current Value: {formatCurrency(data.currentValue)}
          </p>
          {data.initialValue && (
            <p className="text-sm text-gray-600">
              Initial Value: {formatCurrency(data.initialValue)}
            </p>
          )}
        </div>
      );
    }
    return null;
  };

  // Format Y-axis based on metric
  const formatYAxis = (value: number) => {
    if (metric === 'currentValue') {
      if (value >= 100000) {
        return `₹${(value / 100000).toFixed(1)}L`;
      } else if (value >= 1000) {
        return `₹${(value / 1000).toFixed(1)}K`;
      }
      return `₹${value}`;
    } else {
      return `${value}%`;
    }
  };

  // Get metric label
  const getMetricLabel = () => {
    switch (metric) {
      case 'cagr':
        return 'CAGR (%)';
      case 'absoluteReturn':
        return 'Absolute Return (%)';
      case 'currentValue':
        return 'Current Value (₹)';
      default:
        return 'Value';
    }
  };

  return (
    <div className="w-full">
      {title && (
        <h3 className="text-lg font-semibold text-gray-800 mb-4 text-center">
          {title}
        </h3>
      )}
      
      <ResponsiveContainer width="100%" height={height}>
        <BarChart
          data={sortedData}
          margin={{
            top: 20,
            right: 30,
            left: 20,
            bottom: 60,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
          
          <XAxis
            dataKey="name"
            stroke="#666"
            fontSize={12}
            tick={{ fill: '#666' }}
            angle={-45}
            textAnchor="end"
            height={80}
          />
          
          <YAxis
            tickFormatter={formatYAxis}
            stroke="#666"
            fontSize={12}
            tick={{ fill: '#666' }}
            label={{ 
              value: getMetricLabel(), 
              angle: -90, 
              position: 'insideLeft',
              style: { textAnchor: 'middle' }
            }}
          />
          
          <Tooltip content={<CustomTooltip />} />
          
          {showLegend && (
            <Legend
              wrapperStyle={{
                paddingTop: '20px',
                fontSize: '14px',
              }}
            />
          )}
          
          <Bar
            dataKey={metric}
            name={getMetricLabel()}
            radius={[4, 4, 0, 0]}
          >
            {sortedData.map((entry, index) => (
              <Cell 
                key={`cell-${index}`} 
                fill={getBarColor(entry.name, index)}
                stroke={highlightBest && index === 0 ? '#000' : 'none'}
                strokeWidth={highlightBest && index === 0 ? 2 : 0}
              />
            ))}
          </Bar>
        </BarChart>
      </ResponsiveContainer>
      
      {/* Performance Rankings */}
      <div className="mt-4">
        <h4 className="text-md font-semibold text-gray-700 mb-2">
          Performance Ranking ({getMetricLabel()})
        </h4>
        <div className="space-y-2">
          {sortedData.map((item, index) => (
            <div 
              key={item.name}
              className={`flex justify-between items-center p-2 rounded ${
                index === 0 ? 'bg-green-50 border border-green-200' : 'bg-gray-50'
              }`}
            >
              <div className="flex items-center">
                <span className={`inline-flex items-center justify-center w-6 h-6 rounded-full text-xs font-bold mr-3 ${
                  index === 0 ? 'bg-green-500 text-white' : 'bg-gray-400 text-white'
                }`}>
                  {index + 1}
                </span>
                <span className="font-medium">{item.name}</span>
              </div>
              <div className="text-right">
                <div className="font-semibold">
                  {metric === 'currentValue' 
                    ? formatCurrency(item[metric])
                    : formatPercentage(item[metric])
                  }
                </div>
                {metric !== 'currentValue' && (
                  <div className="text-sm text-gray-600">
                    {formatCurrency(item.currentValue)}
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ComparisonBarChart;
