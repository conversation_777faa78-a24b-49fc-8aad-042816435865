import { NextRequest, NextResponse } from 'next/server';
import { AngelOneClient } from '@/lib/api/angelone';
import { StockDataService } from '@/lib/services/stockData';
import { BenchmarkDataService } from '@/lib/services/benchmarkData';
import { InvestmentCalculator } from '@/lib/services/investmentCalculator';
import { ComparisonService } from '@/lib/services/comparisonService';
import { InvestmentScenario } from '@/lib/types';
import { generateId } from '@/lib/utils';

// Initialize services
let angelOneClient: AngelOneClient;
let stockDataService: StockDataService;
let benchmarkDataService: BenchmarkDataService;
let investmentCalculator: InvestmentCalculator;
let comparisonService: ComparisonService;

async function initializeServices() {
  if (!angelOneClient) {
    // Initialize Angel One client with environment variables
    angelOneClient = new AngelOneClient({
      apiKey: process.env.ANGEL_ONE_API_KEY!,
      clientId: process.env.ANGEL_ONE_CLIENT_ID!,
      password: process.env.ANGEL_ONE_PASSWORD!,
      totpSecret: process.env.ANGEL_ONE_TOTP_SECRET!,
    });

    // Authenticate
    await angelOneClient.login();

    // Initialize services
    stockDataService = new StockDataService(angelOneClient);
    benchmarkDataService = new BenchmarkDataService(angelOneClient);
    investmentCalculator = new InvestmentCalculator(stockDataService, benchmarkDataService);
    comparisonService = new ComparisonService(investmentCalculator, benchmarkDataService);
  }
}

export async function POST(request: NextRequest) {
  try {
    // Initialize services if not already done
    await initializeServices();

    // Parse request body
    const body = await request.json();
    const { stockSymbol, investmentAmount, startDate, endDate } = body;

    // Validate input
    if (!stockSymbol || !investmentAmount || !startDate || !endDate) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Create investment scenario
    const scenario: InvestmentScenario = {
      id: generateId(),
      stockSymbol,
      investmentAmount: parseFloat(investmentAmount),
      startDate: new Date(startDate),
      endDate: new Date(endDate),
      createdAt: new Date(),
    };

    // Validate date range
    if (scenario.startDate >= scenario.endDate) {
      return NextResponse.json(
        { error: 'Start date must be before end date' },
        { status: 400 }
      );
    }

    if (scenario.startDate > new Date()) {
      return NextResponse.json(
        { error: 'Start date cannot be in the future' },
        { status: 400 }
      );
    }

    // Calculate investment result
    const investmentResult = await investmentCalculator.calculateInvestmentResult(scenario);

    // Calculate comparison with benchmarks
    const comparisonResult = await investmentCalculator.calculateWithComparisons(scenario);

    // Generate comparison summary
    const comparisonSummary = await comparisonService.generateComparisonSummary(scenario);

    // Generate chart data
    const chartData = await comparisonService.generateComparisonChartData(scenario);

    // Return results
    return NextResponse.json({
      investmentResult,
      comparisonResult,
      comparisonSummary,
      chartData,
    });

  } catch (error) {
    console.error('Analysis API error:', error);
    
    // Handle specific error types
    if (error instanceof Error) {
      if (error.message.includes('Stock not found') || error.message.includes('Invalid stock symbol')) {
        return NextResponse.json(
          { error: 'Stock symbol not found. Please check the symbol and try again.' },
          { status: 404 }
        );
      }
      
      if (error.message.includes('Rate limit')) {
        return NextResponse.json(
          { error: 'Too many requests. Please wait a moment and try again.' },
          { status: 429 }
        );
      }
      
      if (error.message.includes('Authentication')) {
        return NextResponse.json(
          { error: 'Authentication failed. Please try again later.' },
          { status: 401 }
        );
      }
    }

    return NextResponse.json(
      { error: 'An error occurred while analyzing the investment. Please try again.' },
      { status: 500 }
    );
  }
}

// Health check endpoint
export async function GET() {
  try {
    await initializeServices();
    return NextResponse.json({ status: 'OK', message: 'Analysis API is running' });
  } catch (error) {
    console.error('Health check failed:', error);
    return NextResponse.json(
      { status: 'ERROR', message: 'Service unavailable' },
      { status: 503 }
    );
  }
}
