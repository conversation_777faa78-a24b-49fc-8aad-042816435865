// Symbol to token mapping for Angel One API
// This maps trading symbols to their corresponding Angel One tokens

export interface StockMapping {
  symbol: string;
  name: string;
  token: string;
  exchange: 'NSE' | 'BSE';
}

export const STOCK_MAPPINGS: Record<string, StockMapping> = {
  'SBIN-EQ': { symbol: 'SBIN-EQ', name: 'State Bank of India', token: '3045', exchange: 'NSE' },
  'RELIANCE-EQ': { symbol: 'RELIANCE-EQ', name: 'Reliance Industries', token: '2885', exchange: 'NSE' },
  'TCS-EQ': { symbol: 'TCS-EQ', name: 'Tata Consultancy Services', token: '11536', exchange: 'NSE' },
  'INFY-EQ': { symbol: 'INFY-EQ', name: 'Infosys Limited', token: '1594', exchange: 'NSE' },
  'HDFCBANK-EQ': { symbol: 'HDFCBANK-EQ', name: 'HDFC Bank', token: '1333', exchange: 'NSE' },
  'ICICIBANK-EQ': { symbol: 'ICICIBANK-EQ', name: 'ICICI Bank', token: '4963', exchange: 'NSE' },
  'BHARTIARTL-EQ': { symbol: 'BHARTIARTL-EQ', name: 'Bharti Airtel', token: '10604', exchange: 'NSE' },
  'ITC-EQ': { symbol: 'ITC-EQ', name: 'ITC Limited', token: '424', exchange: 'NSE' },
  'KOTAKBANK-EQ': { symbol: 'KOTAKBANK-EQ', name: 'Kotak Mahindra Bank', token: '1922', exchange: 'NSE' },
  'LT-EQ': { symbol: 'LT-EQ', name: 'Larsen & Toubro', token: '11483', exchange: 'NSE' },
};

/**
 * Resolve a stock symbol to its Angel One token and exchange
 */
export function resolveStockSymbol(symbol: string): StockMapping {
  const mapping = STOCK_MAPPINGS[symbol];
  if (!mapping) {
    throw new Error(`Unknown stock symbol: ${symbol}. Please use a supported symbol or add it to the mapping.`);
  }
  return mapping;
}

/**
 * Check if a symbol is supported
 */
export function isSymbolSupported(symbol: string): boolean {
  return symbol in STOCK_MAPPINGS;
}

/**
 * Get all supported symbols
 */
export function getSupportedSymbols(): StockMapping[] {
  return Object.values(STOCK_MAPPINGS);
}
