// Symbol to token mapping for Angel One API
// This maps trading symbols to their corresponding Angel One tokens

export interface StockMapping {
  symbol: string;
  name: string;
  token: string;
  exchange: 'NSE' | 'BSE';
  type: 'STOCK' | 'INDEX';
}

export const STOCK_MAPPINGS: Record<string, StockMapping> = {
  'SBIN-EQ': { symbol: 'SBIN-EQ', name: 'State Bank of India', token: '3045', exchange: 'NSE', type: 'STOCK' },
  'RELIANCE-EQ': { symbol: 'RELIANCE-EQ', name: 'Reliance Industries', token: '2885', exchange: 'NSE', type: 'STOCK' },
  'TCS-EQ': { symbol: 'TCS-EQ', name: 'Tata Consultancy Services', token: '11536', exchange: 'NSE', type: 'STOCK' },
  'INFY-EQ': { symbol: 'INFY-EQ', name: 'Infosys Limited', token: '1594', exchange: 'NSE', type: 'STOCK' },
  'HDFCBANK-EQ': { symbol: 'HDFCBANK-EQ', name: 'HDFC Bank', token: '1333', exchange: 'NSE', type: 'STOCK' },
  'ICICIBANK-EQ': { symbol: 'ICICIBANK-EQ', name: 'ICICI Bank', token: '4963', exchange: 'NSE', type: 'STOCK' },
  'BHARTIARTL-EQ': { symbol: 'BHARTIARTL-EQ', name: 'Bharti Airtel', token: '10604', exchange: 'NSE', type: 'STOCK' },
  'ITC-EQ': { symbol: 'ITC-EQ', name: 'ITC Limited', token: '424', exchange: 'NSE', type: 'STOCK' },
  'KOTAKBANK-EQ': { symbol: 'KOTAKBANK-EQ', name: 'Kotak Mahindra Bank', token: '1922', exchange: 'NSE', type: 'STOCK' },
  'LT-EQ': { symbol: 'LT-EQ', name: 'Larsen & Toubro', token: '11483', exchange: 'NSE', type: 'STOCK' },
};

// Market indices for benchmarking (replacing FD and Gold with real market data)
export const INDEX_MAPPINGS: Record<string, StockMapping> = {
  'NIFTY': { symbol: 'NIFTY', name: 'Nifty 50', token: '********', exchange: 'NSE', type: 'INDEX' },
  'BANKNIFTY': { symbol: 'BANKNIFTY', name: 'Bank Nifty', token: '********', exchange: 'NSE', type: 'INDEX' },
  'NIFTYIT': { symbol: 'NIFTYIT', name: 'Nifty IT', token: '********', exchange: 'NSE', type: 'INDEX' },
  'SENSEX': { symbol: 'SENSEX', name: 'BSE Sensex', token: '********', exchange: 'BSE', type: 'INDEX' },
  'NIFTYNEXT50': { symbol: 'NIFTYNEXT50', name: 'Nifty Next 50', token: '********', exchange: 'NSE', type: 'INDEX' },
};

/**
 * Resolve a stock symbol to its Angel One token and exchange
 */
export function resolveStockSymbol(symbol: string): StockMapping {
  const mapping = STOCK_MAPPINGS[symbol];
  if (!mapping) {
    throw new Error(`Unknown stock symbol: ${symbol}. Please use a supported symbol or add it to the mapping.`);
  }
  return mapping;
}

/**
 * Resolve an index symbol to its Angel One token and exchange
 */
export function resolveIndexSymbol(symbol: string): StockMapping {
  const mapping = INDEX_MAPPINGS[symbol];
  if (!mapping) {
    throw new Error(`Unknown index symbol: ${symbol}. Please use a supported index or add it to the mapping.`);
  }
  return mapping;
}

/**
 * Resolve any symbol (stock or index) to its mapping
 */
export function resolveSymbol(symbol: string): StockMapping {
  // Try stock mapping first
  if (symbol in STOCK_MAPPINGS) {
    return STOCK_MAPPINGS[symbol];
  }

  // Try index mapping
  if (symbol in INDEX_MAPPINGS) {
    return INDEX_MAPPINGS[symbol];
  }

  throw new Error(`Unknown symbol: ${symbol}. Please use a supported stock or index symbol.`);
}

/**
 * Check if a symbol is supported (stock or index)
 */
export function isSymbolSupported(symbol: string): boolean {
  return symbol in STOCK_MAPPINGS || symbol in INDEX_MAPPINGS;
}

/**
 * Get all supported symbols (stocks and indices)
 */
export function getSupportedSymbols(): StockMapping[] {
  return [...Object.values(STOCK_MAPPINGS), ...Object.values(INDEX_MAPPINGS)];
}

/**
 * Get all supported stock symbols
 */
export function getSupportedStocks(): StockMapping[] {
  return Object.values(STOCK_MAPPINGS);
}

/**
 * Get all supported index symbols
 */
export function getSupportedIndices(): StockMapping[] {
  return Object.values(INDEX_MAPPINGS);
}
