import { NextRequest, NextResponse } from 'next/server';
import { AngelOneClient } from '@/lib/api/angelone';
import { StockDataService } from '@/lib/services/stockData';
import { BenchmarkDataService } from '@/lib/services/benchmarkData';
import { InvestmentCalculator } from '@/lib/services/investmentCalculator';
import { ComparisonService } from '@/lib/services/comparisonService';

export async function POST(request: NextRequest) {
  try {
    console.log('🧪 Testing service initialization step by step...');
    
    // Step 1: Initialize Angel One client
    console.log('1️⃣ Initializing Angel One client...');
    const angelOneClient = new AngelOneClient({
      apiKey: process.env.ANGEL_ONE_API_KEY!,
      clientId: process.env.ANGEL_ONE_CLIENT_ID!,
      password: process.env.ANGEL_ONE_PASSWORD!,
      totpSecret: process.env.ANGEL_ONE_TOTP_SECRET!,
    });
    console.log('✅ Angel One client created');

    // Step 2: Authenticate
    console.log('2️⃣ Authenticating...');
    await angelOneClient.login();
    console.log('✅ Authentication successful');

    // Step 3: Initialize StockDataService
    console.log('3️⃣ Initializing StockDataService...');
    const stockDataService = new StockDataService(angelOneClient);
    console.log('✅ StockDataService initialized');

    // Step 4: Initialize BenchmarkDataService
    console.log('4️⃣ Initializing BenchmarkDataService...');
    const benchmarkDataService = new BenchmarkDataService(angelOneClient);
    console.log('✅ BenchmarkDataService initialized');

    // Step 5: Initialize InvestmentCalculator
    console.log('5️⃣ Initializing InvestmentCalculator...');
    const investmentCalculator = new InvestmentCalculator(stockDataService, benchmarkDataService);
    console.log('✅ InvestmentCalculator initialized');

    // Step 6: Initialize ComparisonService
    console.log('6️⃣ Initializing ComparisonService...');
    const comparisonService = new ComparisonService(investmentCalculator, benchmarkDataService);
    console.log('✅ ComparisonService initialized');

    console.log('🎉 All services initialized successfully!');

    return NextResponse.json({
      status: 'success',
      message: 'All services initialized successfully',
      services: {
        angelOneClient: '✅ Ready',
        stockDataService: '✅ Ready',
        benchmarkDataService: '✅ Ready',
        investmentCalculator: '✅ Ready',
        comparisonService: '✅ Ready'
      }
    });

  } catch (error) {
    console.error('💥 Service initialization error:', error);
    console.error('Error type:', error?.constructor?.name);
    console.error('Error message:', error?.message);
    console.error('Error stack:', error?.stack);
    
    return NextResponse.json(
      { 
        error: 'Service initialization failed', 
        details: error?.message,
        type: error?.constructor?.name,
        stack: error?.stack
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({ message: 'Service test endpoint ready' });
}
