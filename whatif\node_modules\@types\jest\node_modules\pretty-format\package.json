{"name": "pretty-format", "version": "30.0.2", "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/pretty-format"}, "license": "MIT", "description": "Stringify any JavaScript value.", "main": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "require": "./build/index.js", "import": "./build/index.mjs", "default": "./build/index.js"}, "./package.json": "./package.json"}, "author": "<PERSON> <<EMAIL>>", "dependencies": {"@jest/schemas": "30.0.1", "ansi-styles": "^5.2.0", "react-is": "^18.3.1"}, "devDependencies": {"@types/react": "^18.3.23", "@types/react-is": "^18.3.1", "@types/react-test-renderer": "^18.3.1", "immutable": "^5.1.2", "jest-util": "30.0.2", "react": "18.3.1", "react-dom": "18.3.1", "react-test-renderer": "18.3.1"}, "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "publishConfig": {"access": "public"}, "gitHead": "393acbfac31f64bb38dff23c89224797caded83c"}