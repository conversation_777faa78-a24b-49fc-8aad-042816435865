'use client';

import { useState } from 'react';
import { InvestmentScenario } from '@/lib/types';

interface InvestmentAnalysisFormProps {
  onSubmit: (scenario: Omit<InvestmentScenario, 'id' | 'createdAt'>) => void;
  loading: boolean;
  error: string | null;
}

// Common Indian stocks for quick selection
const POPULAR_STOCKS = [
  { symbol: 'SBIN-EQ', name: 'State Bank of India', token: '3045' },
  { symbol: 'RELIANCE-EQ', name: 'Reliance Industries', token: '2885' },
  { symbol: 'TCS-EQ', name: 'Tata Consultancy Services', token: '11536' },
  { symbol: 'INFY-EQ', name: 'Infosys Limited', token: '1594' },
  { symbol: 'HDFCBANK-EQ', name: 'HDFC Bank', token: '1333' },
  { symbol: 'ICICIBANK-EQ', name: 'ICICI Bank', token: '4963' },
  { symbol: 'BHARTIARTL-EQ', name: '<PERSON><PERSON>i Airtel', token: '10604' },
  { symbol: 'ITC-EQ', name: 'ITC Limited', token: '424' },
  { symbol: 'KOTAKBANK-EQ', name: 'Kotak Mahindra Bank', token: '1922' },
  { symbol: 'LT-EQ', name: 'Larsen & Toubro', token: '11483' },
];

export function InvestmentAnalysisForm({ onSubmit, loading, error }: InvestmentAnalysisFormProps) {
  const [formData, setFormData] = useState({
    stockSymbol: '',
    customStock: '',
    investmentAmount: '',
    startDate: '',
    endDate: new Date().toISOString().split('T')[0], // Today's date
  });

  const [useCustomStock, setUseCustomStock] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const stockSymbol = useCustomStock ? formData.customStock : formData.stockSymbol;
    
    if (!stockSymbol || !formData.investmentAmount || !formData.startDate) {
      return;
    }

    onSubmit({
      stockSymbol,
      investmentAmount: parseFloat(formData.investmentAmount),
      startDate: new Date(formData.startDate),
      endDate: new Date(formData.endDate),
    });
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const selectedStock = POPULAR_STOCKS.find(stock => stock.symbol === formData.stockSymbol);

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Stock Selection */}
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Stock Selection
            </label>
            <div className="space-y-3">
              <div>
                <label className="flex items-center space-x-2">
                  <input
                    type="radio"
                    checked={!useCustomStock}
                    onChange={() => setUseCustomStock(false)}
                    className="text-blue-600"
                  />
                  <span className="text-sm text-gray-700 dark:text-gray-300">Popular Stocks</span>
                </label>
                {!useCustomStock && (
                  <select
                    value={formData.stockSymbol}
                    onChange={(e) => handleInputChange('stockSymbol', e.target.value)}
                    className="mt-2 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                    required={!useCustomStock}
                  >
                    <option value="">Select a stock...</option>
                    {POPULAR_STOCKS.map((stock) => (
                      <option key={stock.symbol} value={stock.symbol}>
                        {stock.name} ({stock.symbol})
                      </option>
                    ))}
                  </select>
                )}
              </div>
              
              <div>
                <label className="flex items-center space-x-2">
                  <input
                    type="radio"
                    checked={useCustomStock}
                    onChange={() => setUseCustomStock(true)}
                    className="text-blue-600"
                  />
                  <span className="text-sm text-gray-700 dark:text-gray-300">Custom Stock Symbol</span>
                </label>
                {useCustomStock && (
                  <input
                    type="text"
                    value={formData.customStock}
                    onChange={(e) => handleInputChange('customStock', e.target.value)}
                    placeholder="e.g., WIPRO-EQ"
                    className="mt-2 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                    required={useCustomStock}
                  />
                )}
              </div>
            </div>
            
            {selectedStock && !useCustomStock && (
              <div className="mt-2 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-md">
                <p className="text-sm text-blue-700 dark:text-blue-300">
                  Selected: <strong>{selectedStock.name}</strong>
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Investment Amount */}
        <div>
          <label htmlFor="investmentAmount" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Investment Amount (₹)
          </label>
          <input
            type="number"
            id="investmentAmount"
            value={formData.investmentAmount}
            onChange={(e) => handleInputChange('investmentAmount', e.target.value)}
            placeholder="e.g., 100000"
            min="1000"
            step="1000"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            required
          />
          <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
            Minimum investment: ₹1,000
          </p>
        </div>

        {/* Start Date */}
        <div>
          <label htmlFor="startDate" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Investment Start Date
          </label>
          <input
            type="date"
            id="startDate"
            value={formData.startDate}
            onChange={(e) => handleInputChange('startDate', e.target.value)}
            max={new Date().toISOString().split('T')[0]}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            required
          />
        </div>

        {/* End Date */}
        <div>
          <label htmlFor="endDate" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Analysis End Date
          </label>
          <input
            type="date"
            id="endDate"
            value={formData.endDate}
            onChange={(e) => handleInputChange('endDate', e.target.value)}
            max={new Date().toISOString().split('T')[0]}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            required
          />
          <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
            Default: Today's date
          </p>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
          <p className="text-sm text-red-700 dark:text-red-300">{error}</p>
        </div>
      )}

      {/* Submit Button */}
      <div className="flex justify-center">
        <button
          type="submit"
          disabled={loading}
          className="px-8 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          {loading ? (
            <div className="flex items-center space-x-2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              <span>Analyzing...</span>
            </div>
          ) : (
            'Analyze Investment'
          )}
        </button>
      </div>

      {/* Quick Examples */}
      <div className="mt-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-md">
        <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Quick Examples:</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-xs text-gray-600 dark:text-gray-400">
          <div>• ₹1,00,000 in SBIN from Jan 2023</div>
          <div>• ₹50,000 in TCS from Jan 2022</div>
          <div>• ₹2,00,000 in RELIANCE from Jan 2021</div>
          <div>• ₹75,000 in INFY from Jan 2020</div>
        </div>
      </div>
    </form>
  );
}
