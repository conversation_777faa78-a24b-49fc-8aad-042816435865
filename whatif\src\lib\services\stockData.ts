// Stock data service for the What If investment analysis tool

import { format, parseISO } from 'date-fns';
import { AngelOneClient } from '../api/angelone';
import { validateDateRange, calculateYearsBetweenDates } from '../utils';
import { resolveStockSymbol } from '../utils/symbolMapping';
import {
  HistoricalPrice,
  StockData,
  InvestmentScenario,
  InvestmentResult,
  AngelOneHistoricalRequest,
  AngelOneLTPRequest,
} from '../types';

export class StockDataService {
  private angelOneClient: AngelOneClient;

  constructor(angelOneClient: AngelOneClient) {
    this.angelOneClient = angelOneClient;
  }

  /**
   * Get historical price data for a stock with automatic chunking for large date ranges
   */
  async getHistoricalPrices(
    symbolToken: string,
    exchange: 'NSE' | 'BSE',
    startDate: Date,
    endDate: Date,
    interval: 'ONE_DAY' = 'ONE_DAY'
  ): Promise<HistoricalPrice[]> {
    // Validate date range
    const validation = validateDateRange(startDate, endDate);
    if (!validation.isValid) {
      throw new Error(validation.error);
    }

    // Calculate date range in days
    const daysDifference = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));

    // If date range is larger than 2 years (730 days), chunk the requests
    const MAX_DAYS_PER_CHUNK = 730; // 2 years

    if (daysDifference <= MAX_DAYS_PER_CHUNK) {
      // Single request for smaller ranges
      return this.fetchSingleDateRange(symbolToken, exchange, startDate, endDate, interval);
    } else {
      // Chunk large date ranges
      console.log(`📅 Large date range detected (${daysDifference} days). Chunking into smaller requests...`);
      return this.fetchChunkedDateRange(symbolToken, exchange, startDate, endDate, interval, MAX_DAYS_PER_CHUNK);
    }
  }

  /**
   * Fetch historical data for a single date range
   */
  private async fetchSingleDateRange(
    symbolToken: string,
    exchange: 'NSE' | 'BSE',
    startDate: Date,
    endDate: Date,
    interval: 'ONE_DAY'
  ): Promise<HistoricalPrice[]> {
    const fromdate = format(startDate, 'yyyy-MM-dd HH:mm');
    const todate = format(endDate, 'yyyy-MM-dd HH:mm');

    const request: AngelOneHistoricalRequest = {
      exchange,
      symboltoken: symbolToken,
      interval,
      fromdate,
      todate,
    };

    try {
      console.log(`📊 Fetching data: ${fromdate} to ${todate}`);
      const historicalData = await this.angelOneClient.getHistoricalData(request);

      // Sort by date ascending
      return historicalData.sort((a, b) => a.date.getTime() - b.date.getTime());
    } catch (error) {
      console.error('Error fetching historical data:', error);
      throw new Error(`Failed to fetch historical data: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Fetch historical data in chunks for large date ranges
   */
  private async fetchChunkedDateRange(
    symbolToken: string,
    exchange: 'NSE' | 'BSE',
    startDate: Date,
    endDate: Date,
    interval: 'ONE_DAY',
    maxDaysPerChunk: number
  ): Promise<HistoricalPrice[]> {
    const allData: HistoricalPrice[] = [];
    let currentStart = new Date(startDate);
    let chunkNumber = 1;

    while (currentStart < endDate) {
      // Calculate chunk end date
      const chunkEnd = new Date(currentStart);
      chunkEnd.setDate(chunkEnd.getDate() + maxDaysPerChunk);

      // Don't exceed the actual end date
      if (chunkEnd > endDate) {
        chunkEnd.setTime(endDate.getTime());
      }

      console.log(`📦 Fetching chunk ${chunkNumber}: ${format(currentStart, 'yyyy-MM-dd')} to ${format(chunkEnd, 'yyyy-MM-dd')}`);

      try {
        const chunkData = await this.fetchSingleDateRange(symbolToken, exchange, currentStart, chunkEnd, interval);
        allData.push(...chunkData);

        // Add a small delay between requests to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 100));

      } catch (error) {
        console.error(`Error fetching chunk ${chunkNumber}:`, error);
        // Continue with next chunk instead of failing completely
        console.log(`⚠️ Skipping chunk ${chunkNumber} due to error, continuing...`);
      }

      // Move to next chunk
      currentStart = new Date(chunkEnd);
      currentStart.setDate(currentStart.getDate() + 1);
      chunkNumber++;
    }

    if (allData.length === 0) {
      throw new Error('No historical data could be fetched for any date range');
    }

    console.log(`✅ Successfully fetched ${allData.length} data points across ${chunkNumber - 1} chunks`);

    // Remove duplicates and sort by date
    const uniqueData = allData.filter((item, index, arr) =>
      index === arr.findIndex(other => other.date.getTime() === item.date.getTime())
    );

    return uniqueData.sort((a, b) => a.date.getTime() - b.date.getTime());
  }

  /**
   * Get current stock price
   */
  async getCurrentPrice(
    tradingSymbol: string,
    symbolToken: string,
    exchange: 'NSE' | 'BSE'
  ): Promise<StockData> {
    const request: AngelOneLTPRequest = {
      exchange,
      tradingsymbol: tradingSymbol,
      symboltoken: symbolToken,
    };

    try {
      return await this.angelOneClient.getCurrentPrice(request);
    } catch (error) {
      console.error('Error fetching current price:', error);
      throw new Error(`Failed to fetch current price: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Calculate investment result for a scenario
   */
  async calculateInvestmentResult(scenario: InvestmentScenario): Promise<InvestmentResult> {
    try {
      // Resolve stock symbol to token and exchange
      const stockMapping = resolveStockSymbol(scenario.stockSymbol);
      console.log(`📊 Resolved ${scenario.stockSymbol} to token ${stockMapping.token} on ${stockMapping.exchange}`);

      // Get historical data for the investment period
      const historicalData = await this.getHistoricalPrices(
        stockMapping.token, // Use the resolved token
        stockMapping.exchange, // Use the resolved exchange
        scenario.startDate,
        scenario.endDate
      );

      if (historicalData.length === 0) {
        throw new Error('No historical data available for the specified period');
      }

      // Get start and end prices
      const startPrice = historicalData[0].close;
      const endPrice = historicalData[historicalData.length - 1].close;

      // Calculate number of shares that could be bought
      const numberOfShares = scenario.investmentAmount / startPrice;

      // Calculate current value
      const currentValue = numberOfShares * endPrice;

      // Calculate returns
      const absoluteReturn = ((currentValue - scenario.investmentAmount) / scenario.investmentAmount) * 100;
      const years = calculateYearsBetweenDates(scenario.startDate, scenario.endDate);
      const cagr = years > 0 ? (Math.pow(currentValue / scenario.investmentAmount, 1 / years) - 1) * 100 : 0;

      return {
        scenario,
        initialValue: scenario.investmentAmount,
        currentValue,
        absoluteReturn,
        cagr: Number(cagr.toFixed(2)),
        totalReturn: currentValue - scenario.investmentAmount,
        annualizedReturn: cagr,
      };
    } catch (error) {
      console.error('Error calculating investment result:', error);
      throw new Error(`Failed to calculate investment result: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get price at a specific date (or closest available date)
   */
  async getPriceAtDate(
    symbolToken: string,
    exchange: 'NSE' | 'BSE',
    targetDate: Date
  ): Promise<{ price: number; actualDate: Date } | null> {
    try {
      // Get data for a small range around the target date
      const startDate = new Date(targetDate);
      startDate.setDate(startDate.getDate() - 5); // 5 days before

      const endDate = new Date(targetDate);
      endDate.setDate(endDate.getDate() + 5); // 5 days after

      const historicalData = await this.getHistoricalPrices(
        symbolToken,
        exchange,
        startDate,
        endDate
      );

      if (historicalData.length === 0) {
        return null;
      }

      // Find the closest date
      let closestData = historicalData[0];
      let minDiff = Math.abs(closestData.date.getTime() - targetDate.getTime());

      for (const data of historicalData) {
        const diff = Math.abs(data.date.getTime() - targetDate.getTime());
        if (diff < minDiff) {
          minDiff = diff;
          closestData = data;
        }
      }

      return {
        price: closestData.close,
        actualDate: closestData.date,
      };
    } catch (error) {
      console.error('Error getting price at date:', error);
      return null;
    }
  }

  /**
   * Validate stock symbol and get basic info
   */
  async validateStock(
    tradingSymbol: string,
    symbolToken: string,
    exchange: 'NSE' | 'BSE'
  ): Promise<{ isValid: boolean; stockData?: StockData; error?: string }> {
    try {
      const stockData = await this.getCurrentPrice(tradingSymbol, symbolToken, exchange);
      return {
        isValid: true,
        stockData,
      };
    } catch (error) {
      return {
        isValid: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Get multiple stocks' current prices
   */
  async getMultipleCurrentPrices(
    stocks: Array<{
      tradingSymbol: string;
      symbolToken: string;
      exchange: 'NSE' | 'BSE';
    }>
  ): Promise<StockData[]> {
    const results: StockData[] = [];
    
    // Process stocks sequentially to respect rate limits
    for (const stock of stocks) {
      try {
        const stockData = await this.getCurrentPrice(
          stock.tradingSymbol,
          stock.symbolToken,
          stock.exchange
        );
        results.push(stockData);
      } catch (error) {
        console.error(`Error fetching price for ${stock.tradingSymbol}:`, error);
        // Continue with other stocks even if one fails
      }
    }

    return results;
  }
}
