// Stock data service for the What If investment analysis tool

import { format, parseISO } from 'date-fns';
import { AngelOneClient } from '../api/angelone';
import { validateDateRange, calculateYearsBetweenDates } from '../utils';
import { resolveStockSymbol } from '../utils/symbolMapping';
import {
  HistoricalPrice,
  StockData,
  InvestmentScenario,
  InvestmentResult,
  AngelOneHistoricalRequest,
  AngelOneLTPRequest,
} from '../types';

export class StockDataService {
  private angelOneClient: AngelOneClient;

  constructor(angelOneClient: AngelOneClient) {
    this.angelOneClient = angelOneClient;
  }

  /**
   * Get historical price data for a stock
   */
  async getHistoricalPrices(
    symbolToken: string,
    exchange: 'NSE' | 'BSE',
    startDate: Date,
    endDate: Date,
    interval: 'ONE_DAY' = 'ONE_DAY'
  ): Promise<HistoricalPrice[]> {
    // Validate date range
    const validation = validateDateRange(startDate, endDate);
    if (!validation.isValid) {
      throw new Error(validation.error);
    }

    // Format dates for Angel One API
    const fromdate = format(startDate, 'yyyy-MM-dd HH:mm');
    const todate = format(endDate, 'yyyy-MM-dd HH:mm');

    const request: AngelOneHistoricalRequest = {
      exchange,
      symboltoken: symbolToken,
      interval,
      fromdate,
      todate,
    };

    try {
      const historicalData = await this.angelOneClient.getHistoricalData(request);
      
      // Sort by date ascending
      return historicalData.sort((a, b) => a.date.getTime() - b.date.getTime());
    } catch (error) {
      console.error('Error fetching historical data:', error);
      throw new Error(`Failed to fetch historical data: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get current stock price
   */
  async getCurrentPrice(
    tradingSymbol: string,
    symbolToken: string,
    exchange: 'NSE' | 'BSE'
  ): Promise<StockData> {
    const request: AngelOneLTPRequest = {
      exchange,
      tradingsymbol: tradingSymbol,
      symboltoken: symbolToken,
    };

    try {
      return await this.angelOneClient.getCurrentPrice(request);
    } catch (error) {
      console.error('Error fetching current price:', error);
      throw new Error(`Failed to fetch current price: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Calculate investment result for a scenario
   */
  async calculateInvestmentResult(scenario: InvestmentScenario): Promise<InvestmentResult> {
    try {
      // Resolve stock symbol to token and exchange
      const stockMapping = resolveStockSymbol(scenario.stockSymbol);
      console.log(`📊 Resolved ${scenario.stockSymbol} to token ${stockMapping.token} on ${stockMapping.exchange}`);

      // Get historical data for the investment period
      const historicalData = await this.getHistoricalPrices(
        stockMapping.token, // Use the resolved token
        stockMapping.exchange, // Use the resolved exchange
        scenario.startDate,
        scenario.endDate
      );

      if (historicalData.length === 0) {
        throw new Error('No historical data available for the specified period');
      }

      // Get start and end prices
      const startPrice = historicalData[0].close;
      const endPrice = historicalData[historicalData.length - 1].close;

      // Calculate number of shares that could be bought
      const numberOfShares = scenario.investmentAmount / startPrice;

      // Calculate current value
      const currentValue = numberOfShares * endPrice;

      // Calculate returns
      const absoluteReturn = ((currentValue - scenario.investmentAmount) / scenario.investmentAmount) * 100;
      const years = calculateYearsBetweenDates(scenario.startDate, scenario.endDate);
      const cagr = years > 0 ? (Math.pow(currentValue / scenario.investmentAmount, 1 / years) - 1) * 100 : 0;

      return {
        scenario,
        initialValue: scenario.investmentAmount,
        currentValue,
        absoluteReturn,
        cagr: Number(cagr.toFixed(2)),
        totalReturn: currentValue - scenario.investmentAmount,
        annualizedReturn: cagr,
      };
    } catch (error) {
      console.error('Error calculating investment result:', error);
      throw new Error(`Failed to calculate investment result: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get price at a specific date (or closest available date)
   */
  async getPriceAtDate(
    symbolToken: string,
    exchange: 'NSE' | 'BSE',
    targetDate: Date
  ): Promise<{ price: number; actualDate: Date } | null> {
    try {
      // Get data for a small range around the target date
      const startDate = new Date(targetDate);
      startDate.setDate(startDate.getDate() - 5); // 5 days before

      const endDate = new Date(targetDate);
      endDate.setDate(endDate.getDate() + 5); // 5 days after

      const historicalData = await this.getHistoricalPrices(
        symbolToken,
        exchange,
        startDate,
        endDate
      );

      if (historicalData.length === 0) {
        return null;
      }

      // Find the closest date
      let closestData = historicalData[0];
      let minDiff = Math.abs(closestData.date.getTime() - targetDate.getTime());

      for (const data of historicalData) {
        const diff = Math.abs(data.date.getTime() - targetDate.getTime());
        if (diff < minDiff) {
          minDiff = diff;
          closestData = data;
        }
      }

      return {
        price: closestData.close,
        actualDate: closestData.date,
      };
    } catch (error) {
      console.error('Error getting price at date:', error);
      return null;
    }
  }

  /**
   * Validate stock symbol and get basic info
   */
  async validateStock(
    tradingSymbol: string,
    symbolToken: string,
    exchange: 'NSE' | 'BSE'
  ): Promise<{ isValid: boolean; stockData?: StockData; error?: string }> {
    try {
      const stockData = await this.getCurrentPrice(tradingSymbol, symbolToken, exchange);
      return {
        isValid: true,
        stockData,
      };
    } catch (error) {
      return {
        isValid: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Get multiple stocks' current prices
   */
  async getMultipleCurrentPrices(
    stocks: Array<{
      tradingSymbol: string;
      symbolToken: string;
      exchange: 'NSE' | 'BSE';
    }>
  ): Promise<StockData[]> {
    const results: StockData[] = [];
    
    // Process stocks sequentially to respect rate limits
    for (const stock of stocks) {
      try {
        const stockData = await this.getCurrentPrice(
          stock.tradingSymbol,
          stock.symbolToken,
          stock.exchange
        );
        results.push(stockData);
      } catch (error) {
        console.error(`Error fetching price for ${stock.tradingSymbol}:`, error);
        // Continue with other stocks even if one fails
      }
    }

    return results;
  }
}
