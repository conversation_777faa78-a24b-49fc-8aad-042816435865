'use client';

import React, { useState } from 'react';
import { formatCurrency, formatPercentage } from '@/lib/utils';

export interface ComparisonTableData {
  name: string;
  type: string;
  initialValue: number;
  currentValue: number;
  totalReturn: number;
  cagr: number;
  absoluteReturn: number;
  rank: number;
  outperformance?: number;
}

interface ComparisonTableProps {
  data: ComparisonTableData[];
  investmentName: string;
  title?: string;
  showOutperformance?: boolean;
}

type SortField = 'name' | 'cagr' | 'absoluteReturn' | 'currentValue' | 'totalReturn' | 'rank';
type SortDirection = 'asc' | 'desc';

const ComparisonTable: React.FC<ComparisonTableProps> = ({
  data,
  investmentName,
  title = 'Investment Performance Comparison',
  showOutperformance = true,
}) => {
  const [sortField, setSortField] = useState<SortField>('rank');
  const [sortDirection, setSortDirection] = useState<SortDirection>('asc');

  // Sort data based on current sort field and direction
  const sortedData = [...data].sort((a, b) => {
    let aValue = a[sortField];
    let bValue = b[sortField];

    if (typeof aValue === 'string') {
      aValue = aValue.toLowerCase();
      bValue = (bValue as string).toLowerCase();
    }

    if (sortDirection === 'asc') {
      return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
    } else {
      return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
    }
  });

  // Handle column header click for sorting
  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc'); // Default to descending for numeric fields
    }
  };

  // Get sort icon
  const getSortIcon = (field: SortField) => {
    if (sortField !== field) return '↕️';
    return sortDirection === 'asc' ? '↑' : '↓';
  };

  // Get row styling based on performance
  const getRowStyling = (item: ComparisonTableData) => {
    if (item.name.toLowerCase().includes(investmentName.toLowerCase()) || 
        item.name.toLowerCase().includes('investment')) {
      return 'bg-blue-50 border-l-4 border-blue-500';
    }
    
    if (item.rank === 1) {
      return 'bg-green-50 border-l-4 border-green-500';
    }
    
    if (item.rank === data.length) {
      return 'bg-red-50 border-l-4 border-red-300';
    }
    
    return 'bg-white hover:bg-gray-50';
  };

  // Get performance indicator
  const getPerformanceIndicator = (cagr: number) => {
    if (cagr > 15) return { icon: '🚀', color: 'text-green-600', label: 'Excellent' };
    if (cagr > 10) return { icon: '📈', color: 'text-green-500', label: 'Good' };
    if (cagr > 5) return { icon: '📊', color: 'text-yellow-500', label: 'Average' };
    if (cagr > 0) return { icon: '📉', color: 'text-orange-500', label: 'Below Average' };
    return { icon: '❌', color: 'text-red-500', label: 'Loss' };
  };

  const TableHeader: React.FC<{ field: SortField; children: React.ReactNode; className?: string }> = ({
    field,
    children,
    className = '',
  }) => (
    <th
      className={`px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 ${className}`}
      onClick={() => handleSort(field)}
    >
      <div className="flex items-center space-x-1">
        <span>{children}</span>
        <span className="text-gray-400">{getSortIcon(field)}</span>
      </div>
    </th>
  );

  return (
    <div className="w-full">
      {title && (
        <h3 className="text-lg font-semibold text-gray-800 mb-4 text-center">
          {title}
        </h3>
      )}

      <div className="overflow-x-auto shadow-lg rounded-lg">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <TableHeader field="rank" className="w-16">
                Rank
              </TableHeader>
              <TableHeader field="name" className="min-w-48">
                Investment Option
              </TableHeader>
              <TableHeader field="cagr">
                CAGR
              </TableHeader>
              <TableHeader field="absoluteReturn">
                Total Return
              </TableHeader>
              <TableHeader field="currentValue">
                Current Value
              </TableHeader>
              <TableHeader field="totalReturn">
                Profit/Loss
              </TableHeader>
              {showOutperformance && (
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  vs {investmentName}
                </th>
              )}
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Performance
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {sortedData.map((item, index) => {
              const performance = getPerformanceIndicator(item.cagr);
              
              return (
                <tr key={item.name} className={getRowStyling(item)}>
                  <td className="px-4 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <span className={`inline-flex items-center justify-center w-8 h-8 rounded-full text-sm font-bold ${
                        item.rank === 1 ? 'bg-green-500 text-white' : 
                        item.rank === data.length ? 'bg-red-400 text-white' : 
                        'bg-gray-400 text-white'
                      }`}>
                        {item.rank}
                      </span>
                    </div>
                  </td>
                  
                  <td className="px-4 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {item.name}
                      </div>
                      <div className="text-sm text-gray-500">
                        {item.type}
                      </div>
                    </div>
                  </td>
                  
                  <td className="px-4 py-4 whitespace-nowrap">
                    <div className={`text-sm font-semibold ${
                      item.cagr > 0 ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {formatPercentage(item.cagr)}
                    </div>
                  </td>
                  
                  <td className="px-4 py-4 whitespace-nowrap">
                    <div className={`text-sm font-semibold ${
                      item.absoluteReturn > 0 ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {formatPercentage(item.absoluteReturn)}
                    </div>
                  </td>
                  
                  <td className="px-4 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">
                      {formatCurrency(item.currentValue)}
                    </div>
                    <div className="text-xs text-gray-500">
                      from {formatCurrency(item.initialValue)}
                    </div>
                  </td>
                  
                  <td className="px-4 py-4 whitespace-nowrap">
                    <div className={`text-sm font-semibold ${
                      item.totalReturn > 0 ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {item.totalReturn > 0 ? '+' : ''}{formatCurrency(item.totalReturn)}
                    </div>
                  </td>
                  
                  {showOutperformance && (
                    <td className="px-4 py-4 whitespace-nowrap">
                      {item.outperformance !== undefined && (
                        <div className={`text-sm font-semibold ${
                          item.outperformance > 0 ? 'text-green-600' : 
                          item.outperformance < 0 ? 'text-red-600' : 'text-gray-600'
                        }`}>
                          {item.outperformance > 0 ? '+' : ''}{formatPercentage(item.outperformance)}
                        </div>
                      )}
                    </td>
                  )}
                  
                  <td className="px-4 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <span className="mr-2">{performance.icon}</span>
                      <span className={`text-sm font-medium ${performance.color}`}>
                        {performance.label}
                      </span>
                    </div>
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>

      {/* Summary Statistics */}
      <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-blue-50 p-4 rounded-lg">
          <h4 className="text-sm font-semibold text-blue-800 mb-2">Best Performer</h4>
          <div className="text-lg font-bold text-blue-600">
            {sortedData.find(item => item.rank === 1)?.name}
          </div>
          <div className="text-sm text-blue-500">
            {formatPercentage(sortedData.find(item => item.rank === 1)?.cagr || 0)} CAGR
          </div>
        </div>
        
        <div className="bg-green-50 p-4 rounded-lg">
          <h4 className="text-sm font-semibold text-green-800 mb-2">Average CAGR</h4>
          <div className="text-lg font-bold text-green-600">
            {formatPercentage(data.reduce((sum, item) => sum + item.cagr, 0) / data.length)}
          </div>
          <div className="text-sm text-green-500">
            Across all options
          </div>
        </div>
        
        <div className="bg-purple-50 p-4 rounded-lg">
          <h4 className="text-sm font-semibold text-purple-800 mb-2">Your Investment</h4>
          <div className="text-lg font-bold text-purple-600">
            Rank #{data.find(item => 
              item.name.toLowerCase().includes(investmentName.toLowerCase()) ||
              item.name.toLowerCase().includes('investment')
            )?.rank || 'N/A'}
          </div>
          <div className="text-sm text-purple-500">
            Out of {data.length} options
          </div>
        </div>
      </div>
    </div>
  );
};

export default ComparisonTable;
