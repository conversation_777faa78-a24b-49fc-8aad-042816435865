/**
 * Creates a new object with the same values as the given object, but with keys generated
 * by running each own enumerable property of the object through the iteratee function.
 *
 * @template T - The type of the object.
 * @template K - The type of the new keys generated by the iteratee function.
 *
 * @param {T} object - The object to iterate over.
 * @param {(value: T[keyof T], key: keyof T, object: T) => K} getNewKey - The function invoked per own enumerable property.
 * @returns {Record<K, T[keyof T]>} - Returns the new mapped object.
 *
 * @example
 * // Example usage:
 * const obj = { a: 1, b: 2 };
 * const result = mapKeys(obj, (value, key) => key + value);
 * console.log(result); // { a1: 1, b2: 2 }
 */
declare function mapKeys<T extends Record<PropertyKey, any>, K extends PropertyKey>(object: T, getNewKey: (value: T[keyof T], key: keyof T, object: T) => K): Record<K, T[keyof T]>;

export { mapKeys };
