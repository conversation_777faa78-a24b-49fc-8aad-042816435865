(()=>{var e={};e.id=786,e.ids=[786],e.modules={95:e=>{"use strict";e.exports=Math.pow},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},985:(e,a,t)=>{var n=t(8530),i=t(8321),o=t(1531);e.exports=function(e,a,t){for(var s=i(e);s.index<(s.keyedList||e).length;)n(e,a,s,function(e,a){return e?void t(e,a):0===Object.keys(s.jobs).length?void t(null,s.results):void 0}),s.index++;return o.bind(s,t)}},1118:(e,a,t)=>{var n;e.exports=function(){if(!n){try{n=t(8794)("follow-redirects")}catch(e){}"function"!=typeof n&&(n=function(){})}n.apply(null,arguments)}},1328:e=>{"use strict";e.exports=EvalError},1330:(e,a,t)=>{"use strict";var n,i=t(8363),o=t(4332),s=t(1328),r=t(4513),c=t(1441),p=t(2257),l=t(8486),u=t(8022),d=t(3489),m=t(9703),f=t(3495),x=t(6961),h=t(95),v=t(4957),b=t(1700),g=Function,y=function(e){try{return g('"use strict"; return ('+e+").constructor;")()}catch(e){}},w=t(4198),k=t(3922),j=function(){throw new l},E=w?function(){try{return arguments.callee,j}catch(e){try{return w(arguments,"callee").get}catch(e){return j}}}():j,S=t(6456)(),R=t(5907),_=t(2595),O=t(2545),T=t(3377),A=t(5149),C={},F="undefined"!=typeof Uint8Array&&R?R(Uint8Array):n,D={__proto__:null,"%AggregateError%":"undefined"==typeof AggregateError?n:AggregateError,"%Array%":Array,"%ArrayBuffer%":"undefined"==typeof ArrayBuffer?n:ArrayBuffer,"%ArrayIteratorPrototype%":S&&R?R([][Symbol.iterator]()):n,"%AsyncFromSyncIteratorPrototype%":n,"%AsyncFunction%":C,"%AsyncGenerator%":C,"%AsyncGeneratorFunction%":C,"%AsyncIteratorPrototype%":C,"%Atomics%":"undefined"==typeof Atomics?n:Atomics,"%BigInt%":"undefined"==typeof BigInt?n:BigInt,"%BigInt64Array%":"undefined"==typeof BigInt64Array?n:BigInt64Array,"%BigUint64Array%":"undefined"==typeof BigUint64Array?n:BigUint64Array,"%Boolean%":Boolean,"%DataView%":"undefined"==typeof DataView?n:DataView,"%Date%":Date,"%decodeURI%":decodeURI,"%decodeURIComponent%":decodeURIComponent,"%encodeURI%":encodeURI,"%encodeURIComponent%":encodeURIComponent,"%Error%":o,"%eval%":eval,"%EvalError%":s,"%Float16Array%":"undefined"==typeof Float16Array?n:Float16Array,"%Float32Array%":"undefined"==typeof Float32Array?n:Float32Array,"%Float64Array%":"undefined"==typeof Float64Array?n:Float64Array,"%FinalizationRegistry%":"undefined"==typeof FinalizationRegistry?n:FinalizationRegistry,"%Function%":g,"%GeneratorFunction%":C,"%Int8Array%":"undefined"==typeof Int8Array?n:Int8Array,"%Int16Array%":"undefined"==typeof Int16Array?n:Int16Array,"%Int32Array%":"undefined"==typeof Int32Array?n:Int32Array,"%isFinite%":isFinite,"%isNaN%":isNaN,"%IteratorPrototype%":S&&R?R(R([][Symbol.iterator]())):n,"%JSON%":"object"==typeof JSON?JSON:n,"%Map%":"undefined"==typeof Map?n:Map,"%MapIteratorPrototype%":"undefined"!=typeof Map&&S&&R?R(new Map()[Symbol.iterator]()):n,"%Math%":Math,"%Number%":Number,"%Object%":i,"%Object.getOwnPropertyDescriptor%":w,"%parseFloat%":parseFloat,"%parseInt%":parseInt,"%Promise%":"undefined"==typeof Promise?n:Promise,"%Proxy%":"undefined"==typeof Proxy?n:Proxy,"%RangeError%":r,"%ReferenceError%":c,"%Reflect%":"undefined"==typeof Reflect?n:Reflect,"%RegExp%":RegExp,"%Set%":"undefined"==typeof Set?n:Set,"%SetIteratorPrototype%":"undefined"!=typeof Set&&S&&R?R(new Set()[Symbol.iterator]()):n,"%SharedArrayBuffer%":"undefined"==typeof SharedArrayBuffer?n:SharedArrayBuffer,"%String%":String,"%StringIteratorPrototype%":S&&R?R(""[Symbol.iterator]()):n,"%Symbol%":S?Symbol:n,"%SyntaxError%":p,"%ThrowTypeError%":E,"%TypedArray%":F,"%TypeError%":l,"%Uint8Array%":"undefined"==typeof Uint8Array?n:Uint8Array,"%Uint8ClampedArray%":"undefined"==typeof Uint8ClampedArray?n:Uint8ClampedArray,"%Uint16Array%":"undefined"==typeof Uint16Array?n:Uint16Array,"%Uint32Array%":"undefined"==typeof Uint32Array?n:Uint32Array,"%URIError%":u,"%WeakMap%":"undefined"==typeof WeakMap?n:WeakMap,"%WeakRef%":"undefined"==typeof WeakRef?n:WeakRef,"%WeakSet%":"undefined"==typeof WeakSet?n:WeakSet,"%Function.prototype.call%":A,"%Function.prototype.apply%":T,"%Object.defineProperty%":k,"%Object.getPrototypeOf%":_,"%Math.abs%":d,"%Math.floor%":m,"%Math.max%":f,"%Math.min%":x,"%Math.pow%":h,"%Math.round%":v,"%Math.sign%":b,"%Reflect.getPrototypeOf%":O};if(R)try{null.error}catch(e){var P=R(R(e));D["%Error.prototype%"]=P}var N=function e(a){var t;if("%AsyncFunction%"===a)t=y("async function () {}");else if("%GeneratorFunction%"===a)t=y("function* () {}");else if("%AsyncGeneratorFunction%"===a)t=y("async function* () {}");else if("%AsyncGenerator%"===a){var n=e("%AsyncGeneratorFunction%");n&&(t=n.prototype)}else if("%AsyncIteratorPrototype%"===a){var i=e("%AsyncGenerator%");i&&R&&(t=R(i.prototype))}return D[a]=t,t},z={__proto__:null,"%ArrayBufferPrototype%":["ArrayBuffer","prototype"],"%ArrayPrototype%":["Array","prototype"],"%ArrayProto_entries%":["Array","prototype","entries"],"%ArrayProto_forEach%":["Array","prototype","forEach"],"%ArrayProto_keys%":["Array","prototype","keys"],"%ArrayProto_values%":["Array","prototype","values"],"%AsyncFunctionPrototype%":["AsyncFunction","prototype"],"%AsyncGenerator%":["AsyncGeneratorFunction","prototype"],"%AsyncGeneratorPrototype%":["AsyncGeneratorFunction","prototype","prototype"],"%BooleanPrototype%":["Boolean","prototype"],"%DataViewPrototype%":["DataView","prototype"],"%DatePrototype%":["Date","prototype"],"%ErrorPrototype%":["Error","prototype"],"%EvalErrorPrototype%":["EvalError","prototype"],"%Float32ArrayPrototype%":["Float32Array","prototype"],"%Float64ArrayPrototype%":["Float64Array","prototype"],"%FunctionPrototype%":["Function","prototype"],"%Generator%":["GeneratorFunction","prototype"],"%GeneratorPrototype%":["GeneratorFunction","prototype","prototype"],"%Int8ArrayPrototype%":["Int8Array","prototype"],"%Int16ArrayPrototype%":["Int16Array","prototype"],"%Int32ArrayPrototype%":["Int32Array","prototype"],"%JSONParse%":["JSON","parse"],"%JSONStringify%":["JSON","stringify"],"%MapPrototype%":["Map","prototype"],"%NumberPrototype%":["Number","prototype"],"%ObjectPrototype%":["Object","prototype"],"%ObjProto_toString%":["Object","prototype","toString"],"%ObjProto_valueOf%":["Object","prototype","valueOf"],"%PromisePrototype%":["Promise","prototype"],"%PromiseProto_then%":["Promise","prototype","then"],"%Promise_all%":["Promise","all"],"%Promise_reject%":["Promise","reject"],"%Promise_resolve%":["Promise","resolve"],"%RangeErrorPrototype%":["RangeError","prototype"],"%ReferenceErrorPrototype%":["ReferenceError","prototype"],"%RegExpPrototype%":["RegExp","prototype"],"%SetPrototype%":["Set","prototype"],"%SharedArrayBufferPrototype%":["SharedArrayBuffer","prototype"],"%StringPrototype%":["String","prototype"],"%SymbolPrototype%":["Symbol","prototype"],"%SyntaxErrorPrototype%":["SyntaxError","prototype"],"%TypedArrayPrototype%":["TypedArray","prototype"],"%TypeErrorPrototype%":["TypeError","prototype"],"%Uint8ArrayPrototype%":["Uint8Array","prototype"],"%Uint8ClampedArrayPrototype%":["Uint8ClampedArray","prototype"],"%Uint16ArrayPrototype%":["Uint16Array","prototype"],"%Uint32ArrayPrototype%":["Uint32Array","prototype"],"%URIErrorPrototype%":["URIError","prototype"],"%WeakMapPrototype%":["WeakMap","prototype"],"%WeakSetPrototype%":["WeakSet","prototype"]},L=t(6164),M=t(7284),B=L.call(A,Array.prototype.concat),q=L.call(T,Array.prototype.splice),U=L.call(A,String.prototype.replace),I=L.call(A,String.prototype.slice),H=L.call(A,RegExp.prototype.exec),W=/[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g,$=/\\(\\)?/g,G=function(e){var a=I(e,0,1),t=I(e,-1);if("%"===a&&"%"!==t)throw new p("invalid intrinsic syntax, expected closing `%`");if("%"===t&&"%"!==a)throw new p("invalid intrinsic syntax, expected opening `%`");var n=[];return U(e,W,function(e,a,t,i){n[n.length]=t?U(i,$,"$1"):a||e}),n},V=function(e,a){var t,n=e;if(M(z,n)&&(n="%"+(t=z[n])[0]+"%"),M(D,n)){var i=D[n];if(i===C&&(i=N(n)),void 0===i&&!a)throw new l("intrinsic "+e+" exists, but is not available. Please file an issue!");return{alias:t,name:n,value:i}}throw new p("intrinsic "+e+" does not exist!")};e.exports=function(e,a){if("string"!=typeof e||0===e.length)throw new l("intrinsic name must be a non-empty string");if(arguments.length>1&&"boolean"!=typeof a)throw new l('"allowMissing" argument must be a boolean');if(null===H(/^%?[^%]*%?$/,e))throw new p("`%` may not be present anywhere but at the beginning and end of the intrinsic name");var t=G(e),n=t.length>0?t[0]:"",i=V("%"+n+"%",a),o=i.name,s=i.value,r=!1,c=i.alias;c&&(n=c[0],q(t,B([0,1],c)));for(var u=1,d=!0;u<t.length;u+=1){var m=t[u],f=I(m,0,1),x=I(m,-1);if(('"'===f||"'"===f||"`"===f||'"'===x||"'"===x||"`"===x)&&f!==x)throw new p("property names with quotes must have matching quotes");if("constructor"!==m&&d||(r=!0),n+="."+m,M(D,o="%"+n+"%"))s=D[o];else if(null!=s){if(!(m in s)){if(!a)throw new l("base intrinsic for "+e+" exists, but the property is not available.");return}if(w&&u+1>=t.length){var h=w(s,m);s=(d=!!h)&&"get"in h&&!("originalValue"in h.get)?h.get:s[m]}else d=M(s,m),s=s[m];d&&!r&&(D[o]=s)}}return s}},1441:e=>{"use strict";e.exports=ReferenceError},1531:(e,a,t)=>{var n=t(8790),i=t(8492);e.exports=function(e){Object.keys(this.jobs).length&&(this.index=this.size,n(this),i(e)(null,this.results))}},1630:e=>{"use strict";e.exports=require("http")},1700:(e,a,t)=>{"use strict";var n=t(5580);e.exports=function(e){return n(e)||0===e?e:e<0?-1:1}},1820:e=>{"use strict";e.exports=require("os")},2044:(e,a,t)=>{"use strict";Object.defineProperty(a,"__esModule",{value:!0});var n=function(e){return e&&"object"==typeof e&&"default"in e?e.default:e}(t(5511));a.createDigest=(e,a,t)=>n.createHmac(e,Buffer.from(a,"hex")).update(Buffer.from(t,"hex")).digest().toString("hex"),a.createRandomBytes=(e,a)=>n.randomBytes(e).toString(a)},2257:e=>{"use strict";e.exports=SyntaxError},2297:(e,a,t)=>{e.exports=function(e){function a(e){let t,i,o,s=null;function r(...e){if(!r.enabled)return;let n=Number(new Date);r.diff=n-(t||n),r.prev=t,r.curr=n,t=n,e[0]=a.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let i=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,(t,n)=>{if("%%"===t)return"%";i++;let o=a.formatters[n];if("function"==typeof o){let a=e[i];t=o.call(r,a),e.splice(i,1),i--}return t}),a.formatArgs.call(r,e),(r.log||a.log).apply(r,e)}return r.namespace=e,r.useColors=a.useColors(),r.color=a.selectColor(e),r.extend=n,r.destroy=a.destroy,Object.defineProperty(r,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==s?s:(i!==a.namespaces&&(i=a.namespaces,o=a.enabled(e)),o),set:e=>{s=e}}),"function"==typeof a.init&&a.init(r),r}function n(e,t){let n=a(this.namespace+(void 0===t?":":t)+e);return n.log=this.log,n}function i(e,a){let t=0,n=0,i=-1,o=0;for(;t<e.length;)if(n<a.length&&(a[n]===e[t]||"*"===a[n]))"*"===a[n]?(i=n,o=t):t++,n++;else{if(-1===i)return!1;n=i+1,t=++o}for(;n<a.length&&"*"===a[n];)n++;return n===a.length}return a.debug=a,a.default=a,a.coerce=function(e){return e instanceof Error?e.stack||e.message:e},a.disable=function(){let e=[...a.names,...a.skips.map(e=>"-"+e)].join(",");return a.enable(""),e},a.enable=function(e){for(let t of(a.save(e),a.namespaces=e,a.names=[],a.skips=[],("string"==typeof e?e:"").trim().replace(/\s+/g,",").split(",").filter(Boolean)))"-"===t[0]?a.skips.push(t.slice(1)):a.names.push(t)},a.enabled=function(e){for(let t of a.skips)if(i(e,t))return!1;for(let t of a.names)if(i(e,t))return!0;return!1},a.humanize=t(4072),a.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach(t=>{a[t]=e[t]}),a.names=[],a.skips=[],a.formatters={},a.selectColor=function(e){let t=0;for(let a=0;a<e.length;a++)t=(t<<5)-t+e.charCodeAt(a)|0;return a.colors[Math.abs(t)%a.colors.length]},a.enable(a.load()),a}},2412:e=>{"use strict";e.exports=require("assert")},2545:e=>{"use strict";e.exports="undefined"!=typeof Reflect&&Reflect.getPrototypeOf||null},2590:e=>{"use strict";e.exports=function(){if("function"!=typeof Symbol||"function"!=typeof Object.getOwnPropertySymbols)return!1;if("symbol"==typeof Symbol.iterator)return!0;var e={},a=Symbol("test"),t=Object(a);if("string"==typeof a||"[object Symbol]"!==Object.prototype.toString.call(a)||"[object Symbol]"!==Object.prototype.toString.call(t))return!1;for(var n in e[a]=42,e)return!1;if("function"==typeof Object.keys&&0!==Object.keys(e).length||"function"==typeof Object.getOwnPropertyNames&&0!==Object.getOwnPropertyNames(e).length)return!1;var i=Object.getOwnPropertySymbols(e);if(1!==i.length||i[0]!==a||!Object.prototype.propertyIsEnumerable.call(e,a))return!1;if("function"==typeof Object.getOwnPropertyDescriptor){var o=Object.getOwnPropertyDescriptor(e,a);if(42!==o.value||!0!==o.enumerable)return!1}return!0}},2595:(e,a,t)=>{"use strict";e.exports=t(8363).getPrototypeOf||null},3030:(e,a,t)=>{"use strict";var n=t(6164),i=t(8486),o=t(5149),s=t(8803);e.exports=function(e){if(e.length<1||"function"!=typeof e[0])throw new i("a function is required");return s(n,o,e)}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3377:e=>{"use strict";e.exports=Function.prototype.apply},3394:e=>{"use strict";e.exports=JSON.parse('{"application/1d-interleaved-parityfec":{"source":"iana"},"application/3gpdash-qoe-report+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/3gpp-ims+xml":{"source":"iana","compressible":true},"application/3gpphal+json":{"source":"iana","compressible":true},"application/3gpphalforms+json":{"source":"iana","compressible":true},"application/a2l":{"source":"iana"},"application/ace+cbor":{"source":"iana"},"application/activemessage":{"source":"iana"},"application/activity+json":{"source":"iana","compressible":true},"application/alto-costmap+json":{"source":"iana","compressible":true},"application/alto-costmapfilter+json":{"source":"iana","compressible":true},"application/alto-directory+json":{"source":"iana","compressible":true},"application/alto-endpointcost+json":{"source":"iana","compressible":true},"application/alto-endpointcostparams+json":{"source":"iana","compressible":true},"application/alto-endpointprop+json":{"source":"iana","compressible":true},"application/alto-endpointpropparams+json":{"source":"iana","compressible":true},"application/alto-error+json":{"source":"iana","compressible":true},"application/alto-networkmap+json":{"source":"iana","compressible":true},"application/alto-networkmapfilter+json":{"source":"iana","compressible":true},"application/alto-updatestreamcontrol+json":{"source":"iana","compressible":true},"application/alto-updatestreamparams+json":{"source":"iana","compressible":true},"application/aml":{"source":"iana"},"application/andrew-inset":{"source":"iana","extensions":["ez"]},"application/applefile":{"source":"iana"},"application/applixware":{"source":"apache","extensions":["aw"]},"application/at+jwt":{"source":"iana"},"application/atf":{"source":"iana"},"application/atfx":{"source":"iana"},"application/atom+xml":{"source":"iana","compressible":true,"extensions":["atom"]},"application/atomcat+xml":{"source":"iana","compressible":true,"extensions":["atomcat"]},"application/atomdeleted+xml":{"source":"iana","compressible":true,"extensions":["atomdeleted"]},"application/atomicmail":{"source":"iana"},"application/atomsvc+xml":{"source":"iana","compressible":true,"extensions":["atomsvc"]},"application/atsc-dwd+xml":{"source":"iana","compressible":true,"extensions":["dwd"]},"application/atsc-dynamic-event-message":{"source":"iana"},"application/atsc-held+xml":{"source":"iana","compressible":true,"extensions":["held"]},"application/atsc-rdt+json":{"source":"iana","compressible":true},"application/atsc-rsat+xml":{"source":"iana","compressible":true,"extensions":["rsat"]},"application/atxml":{"source":"iana"},"application/auth-policy+xml":{"source":"iana","compressible":true},"application/bacnet-xdd+zip":{"source":"iana","compressible":false},"application/batch-smtp":{"source":"iana"},"application/bdoc":{"compressible":false,"extensions":["bdoc"]},"application/beep+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/calendar+json":{"source":"iana","compressible":true},"application/calendar+xml":{"source":"iana","compressible":true,"extensions":["xcs"]},"application/call-completion":{"source":"iana"},"application/cals-1840":{"source":"iana"},"application/captive+json":{"source":"iana","compressible":true},"application/cbor":{"source":"iana"},"application/cbor-seq":{"source":"iana"},"application/cccex":{"source":"iana"},"application/ccmp+xml":{"source":"iana","compressible":true},"application/ccxml+xml":{"source":"iana","compressible":true,"extensions":["ccxml"]},"application/cdfx+xml":{"source":"iana","compressible":true,"extensions":["cdfx"]},"application/cdmi-capability":{"source":"iana","extensions":["cdmia"]},"application/cdmi-container":{"source":"iana","extensions":["cdmic"]},"application/cdmi-domain":{"source":"iana","extensions":["cdmid"]},"application/cdmi-object":{"source":"iana","extensions":["cdmio"]},"application/cdmi-queue":{"source":"iana","extensions":["cdmiq"]},"application/cdni":{"source":"iana"},"application/cea":{"source":"iana"},"application/cea-2018+xml":{"source":"iana","compressible":true},"application/cellml+xml":{"source":"iana","compressible":true},"application/cfw":{"source":"iana"},"application/city+json":{"source":"iana","compressible":true},"application/clr":{"source":"iana"},"application/clue+xml":{"source":"iana","compressible":true},"application/clue_info+xml":{"source":"iana","compressible":true},"application/cms":{"source":"iana"},"application/cnrp+xml":{"source":"iana","compressible":true},"application/coap-group+json":{"source":"iana","compressible":true},"application/coap-payload":{"source":"iana"},"application/commonground":{"source":"iana"},"application/conference-info+xml":{"source":"iana","compressible":true},"application/cose":{"source":"iana"},"application/cose-key":{"source":"iana"},"application/cose-key-set":{"source":"iana"},"application/cpl+xml":{"source":"iana","compressible":true,"extensions":["cpl"]},"application/csrattrs":{"source":"iana"},"application/csta+xml":{"source":"iana","compressible":true},"application/cstadata+xml":{"source":"iana","compressible":true},"application/csvm+json":{"source":"iana","compressible":true},"application/cu-seeme":{"source":"apache","extensions":["cu"]},"application/cwt":{"source":"iana"},"application/cybercash":{"source":"iana"},"application/dart":{"compressible":true},"application/dash+xml":{"source":"iana","compressible":true,"extensions":["mpd"]},"application/dash-patch+xml":{"source":"iana","compressible":true,"extensions":["mpp"]},"application/dashdelta":{"source":"iana"},"application/davmount+xml":{"source":"iana","compressible":true,"extensions":["davmount"]},"application/dca-rft":{"source":"iana"},"application/dcd":{"source":"iana"},"application/dec-dx":{"source":"iana"},"application/dialog-info+xml":{"source":"iana","compressible":true},"application/dicom":{"source":"iana"},"application/dicom+json":{"source":"iana","compressible":true},"application/dicom+xml":{"source":"iana","compressible":true},"application/dii":{"source":"iana"},"application/dit":{"source":"iana"},"application/dns":{"source":"iana"},"application/dns+json":{"source":"iana","compressible":true},"application/dns-message":{"source":"iana"},"application/docbook+xml":{"source":"apache","compressible":true,"extensions":["dbk"]},"application/dots+cbor":{"source":"iana"},"application/dskpp+xml":{"source":"iana","compressible":true},"application/dssc+der":{"source":"iana","extensions":["dssc"]},"application/dssc+xml":{"source":"iana","compressible":true,"extensions":["xdssc"]},"application/dvcs":{"source":"iana"},"application/ecmascript":{"source":"iana","compressible":true,"extensions":["es","ecma"]},"application/edi-consent":{"source":"iana"},"application/edi-x12":{"source":"iana","compressible":false},"application/edifact":{"source":"iana","compressible":false},"application/efi":{"source":"iana"},"application/elm+json":{"source":"iana","charset":"UTF-8","compressible":true},"application/elm+xml":{"source":"iana","compressible":true},"application/emergencycalldata.cap+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/emergencycalldata.comment+xml":{"source":"iana","compressible":true},"application/emergencycalldata.control+xml":{"source":"iana","compressible":true},"application/emergencycalldata.deviceinfo+xml":{"source":"iana","compressible":true},"application/emergencycalldata.ecall.msd":{"source":"iana"},"application/emergencycalldata.providerinfo+xml":{"source":"iana","compressible":true},"application/emergencycalldata.serviceinfo+xml":{"source":"iana","compressible":true},"application/emergencycalldata.subscriberinfo+xml":{"source":"iana","compressible":true},"application/emergencycalldata.veds+xml":{"source":"iana","compressible":true},"application/emma+xml":{"source":"iana","compressible":true,"extensions":["emma"]},"application/emotionml+xml":{"source":"iana","compressible":true,"extensions":["emotionml"]},"application/encaprtp":{"source":"iana"},"application/epp+xml":{"source":"iana","compressible":true},"application/epub+zip":{"source":"iana","compressible":false,"extensions":["epub"]},"application/eshop":{"source":"iana"},"application/exi":{"source":"iana","extensions":["exi"]},"application/expect-ct-report+json":{"source":"iana","compressible":true},"application/express":{"source":"iana","extensions":["exp"]},"application/fastinfoset":{"source":"iana"},"application/fastsoap":{"source":"iana"},"application/fdt+xml":{"source":"iana","compressible":true,"extensions":["fdt"]},"application/fhir+json":{"source":"iana","charset":"UTF-8","compressible":true},"application/fhir+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/fido.trusted-apps+json":{"compressible":true},"application/fits":{"source":"iana"},"application/flexfec":{"source":"iana"},"application/font-sfnt":{"source":"iana"},"application/font-tdpfr":{"source":"iana","extensions":["pfr"]},"application/font-woff":{"source":"iana","compressible":false},"application/framework-attributes+xml":{"source":"iana","compressible":true},"application/geo+json":{"source":"iana","compressible":true,"extensions":["geojson"]},"application/geo+json-seq":{"source":"iana"},"application/geopackage+sqlite3":{"source":"iana"},"application/geoxacml+xml":{"source":"iana","compressible":true},"application/gltf-buffer":{"source":"iana"},"application/gml+xml":{"source":"iana","compressible":true,"extensions":["gml"]},"application/gpx+xml":{"source":"apache","compressible":true,"extensions":["gpx"]},"application/gxf":{"source":"apache","extensions":["gxf"]},"application/gzip":{"source":"iana","compressible":false,"extensions":["gz"]},"application/h224":{"source":"iana"},"application/held+xml":{"source":"iana","compressible":true},"application/hjson":{"extensions":["hjson"]},"application/http":{"source":"iana"},"application/hyperstudio":{"source":"iana","extensions":["stk"]},"application/ibe-key-request+xml":{"source":"iana","compressible":true},"application/ibe-pkg-reply+xml":{"source":"iana","compressible":true},"application/ibe-pp-data":{"source":"iana"},"application/iges":{"source":"iana"},"application/im-iscomposing+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/index":{"source":"iana"},"application/index.cmd":{"source":"iana"},"application/index.obj":{"source":"iana"},"application/index.response":{"source":"iana"},"application/index.vnd":{"source":"iana"},"application/inkml+xml":{"source":"iana","compressible":true,"extensions":["ink","inkml"]},"application/iotp":{"source":"iana"},"application/ipfix":{"source":"iana","extensions":["ipfix"]},"application/ipp":{"source":"iana"},"application/isup":{"source":"iana"},"application/its+xml":{"source":"iana","compressible":true,"extensions":["its"]},"application/java-archive":{"source":"apache","compressible":false,"extensions":["jar","war","ear"]},"application/java-serialized-object":{"source":"apache","compressible":false,"extensions":["ser"]},"application/java-vm":{"source":"apache","compressible":false,"extensions":["class"]},"application/javascript":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["js","mjs"]},"application/jf2feed+json":{"source":"iana","compressible":true},"application/jose":{"source":"iana"},"application/jose+json":{"source":"iana","compressible":true},"application/jrd+json":{"source":"iana","compressible":true},"application/jscalendar+json":{"source":"iana","compressible":true},"application/json":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["json","map"]},"application/json-patch+json":{"source":"iana","compressible":true},"application/json-seq":{"source":"iana"},"application/json5":{"extensions":["json5"]},"application/jsonml+json":{"source":"apache","compressible":true,"extensions":["jsonml"]},"application/jwk+json":{"source":"iana","compressible":true},"application/jwk-set+json":{"source":"iana","compressible":true},"application/jwt":{"source":"iana"},"application/kpml-request+xml":{"source":"iana","compressible":true},"application/kpml-response+xml":{"source":"iana","compressible":true},"application/ld+json":{"source":"iana","compressible":true,"extensions":["jsonld"]},"application/lgr+xml":{"source":"iana","compressible":true,"extensions":["lgr"]},"application/link-format":{"source":"iana"},"application/load-control+xml":{"source":"iana","compressible":true},"application/lost+xml":{"source":"iana","compressible":true,"extensions":["lostxml"]},"application/lostsync+xml":{"source":"iana","compressible":true},"application/lpf+zip":{"source":"iana","compressible":false},"application/lxf":{"source":"iana"},"application/mac-binhex40":{"source":"iana","extensions":["hqx"]},"application/mac-compactpro":{"source":"apache","extensions":["cpt"]},"application/macwriteii":{"source":"iana"},"application/mads+xml":{"source":"iana","compressible":true,"extensions":["mads"]},"application/manifest+json":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["webmanifest"]},"application/marc":{"source":"iana","extensions":["mrc"]},"application/marcxml+xml":{"source":"iana","compressible":true,"extensions":["mrcx"]},"application/mathematica":{"source":"iana","extensions":["ma","nb","mb"]},"application/mathml+xml":{"source":"iana","compressible":true,"extensions":["mathml"]},"application/mathml-content+xml":{"source":"iana","compressible":true},"application/mathml-presentation+xml":{"source":"iana","compressible":true},"application/mbms-associated-procedure-description+xml":{"source":"iana","compressible":true},"application/mbms-deregister+xml":{"source":"iana","compressible":true},"application/mbms-envelope+xml":{"source":"iana","compressible":true},"application/mbms-msk+xml":{"source":"iana","compressible":true},"application/mbms-msk-response+xml":{"source":"iana","compressible":true},"application/mbms-protection-description+xml":{"source":"iana","compressible":true},"application/mbms-reception-report+xml":{"source":"iana","compressible":true},"application/mbms-register+xml":{"source":"iana","compressible":true},"application/mbms-register-response+xml":{"source":"iana","compressible":true},"application/mbms-schedule+xml":{"source":"iana","compressible":true},"application/mbms-user-service-description+xml":{"source":"iana","compressible":true},"application/mbox":{"source":"iana","extensions":["mbox"]},"application/media-policy-dataset+xml":{"source":"iana","compressible":true,"extensions":["mpf"]},"application/media_control+xml":{"source":"iana","compressible":true},"application/mediaservercontrol+xml":{"source":"iana","compressible":true,"extensions":["mscml"]},"application/merge-patch+json":{"source":"iana","compressible":true},"application/metalink+xml":{"source":"apache","compressible":true,"extensions":["metalink"]},"application/metalink4+xml":{"source":"iana","compressible":true,"extensions":["meta4"]},"application/mets+xml":{"source":"iana","compressible":true,"extensions":["mets"]},"application/mf4":{"source":"iana"},"application/mikey":{"source":"iana"},"application/mipc":{"source":"iana"},"application/missing-blocks+cbor-seq":{"source":"iana"},"application/mmt-aei+xml":{"source":"iana","compressible":true,"extensions":["maei"]},"application/mmt-usd+xml":{"source":"iana","compressible":true,"extensions":["musd"]},"application/mods+xml":{"source":"iana","compressible":true,"extensions":["mods"]},"application/moss-keys":{"source":"iana"},"application/moss-signature":{"source":"iana"},"application/mosskey-data":{"source":"iana"},"application/mosskey-request":{"source":"iana"},"application/mp21":{"source":"iana","extensions":["m21","mp21"]},"application/mp4":{"source":"iana","extensions":["mp4s","m4p"]},"application/mpeg4-generic":{"source":"iana"},"application/mpeg4-iod":{"source":"iana"},"application/mpeg4-iod-xmt":{"source":"iana"},"application/mrb-consumer+xml":{"source":"iana","compressible":true},"application/mrb-publish+xml":{"source":"iana","compressible":true},"application/msc-ivr+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/msc-mixer+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/msword":{"source":"iana","compressible":false,"extensions":["doc","dot"]},"application/mud+json":{"source":"iana","compressible":true},"application/multipart-core":{"source":"iana"},"application/mxf":{"source":"iana","extensions":["mxf"]},"application/n-quads":{"source":"iana","extensions":["nq"]},"application/n-triples":{"source":"iana","extensions":["nt"]},"application/nasdata":{"source":"iana"},"application/news-checkgroups":{"source":"iana","charset":"US-ASCII"},"application/news-groupinfo":{"source":"iana","charset":"US-ASCII"},"application/news-transmission":{"source":"iana"},"application/nlsml+xml":{"source":"iana","compressible":true},"application/node":{"source":"iana","extensions":["cjs"]},"application/nss":{"source":"iana"},"application/oauth-authz-req+jwt":{"source":"iana"},"application/oblivious-dns-message":{"source":"iana"},"application/ocsp-request":{"source":"iana"},"application/ocsp-response":{"source":"iana"},"application/octet-stream":{"source":"iana","compressible":false,"extensions":["bin","dms","lrf","mar","so","dist","distz","pkg","bpk","dump","elc","deploy","exe","dll","deb","dmg","iso","img","msi","msp","msm","buffer"]},"application/oda":{"source":"iana","extensions":["oda"]},"application/odm+xml":{"source":"iana","compressible":true},"application/odx":{"source":"iana"},"application/oebps-package+xml":{"source":"iana","compressible":true,"extensions":["opf"]},"application/ogg":{"source":"iana","compressible":false,"extensions":["ogx"]},"application/omdoc+xml":{"source":"apache","compressible":true,"extensions":["omdoc"]},"application/onenote":{"source":"apache","extensions":["onetoc","onetoc2","onetmp","onepkg"]},"application/opc-nodeset+xml":{"source":"iana","compressible":true},"application/oscore":{"source":"iana"},"application/oxps":{"source":"iana","extensions":["oxps"]},"application/p21":{"source":"iana"},"application/p21+zip":{"source":"iana","compressible":false},"application/p2p-overlay+xml":{"source":"iana","compressible":true,"extensions":["relo"]},"application/parityfec":{"source":"iana"},"application/passport":{"source":"iana"},"application/patch-ops-error+xml":{"source":"iana","compressible":true,"extensions":["xer"]},"application/pdf":{"source":"iana","compressible":false,"extensions":["pdf"]},"application/pdx":{"source":"iana"},"application/pem-certificate-chain":{"source":"iana"},"application/pgp-encrypted":{"source":"iana","compressible":false,"extensions":["pgp"]},"application/pgp-keys":{"source":"iana","extensions":["asc"]},"application/pgp-signature":{"source":"iana","extensions":["asc","sig"]},"application/pics-rules":{"source":"apache","extensions":["prf"]},"application/pidf+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/pidf-diff+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/pkcs10":{"source":"iana","extensions":["p10"]},"application/pkcs12":{"source":"iana"},"application/pkcs7-mime":{"source":"iana","extensions":["p7m","p7c"]},"application/pkcs7-signature":{"source":"iana","extensions":["p7s"]},"application/pkcs8":{"source":"iana","extensions":["p8"]},"application/pkcs8-encrypted":{"source":"iana"},"application/pkix-attr-cert":{"source":"iana","extensions":["ac"]},"application/pkix-cert":{"source":"iana","extensions":["cer"]},"application/pkix-crl":{"source":"iana","extensions":["crl"]},"application/pkix-pkipath":{"source":"iana","extensions":["pkipath"]},"application/pkixcmp":{"source":"iana","extensions":["pki"]},"application/pls+xml":{"source":"iana","compressible":true,"extensions":["pls"]},"application/poc-settings+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/postscript":{"source":"iana","compressible":true,"extensions":["ai","eps","ps"]},"application/ppsp-tracker+json":{"source":"iana","compressible":true},"application/problem+json":{"source":"iana","compressible":true},"application/problem+xml":{"source":"iana","compressible":true},"application/provenance+xml":{"source":"iana","compressible":true,"extensions":["provx"]},"application/prs.alvestrand.titrax-sheet":{"source":"iana"},"application/prs.cww":{"source":"iana","extensions":["cww"]},"application/prs.cyn":{"source":"iana","charset":"7-BIT"},"application/prs.hpub+zip":{"source":"iana","compressible":false},"application/prs.nprend":{"source":"iana"},"application/prs.plucker":{"source":"iana"},"application/prs.rdf-xml-crypt":{"source":"iana"},"application/prs.xsf+xml":{"source":"iana","compressible":true},"application/pskc+xml":{"source":"iana","compressible":true,"extensions":["pskcxml"]},"application/pvd+json":{"source":"iana","compressible":true},"application/qsig":{"source":"iana"},"application/raml+yaml":{"compressible":true,"extensions":["raml"]},"application/raptorfec":{"source":"iana"},"application/rdap+json":{"source":"iana","compressible":true},"application/rdf+xml":{"source":"iana","compressible":true,"extensions":["rdf","owl"]},"application/reginfo+xml":{"source":"iana","compressible":true,"extensions":["rif"]},"application/relax-ng-compact-syntax":{"source":"iana","extensions":["rnc"]},"application/remote-printing":{"source":"iana"},"application/reputon+json":{"source":"iana","compressible":true},"application/resource-lists+xml":{"source":"iana","compressible":true,"extensions":["rl"]},"application/resource-lists-diff+xml":{"source":"iana","compressible":true,"extensions":["rld"]},"application/rfc+xml":{"source":"iana","compressible":true},"application/riscos":{"source":"iana"},"application/rlmi+xml":{"source":"iana","compressible":true},"application/rls-services+xml":{"source":"iana","compressible":true,"extensions":["rs"]},"application/route-apd+xml":{"source":"iana","compressible":true,"extensions":["rapd"]},"application/route-s-tsid+xml":{"source":"iana","compressible":true,"extensions":["sls"]},"application/route-usd+xml":{"source":"iana","compressible":true,"extensions":["rusd"]},"application/rpki-ghostbusters":{"source":"iana","extensions":["gbr"]},"application/rpki-manifest":{"source":"iana","extensions":["mft"]},"application/rpki-publication":{"source":"iana"},"application/rpki-roa":{"source":"iana","extensions":["roa"]},"application/rpki-updown":{"source":"iana"},"application/rsd+xml":{"source":"apache","compressible":true,"extensions":["rsd"]},"application/rss+xml":{"source":"apache","compressible":true,"extensions":["rss"]},"application/rtf":{"source":"iana","compressible":true,"extensions":["rtf"]},"application/rtploopback":{"source":"iana"},"application/rtx":{"source":"iana"},"application/samlassertion+xml":{"source":"iana","compressible":true},"application/samlmetadata+xml":{"source":"iana","compressible":true},"application/sarif+json":{"source":"iana","compressible":true},"application/sarif-external-properties+json":{"source":"iana","compressible":true},"application/sbe":{"source":"iana"},"application/sbml+xml":{"source":"iana","compressible":true,"extensions":["sbml"]},"application/scaip+xml":{"source":"iana","compressible":true},"application/scim+json":{"source":"iana","compressible":true},"application/scvp-cv-request":{"source":"iana","extensions":["scq"]},"application/scvp-cv-response":{"source":"iana","extensions":["scs"]},"application/scvp-vp-request":{"source":"iana","extensions":["spq"]},"application/scvp-vp-response":{"source":"iana","extensions":["spp"]},"application/sdp":{"source":"iana","extensions":["sdp"]},"application/secevent+jwt":{"source":"iana"},"application/senml+cbor":{"source":"iana"},"application/senml+json":{"source":"iana","compressible":true},"application/senml+xml":{"source":"iana","compressible":true,"extensions":["senmlx"]},"application/senml-etch+cbor":{"source":"iana"},"application/senml-etch+json":{"source":"iana","compressible":true},"application/senml-exi":{"source":"iana"},"application/sensml+cbor":{"source":"iana"},"application/sensml+json":{"source":"iana","compressible":true},"application/sensml+xml":{"source":"iana","compressible":true,"extensions":["sensmlx"]},"application/sensml-exi":{"source":"iana"},"application/sep+xml":{"source":"iana","compressible":true},"application/sep-exi":{"source":"iana"},"application/session-info":{"source":"iana"},"application/set-payment":{"source":"iana"},"application/set-payment-initiation":{"source":"iana","extensions":["setpay"]},"application/set-registration":{"source":"iana"},"application/set-registration-initiation":{"source":"iana","extensions":["setreg"]},"application/sgml":{"source":"iana"},"application/sgml-open-catalog":{"source":"iana"},"application/shf+xml":{"source":"iana","compressible":true,"extensions":["shf"]},"application/sieve":{"source":"iana","extensions":["siv","sieve"]},"application/simple-filter+xml":{"source":"iana","compressible":true},"application/simple-message-summary":{"source":"iana"},"application/simplesymbolcontainer":{"source":"iana"},"application/sipc":{"source":"iana"},"application/slate":{"source":"iana"},"application/smil":{"source":"iana"},"application/smil+xml":{"source":"iana","compressible":true,"extensions":["smi","smil"]},"application/smpte336m":{"source":"iana"},"application/soap+fastinfoset":{"source":"iana"},"application/soap+xml":{"source":"iana","compressible":true},"application/sparql-query":{"source":"iana","extensions":["rq"]},"application/sparql-results+xml":{"source":"iana","compressible":true,"extensions":["srx"]},"application/spdx+json":{"source":"iana","compressible":true},"application/spirits-event+xml":{"source":"iana","compressible":true},"application/sql":{"source":"iana"},"application/srgs":{"source":"iana","extensions":["gram"]},"application/srgs+xml":{"source":"iana","compressible":true,"extensions":["grxml"]},"application/sru+xml":{"source":"iana","compressible":true,"extensions":["sru"]},"application/ssdl+xml":{"source":"apache","compressible":true,"extensions":["ssdl"]},"application/ssml+xml":{"source":"iana","compressible":true,"extensions":["ssml"]},"application/stix+json":{"source":"iana","compressible":true},"application/swid+xml":{"source":"iana","compressible":true,"extensions":["swidtag"]},"application/tamp-apex-update":{"source":"iana"},"application/tamp-apex-update-confirm":{"source":"iana"},"application/tamp-community-update":{"source":"iana"},"application/tamp-community-update-confirm":{"source":"iana"},"application/tamp-error":{"source":"iana"},"application/tamp-sequence-adjust":{"source":"iana"},"application/tamp-sequence-adjust-confirm":{"source":"iana"},"application/tamp-status-query":{"source":"iana"},"application/tamp-status-response":{"source":"iana"},"application/tamp-update":{"source":"iana"},"application/tamp-update-confirm":{"source":"iana"},"application/tar":{"compressible":true},"application/taxii+json":{"source":"iana","compressible":true},"application/td+json":{"source":"iana","compressible":true},"application/tei+xml":{"source":"iana","compressible":true,"extensions":["tei","teicorpus"]},"application/tetra_isi":{"source":"iana"},"application/thraud+xml":{"source":"iana","compressible":true,"extensions":["tfi"]},"application/timestamp-query":{"source":"iana"},"application/timestamp-reply":{"source":"iana"},"application/timestamped-data":{"source":"iana","extensions":["tsd"]},"application/tlsrpt+gzip":{"source":"iana"},"application/tlsrpt+json":{"source":"iana","compressible":true},"application/tnauthlist":{"source":"iana"},"application/token-introspection+jwt":{"source":"iana"},"application/toml":{"compressible":true,"extensions":["toml"]},"application/trickle-ice-sdpfrag":{"source":"iana"},"application/trig":{"source":"iana","extensions":["trig"]},"application/ttml+xml":{"source":"iana","compressible":true,"extensions":["ttml"]},"application/tve-trigger":{"source":"iana"},"application/tzif":{"source":"iana"},"application/tzif-leap":{"source":"iana"},"application/ubjson":{"compressible":false,"extensions":["ubj"]},"application/ulpfec":{"source":"iana"},"application/urc-grpsheet+xml":{"source":"iana","compressible":true},"application/urc-ressheet+xml":{"source":"iana","compressible":true,"extensions":["rsheet"]},"application/urc-targetdesc+xml":{"source":"iana","compressible":true,"extensions":["td"]},"application/urc-uisocketdesc+xml":{"source":"iana","compressible":true},"application/vcard+json":{"source":"iana","compressible":true},"application/vcard+xml":{"source":"iana","compressible":true},"application/vemmi":{"source":"iana"},"application/vividence.scriptfile":{"source":"apache"},"application/vnd.1000minds.decision-model+xml":{"source":"iana","compressible":true,"extensions":["1km"]},"application/vnd.3gpp-prose+xml":{"source":"iana","compressible":true},"application/vnd.3gpp-prose-pc3ch+xml":{"source":"iana","compressible":true},"application/vnd.3gpp-v2x-local-service-information":{"source":"iana"},"application/vnd.3gpp.5gnas":{"source":"iana"},"application/vnd.3gpp.access-transfer-events+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.bsf+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.gmop+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.gtpc":{"source":"iana"},"application/vnd.3gpp.interworking-data":{"source":"iana"},"application/vnd.3gpp.lpp":{"source":"iana"},"application/vnd.3gpp.mc-signalling-ear":{"source":"iana"},"application/vnd.3gpp.mcdata-affiliation-command+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcdata-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcdata-payload":{"source":"iana"},"application/vnd.3gpp.mcdata-service-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcdata-signalling":{"source":"iana"},"application/vnd.3gpp.mcdata-ue-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcdata-user-profile+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-affiliation-command+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-floor-request+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-location-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-mbms-usage-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-service-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-signed+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-ue-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-ue-init-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-user-profile+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-affiliation-command+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-affiliation-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-location-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-mbms-usage-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-service-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-transmission-request+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-ue-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-user-profile+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mid-call+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.ngap":{"source":"iana"},"application/vnd.3gpp.pfcp":{"source":"iana"},"application/vnd.3gpp.pic-bw-large":{"source":"iana","extensions":["plb"]},"application/vnd.3gpp.pic-bw-small":{"source":"iana","extensions":["psb"]},"application/vnd.3gpp.pic-bw-var":{"source":"iana","extensions":["pvb"]},"application/vnd.3gpp.s1ap":{"source":"iana"},"application/vnd.3gpp.sms":{"source":"iana"},"application/vnd.3gpp.sms+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.srvcc-ext+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.srvcc-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.state-and-event-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.ussd+xml":{"source":"iana","compressible":true},"application/vnd.3gpp2.bcmcsinfo+xml":{"source":"iana","compressible":true},"application/vnd.3gpp2.sms":{"source":"iana"},"application/vnd.3gpp2.tcap":{"source":"iana","extensions":["tcap"]},"application/vnd.3lightssoftware.imagescal":{"source":"iana"},"application/vnd.3m.post-it-notes":{"source":"iana","extensions":["pwn"]},"application/vnd.accpac.simply.aso":{"source":"iana","extensions":["aso"]},"application/vnd.accpac.simply.imp":{"source":"iana","extensions":["imp"]},"application/vnd.acucobol":{"source":"iana","extensions":["acu"]},"application/vnd.acucorp":{"source":"iana","extensions":["atc","acutc"]},"application/vnd.adobe.air-application-installer-package+zip":{"source":"apache","compressible":false,"extensions":["air"]},"application/vnd.adobe.flash.movie":{"source":"iana"},"application/vnd.adobe.formscentral.fcdt":{"source":"iana","extensions":["fcdt"]},"application/vnd.adobe.fxp":{"source":"iana","extensions":["fxp","fxpl"]},"application/vnd.adobe.partial-upload":{"source":"iana"},"application/vnd.adobe.xdp+xml":{"source":"iana","compressible":true,"extensions":["xdp"]},"application/vnd.adobe.xfdf":{"source":"iana","extensions":["xfdf"]},"application/vnd.aether.imp":{"source":"iana"},"application/vnd.afpc.afplinedata":{"source":"iana"},"application/vnd.afpc.afplinedata-pagedef":{"source":"iana"},"application/vnd.afpc.cmoca-cmresource":{"source":"iana"},"application/vnd.afpc.foca-charset":{"source":"iana"},"application/vnd.afpc.foca-codedfont":{"source":"iana"},"application/vnd.afpc.foca-codepage":{"source":"iana"},"application/vnd.afpc.modca":{"source":"iana"},"application/vnd.afpc.modca-cmtable":{"source":"iana"},"application/vnd.afpc.modca-formdef":{"source":"iana"},"application/vnd.afpc.modca-mediummap":{"source":"iana"},"application/vnd.afpc.modca-objectcontainer":{"source":"iana"},"application/vnd.afpc.modca-overlay":{"source":"iana"},"application/vnd.afpc.modca-pagesegment":{"source":"iana"},"application/vnd.age":{"source":"iana","extensions":["age"]},"application/vnd.ah-barcode":{"source":"iana"},"application/vnd.ahead.space":{"source":"iana","extensions":["ahead"]},"application/vnd.airzip.filesecure.azf":{"source":"iana","extensions":["azf"]},"application/vnd.airzip.filesecure.azs":{"source":"iana","extensions":["azs"]},"application/vnd.amadeus+json":{"source":"iana","compressible":true},"application/vnd.amazon.ebook":{"source":"apache","extensions":["azw"]},"application/vnd.amazon.mobi8-ebook":{"source":"iana"},"application/vnd.americandynamics.acc":{"source":"iana","extensions":["acc"]},"application/vnd.amiga.ami":{"source":"iana","extensions":["ami"]},"application/vnd.amundsen.maze+xml":{"source":"iana","compressible":true},"application/vnd.android.ota":{"source":"iana"},"application/vnd.android.package-archive":{"source":"apache","compressible":false,"extensions":["apk"]},"application/vnd.anki":{"source":"iana"},"application/vnd.anser-web-certificate-issue-initiation":{"source":"iana","extensions":["cii"]},"application/vnd.anser-web-funds-transfer-initiation":{"source":"apache","extensions":["fti"]},"application/vnd.antix.game-component":{"source":"iana","extensions":["atx"]},"application/vnd.apache.arrow.file":{"source":"iana"},"application/vnd.apache.arrow.stream":{"source":"iana"},"application/vnd.apache.thrift.binary":{"source":"iana"},"application/vnd.apache.thrift.compact":{"source":"iana"},"application/vnd.apache.thrift.json":{"source":"iana"},"application/vnd.api+json":{"source":"iana","compressible":true},"application/vnd.aplextor.warrp+json":{"source":"iana","compressible":true},"application/vnd.apothekende.reservation+json":{"source":"iana","compressible":true},"application/vnd.apple.installer+xml":{"source":"iana","compressible":true,"extensions":["mpkg"]},"application/vnd.apple.keynote":{"source":"iana","extensions":["key"]},"application/vnd.apple.mpegurl":{"source":"iana","extensions":["m3u8"]},"application/vnd.apple.numbers":{"source":"iana","extensions":["numbers"]},"application/vnd.apple.pages":{"source":"iana","extensions":["pages"]},"application/vnd.apple.pkpass":{"compressible":false,"extensions":["pkpass"]},"application/vnd.arastra.swi":{"source":"iana"},"application/vnd.aristanetworks.swi":{"source":"iana","extensions":["swi"]},"application/vnd.artisan+json":{"source":"iana","compressible":true},"application/vnd.artsquare":{"source":"iana"},"application/vnd.astraea-software.iota":{"source":"iana","extensions":["iota"]},"application/vnd.audiograph":{"source":"iana","extensions":["aep"]},"application/vnd.autopackage":{"source":"iana"},"application/vnd.avalon+json":{"source":"iana","compressible":true},"application/vnd.avistar+xml":{"source":"iana","compressible":true},"application/vnd.balsamiq.bmml+xml":{"source":"iana","compressible":true,"extensions":["bmml"]},"application/vnd.balsamiq.bmpr":{"source":"iana"},"application/vnd.banana-accounting":{"source":"iana"},"application/vnd.bbf.usp.error":{"source":"iana"},"application/vnd.bbf.usp.msg":{"source":"iana"},"application/vnd.bbf.usp.msg+json":{"source":"iana","compressible":true},"application/vnd.bekitzur-stech+json":{"source":"iana","compressible":true},"application/vnd.bint.med-content":{"source":"iana"},"application/vnd.biopax.rdf+xml":{"source":"iana","compressible":true},"application/vnd.blink-idb-value-wrapper":{"source":"iana"},"application/vnd.blueice.multipass":{"source":"iana","extensions":["mpm"]},"application/vnd.bluetooth.ep.oob":{"source":"iana"},"application/vnd.bluetooth.le.oob":{"source":"iana"},"application/vnd.bmi":{"source":"iana","extensions":["bmi"]},"application/vnd.bpf":{"source":"iana"},"application/vnd.bpf3":{"source":"iana"},"application/vnd.businessobjects":{"source":"iana","extensions":["rep"]},"application/vnd.byu.uapi+json":{"source":"iana","compressible":true},"application/vnd.cab-jscript":{"source":"iana"},"application/vnd.canon-cpdl":{"source":"iana"},"application/vnd.canon-lips":{"source":"iana"},"application/vnd.capasystems-pg+json":{"source":"iana","compressible":true},"application/vnd.cendio.thinlinc.clientconf":{"source":"iana"},"application/vnd.century-systems.tcp_stream":{"source":"iana"},"application/vnd.chemdraw+xml":{"source":"iana","compressible":true,"extensions":["cdxml"]},"application/vnd.chess-pgn":{"source":"iana"},"application/vnd.chipnuts.karaoke-mmd":{"source":"iana","extensions":["mmd"]},"application/vnd.ciedi":{"source":"iana"},"application/vnd.cinderella":{"source":"iana","extensions":["cdy"]},"application/vnd.cirpack.isdn-ext":{"source":"iana"},"application/vnd.citationstyles.style+xml":{"source":"iana","compressible":true,"extensions":["csl"]},"application/vnd.claymore":{"source":"iana","extensions":["cla"]},"application/vnd.cloanto.rp9":{"source":"iana","extensions":["rp9"]},"application/vnd.clonk.c4group":{"source":"iana","extensions":["c4g","c4d","c4f","c4p","c4u"]},"application/vnd.cluetrust.cartomobile-config":{"source":"iana","extensions":["c11amc"]},"application/vnd.cluetrust.cartomobile-config-pkg":{"source":"iana","extensions":["c11amz"]},"application/vnd.coffeescript":{"source":"iana"},"application/vnd.collabio.xodocuments.document":{"source":"iana"},"application/vnd.collabio.xodocuments.document-template":{"source":"iana"},"application/vnd.collabio.xodocuments.presentation":{"source":"iana"},"application/vnd.collabio.xodocuments.presentation-template":{"source":"iana"},"application/vnd.collabio.xodocuments.spreadsheet":{"source":"iana"},"application/vnd.collabio.xodocuments.spreadsheet-template":{"source":"iana"},"application/vnd.collection+json":{"source":"iana","compressible":true},"application/vnd.collection.doc+json":{"source":"iana","compressible":true},"application/vnd.collection.next+json":{"source":"iana","compressible":true},"application/vnd.comicbook+zip":{"source":"iana","compressible":false},"application/vnd.comicbook-rar":{"source":"iana"},"application/vnd.commerce-battelle":{"source":"iana"},"application/vnd.commonspace":{"source":"iana","extensions":["csp"]},"application/vnd.contact.cmsg":{"source":"iana","extensions":["cdbcmsg"]},"application/vnd.coreos.ignition+json":{"source":"iana","compressible":true},"application/vnd.cosmocaller":{"source":"iana","extensions":["cmc"]},"application/vnd.crick.clicker":{"source":"iana","extensions":["clkx"]},"application/vnd.crick.clicker.keyboard":{"source":"iana","extensions":["clkk"]},"application/vnd.crick.clicker.palette":{"source":"iana","extensions":["clkp"]},"application/vnd.crick.clicker.template":{"source":"iana","extensions":["clkt"]},"application/vnd.crick.clicker.wordbank":{"source":"iana","extensions":["clkw"]},"application/vnd.criticaltools.wbs+xml":{"source":"iana","compressible":true,"extensions":["wbs"]},"application/vnd.cryptii.pipe+json":{"source":"iana","compressible":true},"application/vnd.crypto-shade-file":{"source":"iana"},"application/vnd.cryptomator.encrypted":{"source":"iana"},"application/vnd.cryptomator.vault":{"source":"iana"},"application/vnd.ctc-posml":{"source":"iana","extensions":["pml"]},"application/vnd.ctct.ws+xml":{"source":"iana","compressible":true},"application/vnd.cups-pdf":{"source":"iana"},"application/vnd.cups-postscript":{"source":"iana"},"application/vnd.cups-ppd":{"source":"iana","extensions":["ppd"]},"application/vnd.cups-raster":{"source":"iana"},"application/vnd.cups-raw":{"source":"iana"},"application/vnd.curl":{"source":"iana"},"application/vnd.curl.car":{"source":"apache","extensions":["car"]},"application/vnd.curl.pcurl":{"source":"apache","extensions":["pcurl"]},"application/vnd.cyan.dean.root+xml":{"source":"iana","compressible":true},"application/vnd.cybank":{"source":"iana"},"application/vnd.cyclonedx+json":{"source":"iana","compressible":true},"application/vnd.cyclonedx+xml":{"source":"iana","compressible":true},"application/vnd.d2l.coursepackage1p0+zip":{"source":"iana","compressible":false},"application/vnd.d3m-dataset":{"source":"iana"},"application/vnd.d3m-problem":{"source":"iana"},"application/vnd.dart":{"source":"iana","compressible":true,"extensions":["dart"]},"application/vnd.data-vision.rdz":{"source":"iana","extensions":["rdz"]},"application/vnd.datapackage+json":{"source":"iana","compressible":true},"application/vnd.dataresource+json":{"source":"iana","compressible":true},"application/vnd.dbf":{"source":"iana","extensions":["dbf"]},"application/vnd.debian.binary-package":{"source":"iana"},"application/vnd.dece.data":{"source":"iana","extensions":["uvf","uvvf","uvd","uvvd"]},"application/vnd.dece.ttml+xml":{"source":"iana","compressible":true,"extensions":["uvt","uvvt"]},"application/vnd.dece.unspecified":{"source":"iana","extensions":["uvx","uvvx"]},"application/vnd.dece.zip":{"source":"iana","extensions":["uvz","uvvz"]},"application/vnd.denovo.fcselayout-link":{"source":"iana","extensions":["fe_launch"]},"application/vnd.desmume.movie":{"source":"iana"},"application/vnd.dir-bi.plate-dl-nosuffix":{"source":"iana"},"application/vnd.dm.delegation+xml":{"source":"iana","compressible":true},"application/vnd.dna":{"source":"iana","extensions":["dna"]},"application/vnd.document+json":{"source":"iana","compressible":true},"application/vnd.dolby.mlp":{"source":"apache","extensions":["mlp"]},"application/vnd.dolby.mobile.1":{"source":"iana"},"application/vnd.dolby.mobile.2":{"source":"iana"},"application/vnd.doremir.scorecloud-binary-document":{"source":"iana"},"application/vnd.dpgraph":{"source":"iana","extensions":["dpg"]},"application/vnd.dreamfactory":{"source":"iana","extensions":["dfac"]},"application/vnd.drive+json":{"source":"iana","compressible":true},"application/vnd.ds-keypoint":{"source":"apache","extensions":["kpxx"]},"application/vnd.dtg.local":{"source":"iana"},"application/vnd.dtg.local.flash":{"source":"iana"},"application/vnd.dtg.local.html":{"source":"iana"},"application/vnd.dvb.ait":{"source":"iana","extensions":["ait"]},"application/vnd.dvb.dvbisl+xml":{"source":"iana","compressible":true},"application/vnd.dvb.dvbj":{"source":"iana"},"application/vnd.dvb.esgcontainer":{"source":"iana"},"application/vnd.dvb.ipdcdftnotifaccess":{"source":"iana"},"application/vnd.dvb.ipdcesgaccess":{"source":"iana"},"application/vnd.dvb.ipdcesgaccess2":{"source":"iana"},"application/vnd.dvb.ipdcesgpdd":{"source":"iana"},"application/vnd.dvb.ipdcroaming":{"source":"iana"},"application/vnd.dvb.iptv.alfec-base":{"source":"iana"},"application/vnd.dvb.iptv.alfec-enhancement":{"source":"iana"},"application/vnd.dvb.notif-aggregate-root+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-container+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-generic+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-ia-msglist+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-ia-registration-request+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-ia-registration-response+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-init+xml":{"source":"iana","compressible":true},"application/vnd.dvb.pfr":{"source":"iana"},"application/vnd.dvb.service":{"source":"iana","extensions":["svc"]},"application/vnd.dxr":{"source":"iana"},"application/vnd.dynageo":{"source":"iana","extensions":["geo"]},"application/vnd.dzr":{"source":"iana"},"application/vnd.easykaraoke.cdgdownload":{"source":"iana"},"application/vnd.ecdis-update":{"source":"iana"},"application/vnd.ecip.rlp":{"source":"iana"},"application/vnd.eclipse.ditto+json":{"source":"iana","compressible":true},"application/vnd.ecowin.chart":{"source":"iana","extensions":["mag"]},"application/vnd.ecowin.filerequest":{"source":"iana"},"application/vnd.ecowin.fileupdate":{"source":"iana"},"application/vnd.ecowin.series":{"source":"iana"},"application/vnd.ecowin.seriesrequest":{"source":"iana"},"application/vnd.ecowin.seriesupdate":{"source":"iana"},"application/vnd.efi.img":{"source":"iana"},"application/vnd.efi.iso":{"source":"iana"},"application/vnd.emclient.accessrequest+xml":{"source":"iana","compressible":true},"application/vnd.enliven":{"source":"iana","extensions":["nml"]},"application/vnd.enphase.envoy":{"source":"iana"},"application/vnd.eprints.data+xml":{"source":"iana","compressible":true},"application/vnd.epson.esf":{"source":"iana","extensions":["esf"]},"application/vnd.epson.msf":{"source":"iana","extensions":["msf"]},"application/vnd.epson.quickanime":{"source":"iana","extensions":["qam"]},"application/vnd.epson.salt":{"source":"iana","extensions":["slt"]},"application/vnd.epson.ssf":{"source":"iana","extensions":["ssf"]},"application/vnd.ericsson.quickcall":{"source":"iana"},"application/vnd.espass-espass+zip":{"source":"iana","compressible":false},"application/vnd.eszigno3+xml":{"source":"iana","compressible":true,"extensions":["es3","et3"]},"application/vnd.etsi.aoc+xml":{"source":"iana","compressible":true},"application/vnd.etsi.asic-e+zip":{"source":"iana","compressible":false},"application/vnd.etsi.asic-s+zip":{"source":"iana","compressible":false},"application/vnd.etsi.cug+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvcommand+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvdiscovery+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvprofile+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvsad-bc+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvsad-cod+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvsad-npvr+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvservice+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvsync+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvueprofile+xml":{"source":"iana","compressible":true},"application/vnd.etsi.mcid+xml":{"source":"iana","compressible":true},"application/vnd.etsi.mheg5":{"source":"iana"},"application/vnd.etsi.overload-control-policy-dataset+xml":{"source":"iana","compressible":true},"application/vnd.etsi.pstn+xml":{"source":"iana","compressible":true},"application/vnd.etsi.sci+xml":{"source":"iana","compressible":true},"application/vnd.etsi.simservs+xml":{"source":"iana","compressible":true},"application/vnd.etsi.timestamp-token":{"source":"iana"},"application/vnd.etsi.tsl+xml":{"source":"iana","compressible":true},"application/vnd.etsi.tsl.der":{"source":"iana"},"application/vnd.eu.kasparian.car+json":{"source":"iana","compressible":true},"application/vnd.eudora.data":{"source":"iana"},"application/vnd.evolv.ecig.profile":{"source":"iana"},"application/vnd.evolv.ecig.settings":{"source":"iana"},"application/vnd.evolv.ecig.theme":{"source":"iana"},"application/vnd.exstream-empower+zip":{"source":"iana","compressible":false},"application/vnd.exstream-package":{"source":"iana"},"application/vnd.ezpix-album":{"source":"iana","extensions":["ez2"]},"application/vnd.ezpix-package":{"source":"iana","extensions":["ez3"]},"application/vnd.f-secure.mobile":{"source":"iana"},"application/vnd.familysearch.gedcom+zip":{"source":"iana","compressible":false},"application/vnd.fastcopy-disk-image":{"source":"iana"},"application/vnd.fdf":{"source":"iana","extensions":["fdf"]},"application/vnd.fdsn.mseed":{"source":"iana","extensions":["mseed"]},"application/vnd.fdsn.seed":{"source":"iana","extensions":["seed","dataless"]},"application/vnd.ffsns":{"source":"iana"},"application/vnd.ficlab.flb+zip":{"source":"iana","compressible":false},"application/vnd.filmit.zfc":{"source":"iana"},"application/vnd.fints":{"source":"iana"},"application/vnd.firemonkeys.cloudcell":{"source":"iana"},"application/vnd.flographit":{"source":"iana","extensions":["gph"]},"application/vnd.fluxtime.clip":{"source":"iana","extensions":["ftc"]},"application/vnd.font-fontforge-sfd":{"source":"iana"},"application/vnd.framemaker":{"source":"iana","extensions":["fm","frame","maker","book"]},"application/vnd.frogans.fnc":{"source":"iana","extensions":["fnc"]},"application/vnd.frogans.ltf":{"source":"iana","extensions":["ltf"]},"application/vnd.fsc.weblaunch":{"source":"iana","extensions":["fsc"]},"application/vnd.fujifilm.fb.docuworks":{"source":"iana"},"application/vnd.fujifilm.fb.docuworks.binder":{"source":"iana"},"application/vnd.fujifilm.fb.docuworks.container":{"source":"iana"},"application/vnd.fujifilm.fb.jfi+xml":{"source":"iana","compressible":true},"application/vnd.fujitsu.oasys":{"source":"iana","extensions":["oas"]},"application/vnd.fujitsu.oasys2":{"source":"iana","extensions":["oa2"]},"application/vnd.fujitsu.oasys3":{"source":"iana","extensions":["oa3"]},"application/vnd.fujitsu.oasysgp":{"source":"iana","extensions":["fg5"]},"application/vnd.fujitsu.oasysprs":{"source":"iana","extensions":["bh2"]},"application/vnd.fujixerox.art-ex":{"source":"iana"},"application/vnd.fujixerox.art4":{"source":"iana"},"application/vnd.fujixerox.ddd":{"source":"iana","extensions":["ddd"]},"application/vnd.fujixerox.docuworks":{"source":"iana","extensions":["xdw"]},"application/vnd.fujixerox.docuworks.binder":{"source":"iana","extensions":["xbd"]},"application/vnd.fujixerox.docuworks.container":{"source":"iana"},"application/vnd.fujixerox.hbpl":{"source":"iana"},"application/vnd.fut-misnet":{"source":"iana"},"application/vnd.futoin+cbor":{"source":"iana"},"application/vnd.futoin+json":{"source":"iana","compressible":true},"application/vnd.fuzzysheet":{"source":"iana","extensions":["fzs"]},"application/vnd.genomatix.tuxedo":{"source":"iana","extensions":["txd"]},"application/vnd.gentics.grd+json":{"source":"iana","compressible":true},"application/vnd.geo+json":{"source":"iana","compressible":true},"application/vnd.geocube+xml":{"source":"iana","compressible":true},"application/vnd.geogebra.file":{"source":"iana","extensions":["ggb"]},"application/vnd.geogebra.slides":{"source":"iana"},"application/vnd.geogebra.tool":{"source":"iana","extensions":["ggt"]},"application/vnd.geometry-explorer":{"source":"iana","extensions":["gex","gre"]},"application/vnd.geonext":{"source":"iana","extensions":["gxt"]},"application/vnd.geoplan":{"source":"iana","extensions":["g2w"]},"application/vnd.geospace":{"source":"iana","extensions":["g3w"]},"application/vnd.gerber":{"source":"iana"},"application/vnd.globalplatform.card-content-mgt":{"source":"iana"},"application/vnd.globalplatform.card-content-mgt-response":{"source":"iana"},"application/vnd.gmx":{"source":"iana","extensions":["gmx"]},"application/vnd.google-apps.document":{"compressible":false,"extensions":["gdoc"]},"application/vnd.google-apps.presentation":{"compressible":false,"extensions":["gslides"]},"application/vnd.google-apps.spreadsheet":{"compressible":false,"extensions":["gsheet"]},"application/vnd.google-earth.kml+xml":{"source":"iana","compressible":true,"extensions":["kml"]},"application/vnd.google-earth.kmz":{"source":"iana","compressible":false,"extensions":["kmz"]},"application/vnd.gov.sk.e-form+xml":{"source":"iana","compressible":true},"application/vnd.gov.sk.e-form+zip":{"source":"iana","compressible":false},"application/vnd.gov.sk.xmldatacontainer+xml":{"source":"iana","compressible":true},"application/vnd.grafeq":{"source":"iana","extensions":["gqf","gqs"]},"application/vnd.gridmp":{"source":"iana"},"application/vnd.groove-account":{"source":"iana","extensions":["gac"]},"application/vnd.groove-help":{"source":"iana","extensions":["ghf"]},"application/vnd.groove-identity-message":{"source":"iana","extensions":["gim"]},"application/vnd.groove-injector":{"source":"iana","extensions":["grv"]},"application/vnd.groove-tool-message":{"source":"iana","extensions":["gtm"]},"application/vnd.groove-tool-template":{"source":"iana","extensions":["tpl"]},"application/vnd.groove-vcard":{"source":"iana","extensions":["vcg"]},"application/vnd.hal+json":{"source":"iana","compressible":true},"application/vnd.hal+xml":{"source":"iana","compressible":true,"extensions":["hal"]},"application/vnd.handheld-entertainment+xml":{"source":"iana","compressible":true,"extensions":["zmm"]},"application/vnd.hbci":{"source":"iana","extensions":["hbci"]},"application/vnd.hc+json":{"source":"iana","compressible":true},"application/vnd.hcl-bireports":{"source":"iana"},"application/vnd.hdt":{"source":"iana"},"application/vnd.heroku+json":{"source":"iana","compressible":true},"application/vnd.hhe.lesson-player":{"source":"iana","extensions":["les"]},"application/vnd.hl7cda+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.hl7v2+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.hp-hpgl":{"source":"iana","extensions":["hpgl"]},"application/vnd.hp-hpid":{"source":"iana","extensions":["hpid"]},"application/vnd.hp-hps":{"source":"iana","extensions":["hps"]},"application/vnd.hp-jlyt":{"source":"iana","extensions":["jlt"]},"application/vnd.hp-pcl":{"source":"iana","extensions":["pcl"]},"application/vnd.hp-pclxl":{"source":"iana","extensions":["pclxl"]},"application/vnd.httphone":{"source":"iana"},"application/vnd.hydrostatix.sof-data":{"source":"iana","extensions":["sfd-hdstx"]},"application/vnd.hyper+json":{"source":"iana","compressible":true},"application/vnd.hyper-item+json":{"source":"iana","compressible":true},"application/vnd.hyperdrive+json":{"source":"iana","compressible":true},"application/vnd.hzn-3d-crossword":{"source":"iana"},"application/vnd.ibm.afplinedata":{"source":"iana"},"application/vnd.ibm.electronic-media":{"source":"iana"},"application/vnd.ibm.minipay":{"source":"iana","extensions":["mpy"]},"application/vnd.ibm.modcap":{"source":"iana","extensions":["afp","listafp","list3820"]},"application/vnd.ibm.rights-management":{"source":"iana","extensions":["irm"]},"application/vnd.ibm.secure-container":{"source":"iana","extensions":["sc"]},"application/vnd.iccprofile":{"source":"iana","extensions":["icc","icm"]},"application/vnd.ieee.1905":{"source":"iana"},"application/vnd.igloader":{"source":"iana","extensions":["igl"]},"application/vnd.imagemeter.folder+zip":{"source":"iana","compressible":false},"application/vnd.imagemeter.image+zip":{"source":"iana","compressible":false},"application/vnd.immervision-ivp":{"source":"iana","extensions":["ivp"]},"application/vnd.immervision-ivu":{"source":"iana","extensions":["ivu"]},"application/vnd.ims.imsccv1p1":{"source":"iana"},"application/vnd.ims.imsccv1p2":{"source":"iana"},"application/vnd.ims.imsccv1p3":{"source":"iana"},"application/vnd.ims.lis.v2.result+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolconsumerprofile+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolproxy+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolproxy.id+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolsettings+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolsettings.simple+json":{"source":"iana","compressible":true},"application/vnd.informedcontrol.rms+xml":{"source":"iana","compressible":true},"application/vnd.informix-visionary":{"source":"iana"},"application/vnd.infotech.project":{"source":"iana"},"application/vnd.infotech.project+xml":{"source":"iana","compressible":true},"application/vnd.innopath.wamp.notification":{"source":"iana"},"application/vnd.insors.igm":{"source":"iana","extensions":["igm"]},"application/vnd.intercon.formnet":{"source":"iana","extensions":["xpw","xpx"]},"application/vnd.intergeo":{"source":"iana","extensions":["i2g"]},"application/vnd.intertrust.digibox":{"source":"iana"},"application/vnd.intertrust.nncp":{"source":"iana"},"application/vnd.intu.qbo":{"source":"iana","extensions":["qbo"]},"application/vnd.intu.qfx":{"source":"iana","extensions":["qfx"]},"application/vnd.iptc.g2.catalogitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.conceptitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.knowledgeitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.newsitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.newsmessage+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.packageitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.planningitem+xml":{"source":"iana","compressible":true},"application/vnd.ipunplugged.rcprofile":{"source":"iana","extensions":["rcprofile"]},"application/vnd.irepository.package+xml":{"source":"iana","compressible":true,"extensions":["irp"]},"application/vnd.is-xpr":{"source":"iana","extensions":["xpr"]},"application/vnd.isac.fcs":{"source":"iana","extensions":["fcs"]},"application/vnd.iso11783-10+zip":{"source":"iana","compressible":false},"application/vnd.jam":{"source":"iana","extensions":["jam"]},"application/vnd.japannet-directory-service":{"source":"iana"},"application/vnd.japannet-jpnstore-wakeup":{"source":"iana"},"application/vnd.japannet-payment-wakeup":{"source":"iana"},"application/vnd.japannet-registration":{"source":"iana"},"application/vnd.japannet-registration-wakeup":{"source":"iana"},"application/vnd.japannet-setstore-wakeup":{"source":"iana"},"application/vnd.japannet-verification":{"source":"iana"},"application/vnd.japannet-verification-wakeup":{"source":"iana"},"application/vnd.jcp.javame.midlet-rms":{"source":"iana","extensions":["rms"]},"application/vnd.jisp":{"source":"iana","extensions":["jisp"]},"application/vnd.joost.joda-archive":{"source":"iana","extensions":["joda"]},"application/vnd.jsk.isdn-ngn":{"source":"iana"},"application/vnd.kahootz":{"source":"iana","extensions":["ktz","ktr"]},"application/vnd.kde.karbon":{"source":"iana","extensions":["karbon"]},"application/vnd.kde.kchart":{"source":"iana","extensions":["chrt"]},"application/vnd.kde.kformula":{"source":"iana","extensions":["kfo"]},"application/vnd.kde.kivio":{"source":"iana","extensions":["flw"]},"application/vnd.kde.kontour":{"source":"iana","extensions":["kon"]},"application/vnd.kde.kpresenter":{"source":"iana","extensions":["kpr","kpt"]},"application/vnd.kde.kspread":{"source":"iana","extensions":["ksp"]},"application/vnd.kde.kword":{"source":"iana","extensions":["kwd","kwt"]},"application/vnd.kenameaapp":{"source":"iana","extensions":["htke"]},"application/vnd.kidspiration":{"source":"iana","extensions":["kia"]},"application/vnd.kinar":{"source":"iana","extensions":["kne","knp"]},"application/vnd.koan":{"source":"iana","extensions":["skp","skd","skt","skm"]},"application/vnd.kodak-descriptor":{"source":"iana","extensions":["sse"]},"application/vnd.las":{"source":"iana"},"application/vnd.las.las+json":{"source":"iana","compressible":true},"application/vnd.las.las+xml":{"source":"iana","compressible":true,"extensions":["lasxml"]},"application/vnd.laszip":{"source":"iana"},"application/vnd.leap+json":{"source":"iana","compressible":true},"application/vnd.liberty-request+xml":{"source":"iana","compressible":true},"application/vnd.llamagraphics.life-balance.desktop":{"source":"iana","extensions":["lbd"]},"application/vnd.llamagraphics.life-balance.exchange+xml":{"source":"iana","compressible":true,"extensions":["lbe"]},"application/vnd.logipipe.circuit+zip":{"source":"iana","compressible":false},"application/vnd.loom":{"source":"iana"},"application/vnd.lotus-1-2-3":{"source":"iana","extensions":["123"]},"application/vnd.lotus-approach":{"source":"iana","extensions":["apr"]},"application/vnd.lotus-freelance":{"source":"iana","extensions":["pre"]},"application/vnd.lotus-notes":{"source":"iana","extensions":["nsf"]},"application/vnd.lotus-organizer":{"source":"iana","extensions":["org"]},"application/vnd.lotus-screencam":{"source":"iana","extensions":["scm"]},"application/vnd.lotus-wordpro":{"source":"iana","extensions":["lwp"]},"application/vnd.macports.portpkg":{"source":"iana","extensions":["portpkg"]},"application/vnd.mapbox-vector-tile":{"source":"iana","extensions":["mvt"]},"application/vnd.marlin.drm.actiontoken+xml":{"source":"iana","compressible":true},"application/vnd.marlin.drm.conftoken+xml":{"source":"iana","compressible":true},"application/vnd.marlin.drm.license+xml":{"source":"iana","compressible":true},"application/vnd.marlin.drm.mdcf":{"source":"iana"},"application/vnd.mason+json":{"source":"iana","compressible":true},"application/vnd.maxar.archive.3tz+zip":{"source":"iana","compressible":false},"application/vnd.maxmind.maxmind-db":{"source":"iana"},"application/vnd.mcd":{"source":"iana","extensions":["mcd"]},"application/vnd.medcalcdata":{"source":"iana","extensions":["mc1"]},"application/vnd.mediastation.cdkey":{"source":"iana","extensions":["cdkey"]},"application/vnd.meridian-slingshot":{"source":"iana"},"application/vnd.mfer":{"source":"iana","extensions":["mwf"]},"application/vnd.mfmp":{"source":"iana","extensions":["mfm"]},"application/vnd.micro+json":{"source":"iana","compressible":true},"application/vnd.micrografx.flo":{"source":"iana","extensions":["flo"]},"application/vnd.micrografx.igx":{"source":"iana","extensions":["igx"]},"application/vnd.microsoft.portable-executable":{"source":"iana"},"application/vnd.microsoft.windows.thumbnail-cache":{"source":"iana"},"application/vnd.miele+json":{"source":"iana","compressible":true},"application/vnd.mif":{"source":"iana","extensions":["mif"]},"application/vnd.minisoft-hp3000-save":{"source":"iana"},"application/vnd.mitsubishi.misty-guard.trustweb":{"source":"iana"},"application/vnd.mobius.daf":{"source":"iana","extensions":["daf"]},"application/vnd.mobius.dis":{"source":"iana","extensions":["dis"]},"application/vnd.mobius.mbk":{"source":"iana","extensions":["mbk"]},"application/vnd.mobius.mqy":{"source":"iana","extensions":["mqy"]},"application/vnd.mobius.msl":{"source":"iana","extensions":["msl"]},"application/vnd.mobius.plc":{"source":"iana","extensions":["plc"]},"application/vnd.mobius.txf":{"source":"iana","extensions":["txf"]},"application/vnd.mophun.application":{"source":"iana","extensions":["mpn"]},"application/vnd.mophun.certificate":{"source":"iana","extensions":["mpc"]},"application/vnd.motorola.flexsuite":{"source":"iana"},"application/vnd.motorola.flexsuite.adsi":{"source":"iana"},"application/vnd.motorola.flexsuite.fis":{"source":"iana"},"application/vnd.motorola.flexsuite.gotap":{"source":"iana"},"application/vnd.motorola.flexsuite.kmr":{"source":"iana"},"application/vnd.motorola.flexsuite.ttc":{"source":"iana"},"application/vnd.motorola.flexsuite.wem":{"source":"iana"},"application/vnd.motorola.iprm":{"source":"iana"},"application/vnd.mozilla.xul+xml":{"source":"iana","compressible":true,"extensions":["xul"]},"application/vnd.ms-3mfdocument":{"source":"iana"},"application/vnd.ms-artgalry":{"source":"iana","extensions":["cil"]},"application/vnd.ms-asf":{"source":"iana"},"application/vnd.ms-cab-compressed":{"source":"iana","extensions":["cab"]},"application/vnd.ms-color.iccprofile":{"source":"apache"},"application/vnd.ms-excel":{"source":"iana","compressible":false,"extensions":["xls","xlm","xla","xlc","xlt","xlw"]},"application/vnd.ms-excel.addin.macroenabled.12":{"source":"iana","extensions":["xlam"]},"application/vnd.ms-excel.sheet.binary.macroenabled.12":{"source":"iana","extensions":["xlsb"]},"application/vnd.ms-excel.sheet.macroenabled.12":{"source":"iana","extensions":["xlsm"]},"application/vnd.ms-excel.template.macroenabled.12":{"source":"iana","extensions":["xltm"]},"application/vnd.ms-fontobject":{"source":"iana","compressible":true,"extensions":["eot"]},"application/vnd.ms-htmlhelp":{"source":"iana","extensions":["chm"]},"application/vnd.ms-ims":{"source":"iana","extensions":["ims"]},"application/vnd.ms-lrm":{"source":"iana","extensions":["lrm"]},"application/vnd.ms-office.activex+xml":{"source":"iana","compressible":true},"application/vnd.ms-officetheme":{"source":"iana","extensions":["thmx"]},"application/vnd.ms-opentype":{"source":"apache","compressible":true},"application/vnd.ms-outlook":{"compressible":false,"extensions":["msg"]},"application/vnd.ms-package.obfuscated-opentype":{"source":"apache"},"application/vnd.ms-pki.seccat":{"source":"apache","extensions":["cat"]},"application/vnd.ms-pki.stl":{"source":"apache","extensions":["stl"]},"application/vnd.ms-playready.initiator+xml":{"source":"iana","compressible":true},"application/vnd.ms-powerpoint":{"source":"iana","compressible":false,"extensions":["ppt","pps","pot"]},"application/vnd.ms-powerpoint.addin.macroenabled.12":{"source":"iana","extensions":["ppam"]},"application/vnd.ms-powerpoint.presentation.macroenabled.12":{"source":"iana","extensions":["pptm"]},"application/vnd.ms-powerpoint.slide.macroenabled.12":{"source":"iana","extensions":["sldm"]},"application/vnd.ms-powerpoint.slideshow.macroenabled.12":{"source":"iana","extensions":["ppsm"]},"application/vnd.ms-powerpoint.template.macroenabled.12":{"source":"iana","extensions":["potm"]},"application/vnd.ms-printdevicecapabilities+xml":{"source":"iana","compressible":true},"application/vnd.ms-printing.printticket+xml":{"source":"apache","compressible":true},"application/vnd.ms-printschematicket+xml":{"source":"iana","compressible":true},"application/vnd.ms-project":{"source":"iana","extensions":["mpp","mpt"]},"application/vnd.ms-tnef":{"source":"iana"},"application/vnd.ms-windows.devicepairing":{"source":"iana"},"application/vnd.ms-windows.nwprinting.oob":{"source":"iana"},"application/vnd.ms-windows.printerpairing":{"source":"iana"},"application/vnd.ms-windows.wsd.oob":{"source":"iana"},"application/vnd.ms-wmdrm.lic-chlg-req":{"source":"iana"},"application/vnd.ms-wmdrm.lic-resp":{"source":"iana"},"application/vnd.ms-wmdrm.meter-chlg-req":{"source":"iana"},"application/vnd.ms-wmdrm.meter-resp":{"source":"iana"},"application/vnd.ms-word.document.macroenabled.12":{"source":"iana","extensions":["docm"]},"application/vnd.ms-word.template.macroenabled.12":{"source":"iana","extensions":["dotm"]},"application/vnd.ms-works":{"source":"iana","extensions":["wps","wks","wcm","wdb"]},"application/vnd.ms-wpl":{"source":"iana","extensions":["wpl"]},"application/vnd.ms-xpsdocument":{"source":"iana","compressible":false,"extensions":["xps"]},"application/vnd.msa-disk-image":{"source":"iana"},"application/vnd.mseq":{"source":"iana","extensions":["mseq"]},"application/vnd.msign":{"source":"iana"},"application/vnd.multiad.creator":{"source":"iana"},"application/vnd.multiad.creator.cif":{"source":"iana"},"application/vnd.music-niff":{"source":"iana"},"application/vnd.musician":{"source":"iana","extensions":["mus"]},"application/vnd.muvee.style":{"source":"iana","extensions":["msty"]},"application/vnd.mynfc":{"source":"iana","extensions":["taglet"]},"application/vnd.nacamar.ybrid+json":{"source":"iana","compressible":true},"application/vnd.ncd.control":{"source":"iana"},"application/vnd.ncd.reference":{"source":"iana"},"application/vnd.nearst.inv+json":{"source":"iana","compressible":true},"application/vnd.nebumind.line":{"source":"iana"},"application/vnd.nervana":{"source":"iana"},"application/vnd.netfpx":{"source":"iana"},"application/vnd.neurolanguage.nlu":{"source":"iana","extensions":["nlu"]},"application/vnd.nimn":{"source":"iana"},"application/vnd.nintendo.nitro.rom":{"source":"iana"},"application/vnd.nintendo.snes.rom":{"source":"iana"},"application/vnd.nitf":{"source":"iana","extensions":["ntf","nitf"]},"application/vnd.noblenet-directory":{"source":"iana","extensions":["nnd"]},"application/vnd.noblenet-sealer":{"source":"iana","extensions":["nns"]},"application/vnd.noblenet-web":{"source":"iana","extensions":["nnw"]},"application/vnd.nokia.catalogs":{"source":"iana"},"application/vnd.nokia.conml+wbxml":{"source":"iana"},"application/vnd.nokia.conml+xml":{"source":"iana","compressible":true},"application/vnd.nokia.iptv.config+xml":{"source":"iana","compressible":true},"application/vnd.nokia.isds-radio-presets":{"source":"iana"},"application/vnd.nokia.landmark+wbxml":{"source":"iana"},"application/vnd.nokia.landmark+xml":{"source":"iana","compressible":true},"application/vnd.nokia.landmarkcollection+xml":{"source":"iana","compressible":true},"application/vnd.nokia.n-gage.ac+xml":{"source":"iana","compressible":true,"extensions":["ac"]},"application/vnd.nokia.n-gage.data":{"source":"iana","extensions":["ngdat"]},"application/vnd.nokia.n-gage.symbian.install":{"source":"iana","extensions":["n-gage"]},"application/vnd.nokia.ncd":{"source":"iana"},"application/vnd.nokia.pcd+wbxml":{"source":"iana"},"application/vnd.nokia.pcd+xml":{"source":"iana","compressible":true},"application/vnd.nokia.radio-preset":{"source":"iana","extensions":["rpst"]},"application/vnd.nokia.radio-presets":{"source":"iana","extensions":["rpss"]},"application/vnd.novadigm.edm":{"source":"iana","extensions":["edm"]},"application/vnd.novadigm.edx":{"source":"iana","extensions":["edx"]},"application/vnd.novadigm.ext":{"source":"iana","extensions":["ext"]},"application/vnd.ntt-local.content-share":{"source":"iana"},"application/vnd.ntt-local.file-transfer":{"source":"iana"},"application/vnd.ntt-local.ogw_remote-access":{"source":"iana"},"application/vnd.ntt-local.sip-ta_remote":{"source":"iana"},"application/vnd.ntt-local.sip-ta_tcp_stream":{"source":"iana"},"application/vnd.oasis.opendocument.chart":{"source":"iana","extensions":["odc"]},"application/vnd.oasis.opendocument.chart-template":{"source":"iana","extensions":["otc"]},"application/vnd.oasis.opendocument.database":{"source":"iana","extensions":["odb"]},"application/vnd.oasis.opendocument.formula":{"source":"iana","extensions":["odf"]},"application/vnd.oasis.opendocument.formula-template":{"source":"iana","extensions":["odft"]},"application/vnd.oasis.opendocument.graphics":{"source":"iana","compressible":false,"extensions":["odg"]},"application/vnd.oasis.opendocument.graphics-template":{"source":"iana","extensions":["otg"]},"application/vnd.oasis.opendocument.image":{"source":"iana","extensions":["odi"]},"application/vnd.oasis.opendocument.image-template":{"source":"iana","extensions":["oti"]},"application/vnd.oasis.opendocument.presentation":{"source":"iana","compressible":false,"extensions":["odp"]},"application/vnd.oasis.opendocument.presentation-template":{"source":"iana","extensions":["otp"]},"application/vnd.oasis.opendocument.spreadsheet":{"source":"iana","compressible":false,"extensions":["ods"]},"application/vnd.oasis.opendocument.spreadsheet-template":{"source":"iana","extensions":["ots"]},"application/vnd.oasis.opendocument.text":{"source":"iana","compressible":false,"extensions":["odt"]},"application/vnd.oasis.opendocument.text-master":{"source":"iana","extensions":["odm"]},"application/vnd.oasis.opendocument.text-template":{"source":"iana","extensions":["ott"]},"application/vnd.oasis.opendocument.text-web":{"source":"iana","extensions":["oth"]},"application/vnd.obn":{"source":"iana"},"application/vnd.ocf+cbor":{"source":"iana"},"application/vnd.oci.image.manifest.v1+json":{"source":"iana","compressible":true},"application/vnd.oftn.l10n+json":{"source":"iana","compressible":true},"application/vnd.oipf.contentaccessdownload+xml":{"source":"iana","compressible":true},"application/vnd.oipf.contentaccessstreaming+xml":{"source":"iana","compressible":true},"application/vnd.oipf.cspg-hexbinary":{"source":"iana"},"application/vnd.oipf.dae.svg+xml":{"source":"iana","compressible":true},"application/vnd.oipf.dae.xhtml+xml":{"source":"iana","compressible":true},"application/vnd.oipf.mippvcontrolmessage+xml":{"source":"iana","compressible":true},"application/vnd.oipf.pae.gem":{"source":"iana"},"application/vnd.oipf.spdiscovery+xml":{"source":"iana","compressible":true},"application/vnd.oipf.spdlist+xml":{"source":"iana","compressible":true},"application/vnd.oipf.ueprofile+xml":{"source":"iana","compressible":true},"application/vnd.oipf.userprofile+xml":{"source":"iana","compressible":true},"application/vnd.olpc-sugar":{"source":"iana","extensions":["xo"]},"application/vnd.oma-scws-config":{"source":"iana"},"application/vnd.oma-scws-http-request":{"source":"iana"},"application/vnd.oma-scws-http-response":{"source":"iana"},"application/vnd.oma.bcast.associated-procedure-parameter+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.drm-trigger+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.imd+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.ltkm":{"source":"iana"},"application/vnd.oma.bcast.notification+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.provisioningtrigger":{"source":"iana"},"application/vnd.oma.bcast.sgboot":{"source":"iana"},"application/vnd.oma.bcast.sgdd+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.sgdu":{"source":"iana"},"application/vnd.oma.bcast.simple-symbol-container":{"source":"iana"},"application/vnd.oma.bcast.smartcard-trigger+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.sprov+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.stkm":{"source":"iana"},"application/vnd.oma.cab-address-book+xml":{"source":"iana","compressible":true},"application/vnd.oma.cab-feature-handler+xml":{"source":"iana","compressible":true},"application/vnd.oma.cab-pcc+xml":{"source":"iana","compressible":true},"application/vnd.oma.cab-subs-invite+xml":{"source":"iana","compressible":true},"application/vnd.oma.cab-user-prefs+xml":{"source":"iana","compressible":true},"application/vnd.oma.dcd":{"source":"iana"},"application/vnd.oma.dcdc":{"source":"iana"},"application/vnd.oma.dd2+xml":{"source":"iana","compressible":true,"extensions":["dd2"]},"application/vnd.oma.drm.risd+xml":{"source":"iana","compressible":true},"application/vnd.oma.group-usage-list+xml":{"source":"iana","compressible":true},"application/vnd.oma.lwm2m+cbor":{"source":"iana"},"application/vnd.oma.lwm2m+json":{"source":"iana","compressible":true},"application/vnd.oma.lwm2m+tlv":{"source":"iana"},"application/vnd.oma.pal+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.detailed-progress-report+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.final-report+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.groups+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.invocation-descriptor+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.optimized-progress-report+xml":{"source":"iana","compressible":true},"application/vnd.oma.push":{"source":"iana"},"application/vnd.oma.scidm.messages+xml":{"source":"iana","compressible":true},"application/vnd.oma.xcap-directory+xml":{"source":"iana","compressible":true},"application/vnd.omads-email+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.omads-file+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.omads-folder+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.omaloc-supl-init":{"source":"iana"},"application/vnd.onepager":{"source":"iana"},"application/vnd.onepagertamp":{"source":"iana"},"application/vnd.onepagertamx":{"source":"iana"},"application/vnd.onepagertat":{"source":"iana"},"application/vnd.onepagertatp":{"source":"iana"},"application/vnd.onepagertatx":{"source":"iana"},"application/vnd.openblox.game+xml":{"source":"iana","compressible":true,"extensions":["obgx"]},"application/vnd.openblox.game-binary":{"source":"iana"},"application/vnd.openeye.oeb":{"source":"iana"},"application/vnd.openofficeorg.extension":{"source":"apache","extensions":["oxt"]},"application/vnd.openstreetmap.data+xml":{"source":"iana","compressible":true,"extensions":["osm"]},"application/vnd.opentimestamps.ots":{"source":"iana"},"application/vnd.openxmlformats-officedocument.custom-properties+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.customxmlproperties+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawing+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.chart+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.chartshapes+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.diagramcolors+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.diagramdata+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.diagramlayout+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.diagramstyle+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.extended-properties+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.commentauthors+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.comments+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.handoutmaster+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.notesmaster+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.notesslide+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.presentation":{"source":"iana","compressible":false,"extensions":["pptx"]},"application/vnd.openxmlformats-officedocument.presentationml.presentation.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.presprops+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slide":{"source":"iana","extensions":["sldx"]},"application/vnd.openxmlformats-officedocument.presentationml.slide+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slidelayout+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slidemaster+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slideshow":{"source":"iana","extensions":["ppsx"]},"application/vnd.openxmlformats-officedocument.presentationml.slideshow.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slideupdateinfo+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.tablestyles+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.tags+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.template":{"source":"iana","extensions":["potx"]},"application/vnd.openxmlformats-officedocument.presentationml.template.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.viewprops+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.calcchain+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.connections+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.externallink+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.pivotcachedefinition+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.pivotcacherecords+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.pivottable+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.querytable+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.revisionheaders+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.revisionlog+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.sharedstrings+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":{"source":"iana","compressible":false,"extensions":["xlsx"]},"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.sheetmetadata+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.table+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.tablesinglecells+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.template":{"source":"iana","extensions":["xltx"]},"application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.usernames+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.volatiledependencies+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.theme+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.themeoverride+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.vmldrawing":{"source":"iana"},"application/vnd.openxmlformats-officedocument.wordprocessingml.comments+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.document":{"source":"iana","compressible":false,"extensions":["docx"]},"application/vnd.openxmlformats-officedocument.wordprocessingml.document.glossary+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.document.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.endnotes+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.fonttable+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.footer+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.footnotes+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.numbering+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.settings+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.styles+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.template":{"source":"iana","extensions":["dotx"]},"application/vnd.openxmlformats-officedocument.wordprocessingml.template.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.websettings+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-package.core-properties+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-package.digital-signature-xmlsignature+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-package.relationships+xml":{"source":"iana","compressible":true},"application/vnd.oracle.resource+json":{"source":"iana","compressible":true},"application/vnd.orange.indata":{"source":"iana"},"application/vnd.osa.netdeploy":{"source":"iana"},"application/vnd.osgeo.mapguide.package":{"source":"iana","extensions":["mgp"]},"application/vnd.osgi.bundle":{"source":"iana"},"application/vnd.osgi.dp":{"source":"iana","extensions":["dp"]},"application/vnd.osgi.subsystem":{"source":"iana","extensions":["esa"]},"application/vnd.otps.ct-kip+xml":{"source":"iana","compressible":true},"application/vnd.oxli.countgraph":{"source":"iana"},"application/vnd.pagerduty+json":{"source":"iana","compressible":true},"application/vnd.palm":{"source":"iana","extensions":["pdb","pqa","oprc"]},"application/vnd.panoply":{"source":"iana"},"application/vnd.paos.xml":{"source":"iana"},"application/vnd.patentdive":{"source":"iana"},"application/vnd.patientecommsdoc":{"source":"iana"},"application/vnd.pawaafile":{"source":"iana","extensions":["paw"]},"application/vnd.pcos":{"source":"iana"},"application/vnd.pg.format":{"source":"iana","extensions":["str"]},"application/vnd.pg.osasli":{"source":"iana","extensions":["ei6"]},"application/vnd.piaccess.application-licence":{"source":"iana"},"application/vnd.picsel":{"source":"iana","extensions":["efif"]},"application/vnd.pmi.widget":{"source":"iana","extensions":["wg"]},"application/vnd.poc.group-advertisement+xml":{"source":"iana","compressible":true},"application/vnd.pocketlearn":{"source":"iana","extensions":["plf"]},"application/vnd.powerbuilder6":{"source":"iana","extensions":["pbd"]},"application/vnd.powerbuilder6-s":{"source":"iana"},"application/vnd.powerbuilder7":{"source":"iana"},"application/vnd.powerbuilder7-s":{"source":"iana"},"application/vnd.powerbuilder75":{"source":"iana"},"application/vnd.powerbuilder75-s":{"source":"iana"},"application/vnd.preminet":{"source":"iana"},"application/vnd.previewsystems.box":{"source":"iana","extensions":["box"]},"application/vnd.proteus.magazine":{"source":"iana","extensions":["mgz"]},"application/vnd.psfs":{"source":"iana"},"application/vnd.publishare-delta-tree":{"source":"iana","extensions":["qps"]},"application/vnd.pvi.ptid1":{"source":"iana","extensions":["ptid"]},"application/vnd.pwg-multiplexed":{"source":"iana"},"application/vnd.pwg-xhtml-print+xml":{"source":"iana","compressible":true},"application/vnd.qualcomm.brew-app-res":{"source":"iana"},"application/vnd.quarantainenet":{"source":"iana"},"application/vnd.quark.quarkxpress":{"source":"iana","extensions":["qxd","qxt","qwd","qwt","qxl","qxb"]},"application/vnd.quobject-quoxdocument":{"source":"iana"},"application/vnd.radisys.moml+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit-conf+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit-conn+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit-dialog+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit-stream+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-conf+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-base+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-fax-detect+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-fax-sendrecv+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-group+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-speech+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-transform+xml":{"source":"iana","compressible":true},"application/vnd.rainstor.data":{"source":"iana"},"application/vnd.rapid":{"source":"iana"},"application/vnd.rar":{"source":"iana","extensions":["rar"]},"application/vnd.realvnc.bed":{"source":"iana","extensions":["bed"]},"application/vnd.recordare.musicxml":{"source":"iana","extensions":["mxl"]},"application/vnd.recordare.musicxml+xml":{"source":"iana","compressible":true,"extensions":["musicxml"]},"application/vnd.renlearn.rlprint":{"source":"iana"},"application/vnd.resilient.logic":{"source":"iana"},"application/vnd.restful+json":{"source":"iana","compressible":true},"application/vnd.rig.cryptonote":{"source":"iana","extensions":["cryptonote"]},"application/vnd.rim.cod":{"source":"apache","extensions":["cod"]},"application/vnd.rn-realmedia":{"source":"apache","extensions":["rm"]},"application/vnd.rn-realmedia-vbr":{"source":"apache","extensions":["rmvb"]},"application/vnd.route66.link66+xml":{"source":"iana","compressible":true,"extensions":["link66"]},"application/vnd.rs-274x":{"source":"iana"},"application/vnd.ruckus.download":{"source":"iana"},"application/vnd.s3sms":{"source":"iana"},"application/vnd.sailingtracker.track":{"source":"iana","extensions":["st"]},"application/vnd.sar":{"source":"iana"},"application/vnd.sbm.cid":{"source":"iana"},"application/vnd.sbm.mid2":{"source":"iana"},"application/vnd.scribus":{"source":"iana"},"application/vnd.sealed.3df":{"source":"iana"},"application/vnd.sealed.csf":{"source":"iana"},"application/vnd.sealed.doc":{"source":"iana"},"application/vnd.sealed.eml":{"source":"iana"},"application/vnd.sealed.mht":{"source":"iana"},"application/vnd.sealed.net":{"source":"iana"},"application/vnd.sealed.ppt":{"source":"iana"},"application/vnd.sealed.tiff":{"source":"iana"},"application/vnd.sealed.xls":{"source":"iana"},"application/vnd.sealedmedia.softseal.html":{"source":"iana"},"application/vnd.sealedmedia.softseal.pdf":{"source":"iana"},"application/vnd.seemail":{"source":"iana","extensions":["see"]},"application/vnd.seis+json":{"source":"iana","compressible":true},"application/vnd.sema":{"source":"iana","extensions":["sema"]},"application/vnd.semd":{"source":"iana","extensions":["semd"]},"application/vnd.semf":{"source":"iana","extensions":["semf"]},"application/vnd.shade-save-file":{"source":"iana"},"application/vnd.shana.informed.formdata":{"source":"iana","extensions":["ifm"]},"application/vnd.shana.informed.formtemplate":{"source":"iana","extensions":["itp"]},"application/vnd.shana.informed.interchange":{"source":"iana","extensions":["iif"]},"application/vnd.shana.informed.package":{"source":"iana","extensions":["ipk"]},"application/vnd.shootproof+json":{"source":"iana","compressible":true},"application/vnd.shopkick+json":{"source":"iana","compressible":true},"application/vnd.shp":{"source":"iana"},"application/vnd.shx":{"source":"iana"},"application/vnd.sigrok.session":{"source":"iana"},"application/vnd.simtech-mindmapper":{"source":"iana","extensions":["twd","twds"]},"application/vnd.siren+json":{"source":"iana","compressible":true},"application/vnd.smaf":{"source":"iana","extensions":["mmf"]},"application/vnd.smart.notebook":{"source":"iana"},"application/vnd.smart.teacher":{"source":"iana","extensions":["teacher"]},"application/vnd.snesdev-page-table":{"source":"iana"},"application/vnd.software602.filler.form+xml":{"source":"iana","compressible":true,"extensions":["fo"]},"application/vnd.software602.filler.form-xml-zip":{"source":"iana"},"application/vnd.solent.sdkm+xml":{"source":"iana","compressible":true,"extensions":["sdkm","sdkd"]},"application/vnd.spotfire.dxp":{"source":"iana","extensions":["dxp"]},"application/vnd.spotfire.sfs":{"source":"iana","extensions":["sfs"]},"application/vnd.sqlite3":{"source":"iana"},"application/vnd.sss-cod":{"source":"iana"},"application/vnd.sss-dtf":{"source":"iana"},"application/vnd.sss-ntf":{"source":"iana"},"application/vnd.stardivision.calc":{"source":"apache","extensions":["sdc"]},"application/vnd.stardivision.draw":{"source":"apache","extensions":["sda"]},"application/vnd.stardivision.impress":{"source":"apache","extensions":["sdd"]},"application/vnd.stardivision.math":{"source":"apache","extensions":["smf"]},"application/vnd.stardivision.writer":{"source":"apache","extensions":["sdw","vor"]},"application/vnd.stardivision.writer-global":{"source":"apache","extensions":["sgl"]},"application/vnd.stepmania.package":{"source":"iana","extensions":["smzip"]},"application/vnd.stepmania.stepchart":{"source":"iana","extensions":["sm"]},"application/vnd.street-stream":{"source":"iana"},"application/vnd.sun.wadl+xml":{"source":"iana","compressible":true,"extensions":["wadl"]},"application/vnd.sun.xml.calc":{"source":"apache","extensions":["sxc"]},"application/vnd.sun.xml.calc.template":{"source":"apache","extensions":["stc"]},"application/vnd.sun.xml.draw":{"source":"apache","extensions":["sxd"]},"application/vnd.sun.xml.draw.template":{"source":"apache","extensions":["std"]},"application/vnd.sun.xml.impress":{"source":"apache","extensions":["sxi"]},"application/vnd.sun.xml.impress.template":{"source":"apache","extensions":["sti"]},"application/vnd.sun.xml.math":{"source":"apache","extensions":["sxm"]},"application/vnd.sun.xml.writer":{"source":"apache","extensions":["sxw"]},"application/vnd.sun.xml.writer.global":{"source":"apache","extensions":["sxg"]},"application/vnd.sun.xml.writer.template":{"source":"apache","extensions":["stw"]},"application/vnd.sus-calendar":{"source":"iana","extensions":["sus","susp"]},"application/vnd.svd":{"source":"iana","extensions":["svd"]},"application/vnd.swiftview-ics":{"source":"iana"},"application/vnd.sycle+xml":{"source":"iana","compressible":true},"application/vnd.syft+json":{"source":"iana","compressible":true},"application/vnd.symbian.install":{"source":"apache","extensions":["sis","sisx"]},"application/vnd.syncml+xml":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["xsm"]},"application/vnd.syncml.dm+wbxml":{"source":"iana","charset":"UTF-8","extensions":["bdm"]},"application/vnd.syncml.dm+xml":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["xdm"]},"application/vnd.syncml.dm.notification":{"source":"iana"},"application/vnd.syncml.dmddf+wbxml":{"source":"iana"},"application/vnd.syncml.dmddf+xml":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["ddf"]},"application/vnd.syncml.dmtnds+wbxml":{"source":"iana"},"application/vnd.syncml.dmtnds+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.syncml.ds.notification":{"source":"iana"},"application/vnd.tableschema+json":{"source":"iana","compressible":true},"application/vnd.tao.intent-module-archive":{"source":"iana","extensions":["tao"]},"application/vnd.tcpdump.pcap":{"source":"iana","extensions":["pcap","cap","dmp"]},"application/vnd.think-cell.ppttc+json":{"source":"iana","compressible":true},"application/vnd.tmd.mediaflex.api+xml":{"source":"iana","compressible":true},"application/vnd.tml":{"source":"iana"},"application/vnd.tmobile-livetv":{"source":"iana","extensions":["tmo"]},"application/vnd.tri.onesource":{"source":"iana"},"application/vnd.trid.tpt":{"source":"iana","extensions":["tpt"]},"application/vnd.triscape.mxs":{"source":"iana","extensions":["mxs"]},"application/vnd.trueapp":{"source":"iana","extensions":["tra"]},"application/vnd.truedoc":{"source":"iana"},"application/vnd.ubisoft.webplayer":{"source":"iana"},"application/vnd.ufdl":{"source":"iana","extensions":["ufd","ufdl"]},"application/vnd.uiq.theme":{"source":"iana","extensions":["utz"]},"application/vnd.umajin":{"source":"iana","extensions":["umj"]},"application/vnd.unity":{"source":"iana","extensions":["unityweb"]},"application/vnd.uoml+xml":{"source":"iana","compressible":true,"extensions":["uoml"]},"application/vnd.uplanet.alert":{"source":"iana"},"application/vnd.uplanet.alert-wbxml":{"source":"iana"},"application/vnd.uplanet.bearer-choice":{"source":"iana"},"application/vnd.uplanet.bearer-choice-wbxml":{"source":"iana"},"application/vnd.uplanet.cacheop":{"source":"iana"},"application/vnd.uplanet.cacheop-wbxml":{"source":"iana"},"application/vnd.uplanet.channel":{"source":"iana"},"application/vnd.uplanet.channel-wbxml":{"source":"iana"},"application/vnd.uplanet.list":{"source":"iana"},"application/vnd.uplanet.list-wbxml":{"source":"iana"},"application/vnd.uplanet.listcmd":{"source":"iana"},"application/vnd.uplanet.listcmd-wbxml":{"source":"iana"},"application/vnd.uplanet.signal":{"source":"iana"},"application/vnd.uri-map":{"source":"iana"},"application/vnd.valve.source.material":{"source":"iana"},"application/vnd.vcx":{"source":"iana","extensions":["vcx"]},"application/vnd.vd-study":{"source":"iana"},"application/vnd.vectorworks":{"source":"iana"},"application/vnd.vel+json":{"source":"iana","compressible":true},"application/vnd.verimatrix.vcas":{"source":"iana"},"application/vnd.veritone.aion+json":{"source":"iana","compressible":true},"application/vnd.veryant.thin":{"source":"iana"},"application/vnd.ves.encrypted":{"source":"iana"},"application/vnd.vidsoft.vidconference":{"source":"iana"},"application/vnd.visio":{"source":"iana","extensions":["vsd","vst","vss","vsw"]},"application/vnd.visionary":{"source":"iana","extensions":["vis"]},"application/vnd.vividence.scriptfile":{"source":"iana"},"application/vnd.vsf":{"source":"iana","extensions":["vsf"]},"application/vnd.wap.sic":{"source":"iana"},"application/vnd.wap.slc":{"source":"iana"},"application/vnd.wap.wbxml":{"source":"iana","charset":"UTF-8","extensions":["wbxml"]},"application/vnd.wap.wmlc":{"source":"iana","extensions":["wmlc"]},"application/vnd.wap.wmlscriptc":{"source":"iana","extensions":["wmlsc"]},"application/vnd.webturbo":{"source":"iana","extensions":["wtb"]},"application/vnd.wfa.dpp":{"source":"iana"},"application/vnd.wfa.p2p":{"source":"iana"},"application/vnd.wfa.wsc":{"source":"iana"},"application/vnd.windows.devicepairing":{"source":"iana"},"application/vnd.wmc":{"source":"iana"},"application/vnd.wmf.bootstrap":{"source":"iana"},"application/vnd.wolfram.mathematica":{"source":"iana"},"application/vnd.wolfram.mathematica.package":{"source":"iana"},"application/vnd.wolfram.player":{"source":"iana","extensions":["nbp"]},"application/vnd.wordperfect":{"source":"iana","extensions":["wpd"]},"application/vnd.wqd":{"source":"iana","extensions":["wqd"]},"application/vnd.wrq-hp3000-labelled":{"source":"iana"},"application/vnd.wt.stf":{"source":"iana","extensions":["stf"]},"application/vnd.wv.csp+wbxml":{"source":"iana"},"application/vnd.wv.csp+xml":{"source":"iana","compressible":true},"application/vnd.wv.ssp+xml":{"source":"iana","compressible":true},"application/vnd.xacml+json":{"source":"iana","compressible":true},"application/vnd.xara":{"source":"iana","extensions":["xar"]},"application/vnd.xfdl":{"source":"iana","extensions":["xfdl"]},"application/vnd.xfdl.webform":{"source":"iana"},"application/vnd.xmi+xml":{"source":"iana","compressible":true},"application/vnd.xmpie.cpkg":{"source":"iana"},"application/vnd.xmpie.dpkg":{"source":"iana"},"application/vnd.xmpie.plan":{"source":"iana"},"application/vnd.xmpie.ppkg":{"source":"iana"},"application/vnd.xmpie.xlim":{"source":"iana"},"application/vnd.yamaha.hv-dic":{"source":"iana","extensions":["hvd"]},"application/vnd.yamaha.hv-script":{"source":"iana","extensions":["hvs"]},"application/vnd.yamaha.hv-voice":{"source":"iana","extensions":["hvp"]},"application/vnd.yamaha.openscoreformat":{"source":"iana","extensions":["osf"]},"application/vnd.yamaha.openscoreformat.osfpvg+xml":{"source":"iana","compressible":true,"extensions":["osfpvg"]},"application/vnd.yamaha.remote-setup":{"source":"iana"},"application/vnd.yamaha.smaf-audio":{"source":"iana","extensions":["saf"]},"application/vnd.yamaha.smaf-phrase":{"source":"iana","extensions":["spf"]},"application/vnd.yamaha.through-ngn":{"source":"iana"},"application/vnd.yamaha.tunnel-udpencap":{"source":"iana"},"application/vnd.yaoweme":{"source":"iana"},"application/vnd.yellowriver-custom-menu":{"source":"iana","extensions":["cmp"]},"application/vnd.youtube.yt":{"source":"iana"},"application/vnd.zul":{"source":"iana","extensions":["zir","zirz"]},"application/vnd.zzazz.deck+xml":{"source":"iana","compressible":true,"extensions":["zaz"]},"application/voicexml+xml":{"source":"iana","compressible":true,"extensions":["vxml"]},"application/voucher-cms+json":{"source":"iana","compressible":true},"application/vq-rtcpxr":{"source":"iana"},"application/wasm":{"source":"iana","compressible":true,"extensions":["wasm"]},"application/watcherinfo+xml":{"source":"iana","compressible":true,"extensions":["wif"]},"application/webpush-options+json":{"source":"iana","compressible":true},"application/whoispp-query":{"source":"iana"},"application/whoispp-response":{"source":"iana"},"application/widget":{"source":"iana","extensions":["wgt"]},"application/winhlp":{"source":"apache","extensions":["hlp"]},"application/wita":{"source":"iana"},"application/wordperfect5.1":{"source":"iana"},"application/wsdl+xml":{"source":"iana","compressible":true,"extensions":["wsdl"]},"application/wspolicy+xml":{"source":"iana","compressible":true,"extensions":["wspolicy"]},"application/x-7z-compressed":{"source":"apache","compressible":false,"extensions":["7z"]},"application/x-abiword":{"source":"apache","extensions":["abw"]},"application/x-ace-compressed":{"source":"apache","extensions":["ace"]},"application/x-amf":{"source":"apache"},"application/x-apple-diskimage":{"source":"apache","extensions":["dmg"]},"application/x-arj":{"compressible":false,"extensions":["arj"]},"application/x-authorware-bin":{"source":"apache","extensions":["aab","x32","u32","vox"]},"application/x-authorware-map":{"source":"apache","extensions":["aam"]},"application/x-authorware-seg":{"source":"apache","extensions":["aas"]},"application/x-bcpio":{"source":"apache","extensions":["bcpio"]},"application/x-bdoc":{"compressible":false,"extensions":["bdoc"]},"application/x-bittorrent":{"source":"apache","extensions":["torrent"]},"application/x-blorb":{"source":"apache","extensions":["blb","blorb"]},"application/x-bzip":{"source":"apache","compressible":false,"extensions":["bz"]},"application/x-bzip2":{"source":"apache","compressible":false,"extensions":["bz2","boz"]},"application/x-cbr":{"source":"apache","extensions":["cbr","cba","cbt","cbz","cb7"]},"application/x-cdlink":{"source":"apache","extensions":["vcd"]},"application/x-cfs-compressed":{"source":"apache","extensions":["cfs"]},"application/x-chat":{"source":"apache","extensions":["chat"]},"application/x-chess-pgn":{"source":"apache","extensions":["pgn"]},"application/x-chrome-extension":{"extensions":["crx"]},"application/x-cocoa":{"source":"nginx","extensions":["cco"]},"application/x-compress":{"source":"apache"},"application/x-conference":{"source":"apache","extensions":["nsc"]},"application/x-cpio":{"source":"apache","extensions":["cpio"]},"application/x-csh":{"source":"apache","extensions":["csh"]},"application/x-deb":{"compressible":false},"application/x-debian-package":{"source":"apache","extensions":["deb","udeb"]},"application/x-dgc-compressed":{"source":"apache","extensions":["dgc"]},"application/x-director":{"source":"apache","extensions":["dir","dcr","dxr","cst","cct","cxt","w3d","fgd","swa"]},"application/x-doom":{"source":"apache","extensions":["wad"]},"application/x-dtbncx+xml":{"source":"apache","compressible":true,"extensions":["ncx"]},"application/x-dtbook+xml":{"source":"apache","compressible":true,"extensions":["dtb"]},"application/x-dtbresource+xml":{"source":"apache","compressible":true,"extensions":["res"]},"application/x-dvi":{"source":"apache","compressible":false,"extensions":["dvi"]},"application/x-envoy":{"source":"apache","extensions":["evy"]},"application/x-eva":{"source":"apache","extensions":["eva"]},"application/x-font-bdf":{"source":"apache","extensions":["bdf"]},"application/x-font-dos":{"source":"apache"},"application/x-font-framemaker":{"source":"apache"},"application/x-font-ghostscript":{"source":"apache","extensions":["gsf"]},"application/x-font-libgrx":{"source":"apache"},"application/x-font-linux-psf":{"source":"apache","extensions":["psf"]},"application/x-font-pcf":{"source":"apache","extensions":["pcf"]},"application/x-font-snf":{"source":"apache","extensions":["snf"]},"application/x-font-speedo":{"source":"apache"},"application/x-font-sunos-news":{"source":"apache"},"application/x-font-type1":{"source":"apache","extensions":["pfa","pfb","pfm","afm"]},"application/x-font-vfont":{"source":"apache"},"application/x-freearc":{"source":"apache","extensions":["arc"]},"application/x-futuresplash":{"source":"apache","extensions":["spl"]},"application/x-gca-compressed":{"source":"apache","extensions":["gca"]},"application/x-glulx":{"source":"apache","extensions":["ulx"]},"application/x-gnumeric":{"source":"apache","extensions":["gnumeric"]},"application/x-gramps-xml":{"source":"apache","extensions":["gramps"]},"application/x-gtar":{"source":"apache","extensions":["gtar"]},"application/x-gzip":{"source":"apache"},"application/x-hdf":{"source":"apache","extensions":["hdf"]},"application/x-httpd-php":{"compressible":true,"extensions":["php"]},"application/x-install-instructions":{"source":"apache","extensions":["install"]},"application/x-iso9660-image":{"source":"apache","extensions":["iso"]},"application/x-iwork-keynote-sffkey":{"extensions":["key"]},"application/x-iwork-numbers-sffnumbers":{"extensions":["numbers"]},"application/x-iwork-pages-sffpages":{"extensions":["pages"]},"application/x-java-archive-diff":{"source":"nginx","extensions":["jardiff"]},"application/x-java-jnlp-file":{"source":"apache","compressible":false,"extensions":["jnlp"]},"application/x-javascript":{"compressible":true},"application/x-keepass2":{"extensions":["kdbx"]},"application/x-latex":{"source":"apache","compressible":false,"extensions":["latex"]},"application/x-lua-bytecode":{"extensions":["luac"]},"application/x-lzh-compressed":{"source":"apache","extensions":["lzh","lha"]},"application/x-makeself":{"source":"nginx","extensions":["run"]},"application/x-mie":{"source":"apache","extensions":["mie"]},"application/x-mobipocket-ebook":{"source":"apache","extensions":["prc","mobi"]},"application/x-mpegurl":{"compressible":false},"application/x-ms-application":{"source":"apache","extensions":["application"]},"application/x-ms-shortcut":{"source":"apache","extensions":["lnk"]},"application/x-ms-wmd":{"source":"apache","extensions":["wmd"]},"application/x-ms-wmz":{"source":"apache","extensions":["wmz"]},"application/x-ms-xbap":{"source":"apache","extensions":["xbap"]},"application/x-msaccess":{"source":"apache","extensions":["mdb"]},"application/x-msbinder":{"source":"apache","extensions":["obd"]},"application/x-mscardfile":{"source":"apache","extensions":["crd"]},"application/x-msclip":{"source":"apache","extensions":["clp"]},"application/x-msdos-program":{"extensions":["exe"]},"application/x-msdownload":{"source":"apache","extensions":["exe","dll","com","bat","msi"]},"application/x-msmediaview":{"source":"apache","extensions":["mvb","m13","m14"]},"application/x-msmetafile":{"source":"apache","extensions":["wmf","wmz","emf","emz"]},"application/x-msmoney":{"source":"apache","extensions":["mny"]},"application/x-mspublisher":{"source":"apache","extensions":["pub"]},"application/x-msschedule":{"source":"apache","extensions":["scd"]},"application/x-msterminal":{"source":"apache","extensions":["trm"]},"application/x-mswrite":{"source":"apache","extensions":["wri"]},"application/x-netcdf":{"source":"apache","extensions":["nc","cdf"]},"application/x-ns-proxy-autoconfig":{"compressible":true,"extensions":["pac"]},"application/x-nzb":{"source":"apache","extensions":["nzb"]},"application/x-perl":{"source":"nginx","extensions":["pl","pm"]},"application/x-pilot":{"source":"nginx","extensions":["prc","pdb"]},"application/x-pkcs12":{"source":"apache","compressible":false,"extensions":["p12","pfx"]},"application/x-pkcs7-certificates":{"source":"apache","extensions":["p7b","spc"]},"application/x-pkcs7-certreqresp":{"source":"apache","extensions":["p7r"]},"application/x-pki-message":{"source":"iana"},"application/x-rar-compressed":{"source":"apache","compressible":false,"extensions":["rar"]},"application/x-redhat-package-manager":{"source":"nginx","extensions":["rpm"]},"application/x-research-info-systems":{"source":"apache","extensions":["ris"]},"application/x-sea":{"source":"nginx","extensions":["sea"]},"application/x-sh":{"source":"apache","compressible":true,"extensions":["sh"]},"application/x-shar":{"source":"apache","extensions":["shar"]},"application/x-shockwave-flash":{"source":"apache","compressible":false,"extensions":["swf"]},"application/x-silverlight-app":{"source":"apache","extensions":["xap"]},"application/x-sql":{"source":"apache","extensions":["sql"]},"application/x-stuffit":{"source":"apache","compressible":false,"extensions":["sit"]},"application/x-stuffitx":{"source":"apache","extensions":["sitx"]},"application/x-subrip":{"source":"apache","extensions":["srt"]},"application/x-sv4cpio":{"source":"apache","extensions":["sv4cpio"]},"application/x-sv4crc":{"source":"apache","extensions":["sv4crc"]},"application/x-t3vm-image":{"source":"apache","extensions":["t3"]},"application/x-tads":{"source":"apache","extensions":["gam"]},"application/x-tar":{"source":"apache","compressible":true,"extensions":["tar"]},"application/x-tcl":{"source":"apache","extensions":["tcl","tk"]},"application/x-tex":{"source":"apache","extensions":["tex"]},"application/x-tex-tfm":{"source":"apache","extensions":["tfm"]},"application/x-texinfo":{"source":"apache","extensions":["texinfo","texi"]},"application/x-tgif":{"source":"apache","extensions":["obj"]},"application/x-ustar":{"source":"apache","extensions":["ustar"]},"application/x-virtualbox-hdd":{"compressible":true,"extensions":["hdd"]},"application/x-virtualbox-ova":{"compressible":true,"extensions":["ova"]},"application/x-virtualbox-ovf":{"compressible":true,"extensions":["ovf"]},"application/x-virtualbox-vbox":{"compressible":true,"extensions":["vbox"]},"application/x-virtualbox-vbox-extpack":{"compressible":false,"extensions":["vbox-extpack"]},"application/x-virtualbox-vdi":{"compressible":true,"extensions":["vdi"]},"application/x-virtualbox-vhd":{"compressible":true,"extensions":["vhd"]},"application/x-virtualbox-vmdk":{"compressible":true,"extensions":["vmdk"]},"application/x-wais-source":{"source":"apache","extensions":["src"]},"application/x-web-app-manifest+json":{"compressible":true,"extensions":["webapp"]},"application/x-www-form-urlencoded":{"source":"iana","compressible":true},"application/x-x509-ca-cert":{"source":"iana","extensions":["der","crt","pem"]},"application/x-x509-ca-ra-cert":{"source":"iana"},"application/x-x509-next-ca-cert":{"source":"iana"},"application/x-xfig":{"source":"apache","extensions":["fig"]},"application/x-xliff+xml":{"source":"apache","compressible":true,"extensions":["xlf"]},"application/x-xpinstall":{"source":"apache","compressible":false,"extensions":["xpi"]},"application/x-xz":{"source":"apache","extensions":["xz"]},"application/x-zmachine":{"source":"apache","extensions":["z1","z2","z3","z4","z5","z6","z7","z8"]},"application/x400-bp":{"source":"iana"},"application/xacml+xml":{"source":"iana","compressible":true},"application/xaml+xml":{"source":"apache","compressible":true,"extensions":["xaml"]},"application/xcap-att+xml":{"source":"iana","compressible":true,"extensions":["xav"]},"application/xcap-caps+xml":{"source":"iana","compressible":true,"extensions":["xca"]},"application/xcap-diff+xml":{"source":"iana","compressible":true,"extensions":["xdf"]},"application/xcap-el+xml":{"source":"iana","compressible":true,"extensions":["xel"]},"application/xcap-error+xml":{"source":"iana","compressible":true},"application/xcap-ns+xml":{"source":"iana","compressible":true,"extensions":["xns"]},"application/xcon-conference-info+xml":{"source":"iana","compressible":true},"application/xcon-conference-info-diff+xml":{"source":"iana","compressible":true},"application/xenc+xml":{"source":"iana","compressible":true,"extensions":["xenc"]},"application/xhtml+xml":{"source":"iana","compressible":true,"extensions":["xhtml","xht"]},"application/xhtml-voice+xml":{"source":"apache","compressible":true},"application/xliff+xml":{"source":"iana","compressible":true,"extensions":["xlf"]},"application/xml":{"source":"iana","compressible":true,"extensions":["xml","xsl","xsd","rng"]},"application/xml-dtd":{"source":"iana","compressible":true,"extensions":["dtd"]},"application/xml-external-parsed-entity":{"source":"iana"},"application/xml-patch+xml":{"source":"iana","compressible":true},"application/xmpp+xml":{"source":"iana","compressible":true},"application/xop+xml":{"source":"iana","compressible":true,"extensions":["xop"]},"application/xproc+xml":{"source":"apache","compressible":true,"extensions":["xpl"]},"application/xslt+xml":{"source":"iana","compressible":true,"extensions":["xsl","xslt"]},"application/xspf+xml":{"source":"apache","compressible":true,"extensions":["xspf"]},"application/xv+xml":{"source":"iana","compressible":true,"extensions":["mxml","xhvml","xvml","xvm"]},"application/yang":{"source":"iana","extensions":["yang"]},"application/yang-data+json":{"source":"iana","compressible":true},"application/yang-data+xml":{"source":"iana","compressible":true},"application/yang-patch+json":{"source":"iana","compressible":true},"application/yang-patch+xml":{"source":"iana","compressible":true},"application/yin+xml":{"source":"iana","compressible":true,"extensions":["yin"]},"application/zip":{"source":"iana","compressible":false,"extensions":["zip"]},"application/zlib":{"source":"iana"},"application/zstd":{"source":"iana"},"audio/1d-interleaved-parityfec":{"source":"iana"},"audio/32kadpcm":{"source":"iana"},"audio/3gpp":{"source":"iana","compressible":false,"extensions":["3gpp"]},"audio/3gpp2":{"source":"iana"},"audio/aac":{"source":"iana"},"audio/ac3":{"source":"iana"},"audio/adpcm":{"source":"apache","extensions":["adp"]},"audio/amr":{"source":"iana","extensions":["amr"]},"audio/amr-wb":{"source":"iana"},"audio/amr-wb+":{"source":"iana"},"audio/aptx":{"source":"iana"},"audio/asc":{"source":"iana"},"audio/atrac-advanced-lossless":{"source":"iana"},"audio/atrac-x":{"source":"iana"},"audio/atrac3":{"source":"iana"},"audio/basic":{"source":"iana","compressible":false,"extensions":["au","snd"]},"audio/bv16":{"source":"iana"},"audio/bv32":{"source":"iana"},"audio/clearmode":{"source":"iana"},"audio/cn":{"source":"iana"},"audio/dat12":{"source":"iana"},"audio/dls":{"source":"iana"},"audio/dsr-es201108":{"source":"iana"},"audio/dsr-es202050":{"source":"iana"},"audio/dsr-es202211":{"source":"iana"},"audio/dsr-es202212":{"source":"iana"},"audio/dv":{"source":"iana"},"audio/dvi4":{"source":"iana"},"audio/eac3":{"source":"iana"},"audio/encaprtp":{"source":"iana"},"audio/evrc":{"source":"iana"},"audio/evrc-qcp":{"source":"iana"},"audio/evrc0":{"source":"iana"},"audio/evrc1":{"source":"iana"},"audio/evrcb":{"source":"iana"},"audio/evrcb0":{"source":"iana"},"audio/evrcb1":{"source":"iana"},"audio/evrcnw":{"source":"iana"},"audio/evrcnw0":{"source":"iana"},"audio/evrcnw1":{"source":"iana"},"audio/evrcwb":{"source":"iana"},"audio/evrcwb0":{"source":"iana"},"audio/evrcwb1":{"source":"iana"},"audio/evs":{"source":"iana"},"audio/flexfec":{"source":"iana"},"audio/fwdred":{"source":"iana"},"audio/g711-0":{"source":"iana"},"audio/g719":{"source":"iana"},"audio/g722":{"source":"iana"},"audio/g7221":{"source":"iana"},"audio/g723":{"source":"iana"},"audio/g726-16":{"source":"iana"},"audio/g726-24":{"source":"iana"},"audio/g726-32":{"source":"iana"},"audio/g726-40":{"source":"iana"},"audio/g728":{"source":"iana"},"audio/g729":{"source":"iana"},"audio/g7291":{"source":"iana"},"audio/g729d":{"source":"iana"},"audio/g729e":{"source":"iana"},"audio/gsm":{"source":"iana"},"audio/gsm-efr":{"source":"iana"},"audio/gsm-hr-08":{"source":"iana"},"audio/ilbc":{"source":"iana"},"audio/ip-mr_v2.5":{"source":"iana"},"audio/isac":{"source":"apache"},"audio/l16":{"source":"iana"},"audio/l20":{"source":"iana"},"audio/l24":{"source":"iana","compressible":false},"audio/l8":{"source":"iana"},"audio/lpc":{"source":"iana"},"audio/melp":{"source":"iana"},"audio/melp1200":{"source":"iana"},"audio/melp2400":{"source":"iana"},"audio/melp600":{"source":"iana"},"audio/mhas":{"source":"iana"},"audio/midi":{"source":"apache","extensions":["mid","midi","kar","rmi"]},"audio/mobile-xmf":{"source":"iana","extensions":["mxmf"]},"audio/mp3":{"compressible":false,"extensions":["mp3"]},"audio/mp4":{"source":"iana","compressible":false,"extensions":["m4a","mp4a"]},"audio/mp4a-latm":{"source":"iana"},"audio/mpa":{"source":"iana"},"audio/mpa-robust":{"source":"iana"},"audio/mpeg":{"source":"iana","compressible":false,"extensions":["mpga","mp2","mp2a","mp3","m2a","m3a"]},"audio/mpeg4-generic":{"source":"iana"},"audio/musepack":{"source":"apache"},"audio/ogg":{"source":"iana","compressible":false,"extensions":["oga","ogg","spx","opus"]},"audio/opus":{"source":"iana"},"audio/parityfec":{"source":"iana"},"audio/pcma":{"source":"iana"},"audio/pcma-wb":{"source":"iana"},"audio/pcmu":{"source":"iana"},"audio/pcmu-wb":{"source":"iana"},"audio/prs.sid":{"source":"iana"},"audio/qcelp":{"source":"iana"},"audio/raptorfec":{"source":"iana"},"audio/red":{"source":"iana"},"audio/rtp-enc-aescm128":{"source":"iana"},"audio/rtp-midi":{"source":"iana"},"audio/rtploopback":{"source":"iana"},"audio/rtx":{"source":"iana"},"audio/s3m":{"source":"apache","extensions":["s3m"]},"audio/scip":{"source":"iana"},"audio/silk":{"source":"apache","extensions":["sil"]},"audio/smv":{"source":"iana"},"audio/smv-qcp":{"source":"iana"},"audio/smv0":{"source":"iana"},"audio/sofa":{"source":"iana"},"audio/sp-midi":{"source":"iana"},"audio/speex":{"source":"iana"},"audio/t140c":{"source":"iana"},"audio/t38":{"source":"iana"},"audio/telephone-event":{"source":"iana"},"audio/tetra_acelp":{"source":"iana"},"audio/tetra_acelp_bb":{"source":"iana"},"audio/tone":{"source":"iana"},"audio/tsvcis":{"source":"iana"},"audio/uemclip":{"source":"iana"},"audio/ulpfec":{"source":"iana"},"audio/usac":{"source":"iana"},"audio/vdvi":{"source":"iana"},"audio/vmr-wb":{"source":"iana"},"audio/vnd.3gpp.iufp":{"source":"iana"},"audio/vnd.4sb":{"source":"iana"},"audio/vnd.audiokoz":{"source":"iana"},"audio/vnd.celp":{"source":"iana"},"audio/vnd.cisco.nse":{"source":"iana"},"audio/vnd.cmles.radio-events":{"source":"iana"},"audio/vnd.cns.anp1":{"source":"iana"},"audio/vnd.cns.inf1":{"source":"iana"},"audio/vnd.dece.audio":{"source":"iana","extensions":["uva","uvva"]},"audio/vnd.digital-winds":{"source":"iana","extensions":["eol"]},"audio/vnd.dlna.adts":{"source":"iana"},"audio/vnd.dolby.heaac.1":{"source":"iana"},"audio/vnd.dolby.heaac.2":{"source":"iana"},"audio/vnd.dolby.mlp":{"source":"iana"},"audio/vnd.dolby.mps":{"source":"iana"},"audio/vnd.dolby.pl2":{"source":"iana"},"audio/vnd.dolby.pl2x":{"source":"iana"},"audio/vnd.dolby.pl2z":{"source":"iana"},"audio/vnd.dolby.pulse.1":{"source":"iana"},"audio/vnd.dra":{"source":"iana","extensions":["dra"]},"audio/vnd.dts":{"source":"iana","extensions":["dts"]},"audio/vnd.dts.hd":{"source":"iana","extensions":["dtshd"]},"audio/vnd.dts.uhd":{"source":"iana"},"audio/vnd.dvb.file":{"source":"iana"},"audio/vnd.everad.plj":{"source":"iana"},"audio/vnd.hns.audio":{"source":"iana"},"audio/vnd.lucent.voice":{"source":"iana","extensions":["lvp"]},"audio/vnd.ms-playready.media.pya":{"source":"iana","extensions":["pya"]},"audio/vnd.nokia.mobile-xmf":{"source":"iana"},"audio/vnd.nortel.vbk":{"source":"iana"},"audio/vnd.nuera.ecelp4800":{"source":"iana","extensions":["ecelp4800"]},"audio/vnd.nuera.ecelp7470":{"source":"iana","extensions":["ecelp7470"]},"audio/vnd.nuera.ecelp9600":{"source":"iana","extensions":["ecelp9600"]},"audio/vnd.octel.sbc":{"source":"iana"},"audio/vnd.presonus.multitrack":{"source":"iana"},"audio/vnd.qcelp":{"source":"iana"},"audio/vnd.rhetorex.32kadpcm":{"source":"iana"},"audio/vnd.rip":{"source":"iana","extensions":["rip"]},"audio/vnd.rn-realaudio":{"compressible":false},"audio/vnd.sealedmedia.softseal.mpeg":{"source":"iana"},"audio/vnd.vmx.cvsd":{"source":"iana"},"audio/vnd.wave":{"compressible":false},"audio/vorbis":{"source":"iana","compressible":false},"audio/vorbis-config":{"source":"iana"},"audio/wav":{"compressible":false,"extensions":["wav"]},"audio/wave":{"compressible":false,"extensions":["wav"]},"audio/webm":{"source":"apache","compressible":false,"extensions":["weba"]},"audio/x-aac":{"source":"apache","compressible":false,"extensions":["aac"]},"audio/x-aiff":{"source":"apache","extensions":["aif","aiff","aifc"]},"audio/x-caf":{"source":"apache","compressible":false,"extensions":["caf"]},"audio/x-flac":{"source":"apache","extensions":["flac"]},"audio/x-m4a":{"source":"nginx","extensions":["m4a"]},"audio/x-matroska":{"source":"apache","extensions":["mka"]},"audio/x-mpegurl":{"source":"apache","extensions":["m3u"]},"audio/x-ms-wax":{"source":"apache","extensions":["wax"]},"audio/x-ms-wma":{"source":"apache","extensions":["wma"]},"audio/x-pn-realaudio":{"source":"apache","extensions":["ram","ra"]},"audio/x-pn-realaudio-plugin":{"source":"apache","extensions":["rmp"]},"audio/x-realaudio":{"source":"nginx","extensions":["ra"]},"audio/x-tta":{"source":"apache"},"audio/x-wav":{"source":"apache","extensions":["wav"]},"audio/xm":{"source":"apache","extensions":["xm"]},"chemical/x-cdx":{"source":"apache","extensions":["cdx"]},"chemical/x-cif":{"source":"apache","extensions":["cif"]},"chemical/x-cmdf":{"source":"apache","extensions":["cmdf"]},"chemical/x-cml":{"source":"apache","extensions":["cml"]},"chemical/x-csml":{"source":"apache","extensions":["csml"]},"chemical/x-pdb":{"source":"apache"},"chemical/x-xyz":{"source":"apache","extensions":["xyz"]},"font/collection":{"source":"iana","extensions":["ttc"]},"font/otf":{"source":"iana","compressible":true,"extensions":["otf"]},"font/sfnt":{"source":"iana"},"font/ttf":{"source":"iana","compressible":true,"extensions":["ttf"]},"font/woff":{"source":"iana","extensions":["woff"]},"font/woff2":{"source":"iana","extensions":["woff2"]},"image/aces":{"source":"iana","extensions":["exr"]},"image/apng":{"compressible":false,"extensions":["apng"]},"image/avci":{"source":"iana","extensions":["avci"]},"image/avcs":{"source":"iana","extensions":["avcs"]},"image/avif":{"source":"iana","compressible":false,"extensions":["avif"]},"image/bmp":{"source":"iana","compressible":true,"extensions":["bmp"]},"image/cgm":{"source":"iana","extensions":["cgm"]},"image/dicom-rle":{"source":"iana","extensions":["drle"]},"image/emf":{"source":"iana","extensions":["emf"]},"image/fits":{"source":"iana","extensions":["fits"]},"image/g3fax":{"source":"iana","extensions":["g3"]},"image/gif":{"source":"iana","compressible":false,"extensions":["gif"]},"image/heic":{"source":"iana","extensions":["heic"]},"image/heic-sequence":{"source":"iana","extensions":["heics"]},"image/heif":{"source":"iana","extensions":["heif"]},"image/heif-sequence":{"source":"iana","extensions":["heifs"]},"image/hej2k":{"source":"iana","extensions":["hej2"]},"image/hsj2":{"source":"iana","extensions":["hsj2"]},"image/ief":{"source":"iana","extensions":["ief"]},"image/jls":{"source":"iana","extensions":["jls"]},"image/jp2":{"source":"iana","compressible":false,"extensions":["jp2","jpg2"]},"image/jpeg":{"source":"iana","compressible":false,"extensions":["jpeg","jpg","jpe"]},"image/jph":{"source":"iana","extensions":["jph"]},"image/jphc":{"source":"iana","extensions":["jhc"]},"image/jpm":{"source":"iana","compressible":false,"extensions":["jpm"]},"image/jpx":{"source":"iana","compressible":false,"extensions":["jpx","jpf"]},"image/jxr":{"source":"iana","extensions":["jxr"]},"image/jxra":{"source":"iana","extensions":["jxra"]},"image/jxrs":{"source":"iana","extensions":["jxrs"]},"image/jxs":{"source":"iana","extensions":["jxs"]},"image/jxsc":{"source":"iana","extensions":["jxsc"]},"image/jxsi":{"source":"iana","extensions":["jxsi"]},"image/jxss":{"source":"iana","extensions":["jxss"]},"image/ktx":{"source":"iana","extensions":["ktx"]},"image/ktx2":{"source":"iana","extensions":["ktx2"]},"image/naplps":{"source":"iana"},"image/pjpeg":{"compressible":false},"image/png":{"source":"iana","compressible":false,"extensions":["png"]},"image/prs.btif":{"source":"iana","extensions":["btif"]},"image/prs.pti":{"source":"iana","extensions":["pti"]},"image/pwg-raster":{"source":"iana"},"image/sgi":{"source":"apache","extensions":["sgi"]},"image/svg+xml":{"source":"iana","compressible":true,"extensions":["svg","svgz"]},"image/t38":{"source":"iana","extensions":["t38"]},"image/tiff":{"source":"iana","compressible":false,"extensions":["tif","tiff"]},"image/tiff-fx":{"source":"iana","extensions":["tfx"]},"image/vnd.adobe.photoshop":{"source":"iana","compressible":true,"extensions":["psd"]},"image/vnd.airzip.accelerator.azv":{"source":"iana","extensions":["azv"]},"image/vnd.cns.inf2":{"source":"iana"},"image/vnd.dece.graphic":{"source":"iana","extensions":["uvi","uvvi","uvg","uvvg"]},"image/vnd.djvu":{"source":"iana","extensions":["djvu","djv"]},"image/vnd.dvb.subtitle":{"source":"iana","extensions":["sub"]},"image/vnd.dwg":{"source":"iana","extensions":["dwg"]},"image/vnd.dxf":{"source":"iana","extensions":["dxf"]},"image/vnd.fastbidsheet":{"source":"iana","extensions":["fbs"]},"image/vnd.fpx":{"source":"iana","extensions":["fpx"]},"image/vnd.fst":{"source":"iana","extensions":["fst"]},"image/vnd.fujixerox.edmics-mmr":{"source":"iana","extensions":["mmr"]},"image/vnd.fujixerox.edmics-rlc":{"source":"iana","extensions":["rlc"]},"image/vnd.globalgraphics.pgb":{"source":"iana"},"image/vnd.microsoft.icon":{"source":"iana","compressible":true,"extensions":["ico"]},"image/vnd.mix":{"source":"iana"},"image/vnd.mozilla.apng":{"source":"iana"},"image/vnd.ms-dds":{"compressible":true,"extensions":["dds"]},"image/vnd.ms-modi":{"source":"iana","extensions":["mdi"]},"image/vnd.ms-photo":{"source":"apache","extensions":["wdp"]},"image/vnd.net-fpx":{"source":"iana","extensions":["npx"]},"image/vnd.pco.b16":{"source":"iana","extensions":["b16"]},"image/vnd.radiance":{"source":"iana"},"image/vnd.sealed.png":{"source":"iana"},"image/vnd.sealedmedia.softseal.gif":{"source":"iana"},"image/vnd.sealedmedia.softseal.jpg":{"source":"iana"},"image/vnd.svf":{"source":"iana"},"image/vnd.tencent.tap":{"source":"iana","extensions":["tap"]},"image/vnd.valve.source.texture":{"source":"iana","extensions":["vtf"]},"image/vnd.wap.wbmp":{"source":"iana","extensions":["wbmp"]},"image/vnd.xiff":{"source":"iana","extensions":["xif"]},"image/vnd.zbrush.pcx":{"source":"iana","extensions":["pcx"]},"image/webp":{"source":"apache","extensions":["webp"]},"image/wmf":{"source":"iana","extensions":["wmf"]},"image/x-3ds":{"source":"apache","extensions":["3ds"]},"image/x-cmu-raster":{"source":"apache","extensions":["ras"]},"image/x-cmx":{"source":"apache","extensions":["cmx"]},"image/x-freehand":{"source":"apache","extensions":["fh","fhc","fh4","fh5","fh7"]},"image/x-icon":{"source":"apache","compressible":true,"extensions":["ico"]},"image/x-jng":{"source":"nginx","extensions":["jng"]},"image/x-mrsid-image":{"source":"apache","extensions":["sid"]},"image/x-ms-bmp":{"source":"nginx","compressible":true,"extensions":["bmp"]},"image/x-pcx":{"source":"apache","extensions":["pcx"]},"image/x-pict":{"source":"apache","extensions":["pic","pct"]},"image/x-portable-anymap":{"source":"apache","extensions":["pnm"]},"image/x-portable-bitmap":{"source":"apache","extensions":["pbm"]},"image/x-portable-graymap":{"source":"apache","extensions":["pgm"]},"image/x-portable-pixmap":{"source":"apache","extensions":["ppm"]},"image/x-rgb":{"source":"apache","extensions":["rgb"]},"image/x-tga":{"source":"apache","extensions":["tga"]},"image/x-xbitmap":{"source":"apache","extensions":["xbm"]},"image/x-xcf":{"compressible":false},"image/x-xpixmap":{"source":"apache","extensions":["xpm"]},"image/x-xwindowdump":{"source":"apache","extensions":["xwd"]},"message/cpim":{"source":"iana"},"message/delivery-status":{"source":"iana"},"message/disposition-notification":{"source":"iana","extensions":["disposition-notification"]},"message/external-body":{"source":"iana"},"message/feedback-report":{"source":"iana"},"message/global":{"source":"iana","extensions":["u8msg"]},"message/global-delivery-status":{"source":"iana","extensions":["u8dsn"]},"message/global-disposition-notification":{"source":"iana","extensions":["u8mdn"]},"message/global-headers":{"source":"iana","extensions":["u8hdr"]},"message/http":{"source":"iana","compressible":false},"message/imdn+xml":{"source":"iana","compressible":true},"message/news":{"source":"iana"},"message/partial":{"source":"iana","compressible":false},"message/rfc822":{"source":"iana","compressible":true,"extensions":["eml","mime"]},"message/s-http":{"source":"iana"},"message/sip":{"source":"iana"},"message/sipfrag":{"source":"iana"},"message/tracking-status":{"source":"iana"},"message/vnd.si.simp":{"source":"iana"},"message/vnd.wfa.wsc":{"source":"iana","extensions":["wsc"]},"model/3mf":{"source":"iana","extensions":["3mf"]},"model/e57":{"source":"iana"},"model/gltf+json":{"source":"iana","compressible":true,"extensions":["gltf"]},"model/gltf-binary":{"source":"iana","compressible":true,"extensions":["glb"]},"model/iges":{"source":"iana","compressible":false,"extensions":["igs","iges"]},"model/mesh":{"source":"iana","compressible":false,"extensions":["msh","mesh","silo"]},"model/mtl":{"source":"iana","extensions":["mtl"]},"model/obj":{"source":"iana","extensions":["obj"]},"model/step":{"source":"iana"},"model/step+xml":{"source":"iana","compressible":true,"extensions":["stpx"]},"model/step+zip":{"source":"iana","compressible":false,"extensions":["stpz"]},"model/step-xml+zip":{"source":"iana","compressible":false,"extensions":["stpxz"]},"model/stl":{"source":"iana","extensions":["stl"]},"model/vnd.collada+xml":{"source":"iana","compressible":true,"extensions":["dae"]},"model/vnd.dwf":{"source":"iana","extensions":["dwf"]},"model/vnd.flatland.3dml":{"source":"iana"},"model/vnd.gdl":{"source":"iana","extensions":["gdl"]},"model/vnd.gs-gdl":{"source":"apache"},"model/vnd.gs.gdl":{"source":"iana"},"model/vnd.gtw":{"source":"iana","extensions":["gtw"]},"model/vnd.moml+xml":{"source":"iana","compressible":true},"model/vnd.mts":{"source":"iana","extensions":["mts"]},"model/vnd.opengex":{"source":"iana","extensions":["ogex"]},"model/vnd.parasolid.transmit.binary":{"source":"iana","extensions":["x_b"]},"model/vnd.parasolid.transmit.text":{"source":"iana","extensions":["x_t"]},"model/vnd.pytha.pyox":{"source":"iana"},"model/vnd.rosette.annotated-data-model":{"source":"iana"},"model/vnd.sap.vds":{"source":"iana","extensions":["vds"]},"model/vnd.usdz+zip":{"source":"iana","compressible":false,"extensions":["usdz"]},"model/vnd.valve.source.compiled-map":{"source":"iana","extensions":["bsp"]},"model/vnd.vtu":{"source":"iana","extensions":["vtu"]},"model/vrml":{"source":"iana","compressible":false,"extensions":["wrl","vrml"]},"model/x3d+binary":{"source":"apache","compressible":false,"extensions":["x3db","x3dbz"]},"model/x3d+fastinfoset":{"source":"iana","extensions":["x3db"]},"model/x3d+vrml":{"source":"apache","compressible":false,"extensions":["x3dv","x3dvz"]},"model/x3d+xml":{"source":"iana","compressible":true,"extensions":["x3d","x3dz"]},"model/x3d-vrml":{"source":"iana","extensions":["x3dv"]},"multipart/alternative":{"source":"iana","compressible":false},"multipart/appledouble":{"source":"iana"},"multipart/byteranges":{"source":"iana"},"multipart/digest":{"source":"iana"},"multipart/encrypted":{"source":"iana","compressible":false},"multipart/form-data":{"source":"iana","compressible":false},"multipart/header-set":{"source":"iana"},"multipart/mixed":{"source":"iana"},"multipart/multilingual":{"source":"iana"},"multipart/parallel":{"source":"iana"},"multipart/related":{"source":"iana","compressible":false},"multipart/report":{"source":"iana"},"multipart/signed":{"source":"iana","compressible":false},"multipart/vnd.bint.med-plus":{"source":"iana"},"multipart/voice-message":{"source":"iana"},"multipart/x-mixed-replace":{"source":"iana"},"text/1d-interleaved-parityfec":{"source":"iana"},"text/cache-manifest":{"source":"iana","compressible":true,"extensions":["appcache","manifest"]},"text/calendar":{"source":"iana","extensions":["ics","ifb"]},"text/calender":{"compressible":true},"text/cmd":{"compressible":true},"text/coffeescript":{"extensions":["coffee","litcoffee"]},"text/cql":{"source":"iana"},"text/cql-expression":{"source":"iana"},"text/cql-identifier":{"source":"iana"},"text/css":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["css"]},"text/csv":{"source":"iana","compressible":true,"extensions":["csv"]},"text/csv-schema":{"source":"iana"},"text/directory":{"source":"iana"},"text/dns":{"source":"iana"},"text/ecmascript":{"source":"iana"},"text/encaprtp":{"source":"iana"},"text/enriched":{"source":"iana"},"text/fhirpath":{"source":"iana"},"text/flexfec":{"source":"iana"},"text/fwdred":{"source":"iana"},"text/gff3":{"source":"iana"},"text/grammar-ref-list":{"source":"iana"},"text/html":{"source":"iana","compressible":true,"extensions":["html","htm","shtml"]},"text/jade":{"extensions":["jade"]},"text/javascript":{"source":"iana","compressible":true},"text/jcr-cnd":{"source":"iana"},"text/jsx":{"compressible":true,"extensions":["jsx"]},"text/less":{"compressible":true,"extensions":["less"]},"text/markdown":{"source":"iana","compressible":true,"extensions":["markdown","md"]},"text/mathml":{"source":"nginx","extensions":["mml"]},"text/mdx":{"compressible":true,"extensions":["mdx"]},"text/mizar":{"source":"iana"},"text/n3":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["n3"]},"text/parameters":{"source":"iana","charset":"UTF-8"},"text/parityfec":{"source":"iana"},"text/plain":{"source":"iana","compressible":true,"extensions":["txt","text","conf","def","list","log","in","ini"]},"text/provenance-notation":{"source":"iana","charset":"UTF-8"},"text/prs.fallenstein.rst":{"source":"iana"},"text/prs.lines.tag":{"source":"iana","extensions":["dsc"]},"text/prs.prop.logic":{"source":"iana"},"text/raptorfec":{"source":"iana"},"text/red":{"source":"iana"},"text/rfc822-headers":{"source":"iana"},"text/richtext":{"source":"iana","compressible":true,"extensions":["rtx"]},"text/rtf":{"source":"iana","compressible":true,"extensions":["rtf"]},"text/rtp-enc-aescm128":{"source":"iana"},"text/rtploopback":{"source":"iana"},"text/rtx":{"source":"iana"},"text/sgml":{"source":"iana","extensions":["sgml","sgm"]},"text/shaclc":{"source":"iana"},"text/shex":{"source":"iana","extensions":["shex"]},"text/slim":{"extensions":["slim","slm"]},"text/spdx":{"source":"iana","extensions":["spdx"]},"text/strings":{"source":"iana"},"text/stylus":{"extensions":["stylus","styl"]},"text/t140":{"source":"iana"},"text/tab-separated-values":{"source":"iana","compressible":true,"extensions":["tsv"]},"text/troff":{"source":"iana","extensions":["t","tr","roff","man","me","ms"]},"text/turtle":{"source":"iana","charset":"UTF-8","extensions":["ttl"]},"text/ulpfec":{"source":"iana"},"text/uri-list":{"source":"iana","compressible":true,"extensions":["uri","uris","urls"]},"text/vcard":{"source":"iana","compressible":true,"extensions":["vcard"]},"text/vnd.a":{"source":"iana"},"text/vnd.abc":{"source":"iana"},"text/vnd.ascii-art":{"source":"iana"},"text/vnd.curl":{"source":"iana","extensions":["curl"]},"text/vnd.curl.dcurl":{"source":"apache","extensions":["dcurl"]},"text/vnd.curl.mcurl":{"source":"apache","extensions":["mcurl"]},"text/vnd.curl.scurl":{"source":"apache","extensions":["scurl"]},"text/vnd.debian.copyright":{"source":"iana","charset":"UTF-8"},"text/vnd.dmclientscript":{"source":"iana"},"text/vnd.dvb.subtitle":{"source":"iana","extensions":["sub"]},"text/vnd.esmertec.theme-descriptor":{"source":"iana","charset":"UTF-8"},"text/vnd.familysearch.gedcom":{"source":"iana","extensions":["ged"]},"text/vnd.ficlab.flt":{"source":"iana"},"text/vnd.fly":{"source":"iana","extensions":["fly"]},"text/vnd.fmi.flexstor":{"source":"iana","extensions":["flx"]},"text/vnd.gml":{"source":"iana"},"text/vnd.graphviz":{"source":"iana","extensions":["gv"]},"text/vnd.hans":{"source":"iana"},"text/vnd.hgl":{"source":"iana"},"text/vnd.in3d.3dml":{"source":"iana","extensions":["3dml"]},"text/vnd.in3d.spot":{"source":"iana","extensions":["spot"]},"text/vnd.iptc.newsml":{"source":"iana"},"text/vnd.iptc.nitf":{"source":"iana"},"text/vnd.latex-z":{"source":"iana"},"text/vnd.motorola.reflex":{"source":"iana"},"text/vnd.ms-mediapackage":{"source":"iana"},"text/vnd.net2phone.commcenter.command":{"source":"iana"},"text/vnd.radisys.msml-basic-layout":{"source":"iana"},"text/vnd.senx.warpscript":{"source":"iana"},"text/vnd.si.uricatalogue":{"source":"iana"},"text/vnd.sosi":{"source":"iana"},"text/vnd.sun.j2me.app-descriptor":{"source":"iana","charset":"UTF-8","extensions":["jad"]},"text/vnd.trolltech.linguist":{"source":"iana","charset":"UTF-8"},"text/vnd.wap.si":{"source":"iana"},"text/vnd.wap.sl":{"source":"iana"},"text/vnd.wap.wml":{"source":"iana","extensions":["wml"]},"text/vnd.wap.wmlscript":{"source":"iana","extensions":["wmls"]},"text/vtt":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["vtt"]},"text/x-asm":{"source":"apache","extensions":["s","asm"]},"text/x-c":{"source":"apache","extensions":["c","cc","cxx","cpp","h","hh","dic"]},"text/x-component":{"source":"nginx","extensions":["htc"]},"text/x-fortran":{"source":"apache","extensions":["f","for","f77","f90"]},"text/x-gwt-rpc":{"compressible":true},"text/x-handlebars-template":{"extensions":["hbs"]},"text/x-java-source":{"source":"apache","extensions":["java"]},"text/x-jquery-tmpl":{"compressible":true},"text/x-lua":{"extensions":["lua"]},"text/x-markdown":{"compressible":true,"extensions":["mkd"]},"text/x-nfo":{"source":"apache","extensions":["nfo"]},"text/x-opml":{"source":"apache","extensions":["opml"]},"text/x-org":{"compressible":true,"extensions":["org"]},"text/x-pascal":{"source":"apache","extensions":["p","pas"]},"text/x-processing":{"compressible":true,"extensions":["pde"]},"text/x-sass":{"extensions":["sass"]},"text/x-scss":{"extensions":["scss"]},"text/x-setext":{"source":"apache","extensions":["etx"]},"text/x-sfv":{"source":"apache","extensions":["sfv"]},"text/x-suse-ymp":{"compressible":true,"extensions":["ymp"]},"text/x-uuencode":{"source":"apache","extensions":["uu"]},"text/x-vcalendar":{"source":"apache","extensions":["vcs"]},"text/x-vcard":{"source":"apache","extensions":["vcf"]},"text/xml":{"source":"iana","compressible":true,"extensions":["xml"]},"text/xml-external-parsed-entity":{"source":"iana"},"text/yaml":{"compressible":true,"extensions":["yaml","yml"]},"video/1d-interleaved-parityfec":{"source":"iana"},"video/3gpp":{"source":"iana","extensions":["3gp","3gpp"]},"video/3gpp-tt":{"source":"iana"},"video/3gpp2":{"source":"iana","extensions":["3g2"]},"video/av1":{"source":"iana"},"video/bmpeg":{"source":"iana"},"video/bt656":{"source":"iana"},"video/celb":{"source":"iana"},"video/dv":{"source":"iana"},"video/encaprtp":{"source":"iana"},"video/ffv1":{"source":"iana"},"video/flexfec":{"source":"iana"},"video/h261":{"source":"iana","extensions":["h261"]},"video/h263":{"source":"iana","extensions":["h263"]},"video/h263-1998":{"source":"iana"},"video/h263-2000":{"source":"iana"},"video/h264":{"source":"iana","extensions":["h264"]},"video/h264-rcdo":{"source":"iana"},"video/h264-svc":{"source":"iana"},"video/h265":{"source":"iana"},"video/iso.segment":{"source":"iana","extensions":["m4s"]},"video/jpeg":{"source":"iana","extensions":["jpgv"]},"video/jpeg2000":{"source":"iana"},"video/jpm":{"source":"apache","extensions":["jpm","jpgm"]},"video/jxsv":{"source":"iana"},"video/mj2":{"source":"iana","extensions":["mj2","mjp2"]},"video/mp1s":{"source":"iana"},"video/mp2p":{"source":"iana"},"video/mp2t":{"source":"iana","extensions":["ts"]},"video/mp4":{"source":"iana","compressible":false,"extensions":["mp4","mp4v","mpg4"]},"video/mp4v-es":{"source":"iana"},"video/mpeg":{"source":"iana","compressible":false,"extensions":["mpeg","mpg","mpe","m1v","m2v"]},"video/mpeg4-generic":{"source":"iana"},"video/mpv":{"source":"iana"},"video/nv":{"source":"iana"},"video/ogg":{"source":"iana","compressible":false,"extensions":["ogv"]},"video/parityfec":{"source":"iana"},"video/pointer":{"source":"iana"},"video/quicktime":{"source":"iana","compressible":false,"extensions":["qt","mov"]},"video/raptorfec":{"source":"iana"},"video/raw":{"source":"iana"},"video/rtp-enc-aescm128":{"source":"iana"},"video/rtploopback":{"source":"iana"},"video/rtx":{"source":"iana"},"video/scip":{"source":"iana"},"video/smpte291":{"source":"iana"},"video/smpte292m":{"source":"iana"},"video/ulpfec":{"source":"iana"},"video/vc1":{"source":"iana"},"video/vc2":{"source":"iana"},"video/vnd.cctv":{"source":"iana"},"video/vnd.dece.hd":{"source":"iana","extensions":["uvh","uvvh"]},"video/vnd.dece.mobile":{"source":"iana","extensions":["uvm","uvvm"]},"video/vnd.dece.mp4":{"source":"iana"},"video/vnd.dece.pd":{"source":"iana","extensions":["uvp","uvvp"]},"video/vnd.dece.sd":{"source":"iana","extensions":["uvs","uvvs"]},"video/vnd.dece.video":{"source":"iana","extensions":["uvv","uvvv"]},"video/vnd.directv.mpeg":{"source":"iana"},"video/vnd.directv.mpeg-tts":{"source":"iana"},"video/vnd.dlna.mpeg-tts":{"source":"iana"},"video/vnd.dvb.file":{"source":"iana","extensions":["dvb"]},"video/vnd.fvt":{"source":"iana","extensions":["fvt"]},"video/vnd.hns.video":{"source":"iana"},"video/vnd.iptvforum.1dparityfec-1010":{"source":"iana"},"video/vnd.iptvforum.1dparityfec-2005":{"source":"iana"},"video/vnd.iptvforum.2dparityfec-1010":{"source":"iana"},"video/vnd.iptvforum.2dparityfec-2005":{"source":"iana"},"video/vnd.iptvforum.ttsavc":{"source":"iana"},"video/vnd.iptvforum.ttsmpeg2":{"source":"iana"},"video/vnd.motorola.video":{"source":"iana"},"video/vnd.motorola.videop":{"source":"iana"},"video/vnd.mpegurl":{"source":"iana","extensions":["mxu","m4u"]},"video/vnd.ms-playready.media.pyv":{"source":"iana","extensions":["pyv"]},"video/vnd.nokia.interleaved-multimedia":{"source":"iana"},"video/vnd.nokia.mp4vr":{"source":"iana"},"video/vnd.nokia.videovoip":{"source":"iana"},"video/vnd.objectvideo":{"source":"iana"},"video/vnd.radgamettools.bink":{"source":"iana"},"video/vnd.radgamettools.smacker":{"source":"iana"},"video/vnd.sealed.mpeg1":{"source":"iana"},"video/vnd.sealed.mpeg4":{"source":"iana"},"video/vnd.sealed.swf":{"source":"iana"},"video/vnd.sealedmedia.softseal.mov":{"source":"iana"},"video/vnd.uvvu.mp4":{"source":"iana","extensions":["uvu","uvvu"]},"video/vnd.vivo":{"source":"iana","extensions":["viv"]},"video/vnd.youtube.yt":{"source":"iana"},"video/vp8":{"source":"iana"},"video/vp9":{"source":"iana"},"video/webm":{"source":"apache","compressible":false,"extensions":["webm"]},"video/x-f4v":{"source":"apache","extensions":["f4v"]},"video/x-fli":{"source":"apache","extensions":["fli"]},"video/x-flv":{"source":"apache","compressible":false,"extensions":["flv"]},"video/x-m4v":{"source":"apache","extensions":["m4v"]},"video/x-matroska":{"source":"apache","compressible":false,"extensions":["mkv","mk3d","mks"]},"video/x-mng":{"source":"apache","extensions":["mng"]},"video/x-ms-asf":{"source":"apache","extensions":["asf","asx"]},"video/x-ms-vob":{"source":"apache","extensions":["vob"]},"video/x-ms-wm":{"source":"apache","extensions":["wm"]},"video/x-ms-wmv":{"source":"apache","compressible":false,"extensions":["wmv"]},"video/x-ms-wmx":{"source":"apache","extensions":["wmx"]},"video/x-ms-wvx":{"source":"apache","extensions":["wvx"]},"video/x-msvideo":{"source":"apache","extensions":["avi"]},"video/x-sgi-movie":{"source":"apache","extensions":["movie"]},"video/x-smv":{"source":"apache","extensions":["smv"]},"x-conference/x-cooltalk":{"source":"apache","extensions":["ice"]},"x-shader/x-fragment":{"compressible":true},"x-shader/x-vertex":{"compressible":true}}')},3489:e=>{"use strict";e.exports=Math.abs},3495:e=>{"use strict";e.exports=Math.max},3514:(e,a,t)=>{var n=t(6449);e.exports=function(e,a,t){return n(e,a,null,t)}},3632:e=>{"use strict";var a=Object.prototype.toString,t=Math.max,n=function(e,a){for(var t=[],n=0;n<e.length;n+=1)t[n]=e[n];for(var i=0;i<a.length;i+=1)t[i+e.length]=a[i];return t},i=function(e,a){for(var t=[],n=a||0,i=0;n<e.length;n+=1,i+=1)t[i]=e[n];return t},o=function(e,a){for(var t="",n=0;n<e.length;n+=1)t+=e[n],n+1<e.length&&(t+=a);return t};e.exports=function(e){var s,r=this;if("function"!=typeof r||"[object Function]"!==a.apply(r))throw TypeError("Function.prototype.bind called on incompatible "+r);for(var c=i(arguments,1),p=t(0,r.length-c.length),l=[],u=0;u<p;u++)l[u]="$"+u;if(s=Function("binder","return function ("+o(l,",")+"){ return binder.apply(this,arguments); }")(function(){if(this instanceof s){var a=r.apply(this,n(c,arguments));return Object(a)===a?a:this}return r.apply(e,n(c,arguments))}),r.prototype){var d=function(){};d.prototype=r.prototype,s.prototype=new d,d.prototype=null}return s}},3637:e=>{"use strict";e.exports=(e,a=process.argv)=>{let t=e.startsWith("-")?"":1===e.length?"-":"--",n=a.indexOf(t+e),i=a.indexOf("--");return -1!==n&&(-1===i||n<i)}},3873:e=>{"use strict";e.exports=require("path")},3876:(e,a,t)=>{"use strict";Object.defineProperty(a,"__esModule",{value:!0});var n=t(6086);Object.keys(n).forEach(function(e){"default"!==e&&Object.defineProperty(a,e,{enumerable:!0,get:function(){return n[e]}})})},3922:e=>{"use strict";var a=Object.defineProperty||!1;if(a)try{a({},"a",{value:1})}catch(e){a=!1}e.exports=a},3926:e=>{"use strict";e.exports="undefined"!=typeof Reflect&&Reflect&&Reflect.apply},3997:e=>{"use strict";e.exports=require("tty")},4072:e=>{function a(e,a,t,n){return Math.round(e/t)+" "+n+(a>=1.5*t?"s":"")}e.exports=function(e,t){t=t||{};var n,i,o,s,r=typeof e;if("string"===r&&e.length>0){var c=e;if(!((c=String(c)).length>100)){var p=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(c);if(p){var l=parseFloat(p[1]);switch((p[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return 315576e5*l;case"weeks":case"week":case"w":return 6048e5*l;case"days":case"day":case"d":return 864e5*l;case"hours":case"hour":case"hrs":case"hr":case"h":return 36e5*l;case"minutes":case"minute":case"mins":case"min":case"m":return 6e4*l;case"seconds":case"second":case"secs":case"sec":case"s":return 1e3*l;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return l;default:break}}}return}if("number"===r&&isFinite(e)){return t.long?(i=Math.abs(n=e))>=864e5?a(n,i,864e5,"day"):i>=36e5?a(n,i,36e5,"hour"):i>=6e4?a(n,i,6e4,"minute"):i>=1e3?a(n,i,1e3,"second"):n+" ms":(s=Math.abs(o=e))>=864e5?Math.round(o/864e5)+"d":s>=36e5?Math.round(o/36e5)+"h":s>=6e4?Math.round(o/6e4)+"m":s>=1e3?Math.round(o/1e3)+"s":o+"ms"}throw Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},4198:(e,a,t)=>{"use strict";var n=t(5586);if(n)try{n([],"length")}catch(e){n=null}e.exports=n},4318:(e,a,t)=>{"use strict";Object.defineProperty(a,"__esModule",{value:!0});var n=function(e){return e&&"object"==typeof e&&"default"in e?e.default:e}(t(6067));a.keyDecoder=(e,a)=>n.decode(e).toString(a),a.keyEncoder=(e,a)=>n.encode(Buffer.from(e,a).toString("ascii")).toString().replace(/=/g,"")},4332:e=>{"use strict";e.exports=Error},4391:e=>{"use strict";e.exports=function(e,a){return Object.keys(a).forEach(function(t){e[t]=e[t]||a[t]}),e}},4513:e=>{"use strict";e.exports=RangeError},4667:(e,a,t)=>{"use strict";var n=t(2590);e.exports=function(){return n()&&!!Symbol.toStringTag}},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},4946:(e,a,t)=>{"use strict";var n=t(9178),i=t(8354),o=t(3873),s=t(1630),r=t(5591),c=t(9551).parse,p=t(9021),l=t(7910).Stream,u=t(7952),d=t(8196),m=t(7028),f=t(7284),x=t(4391);function h(e){if(!(this instanceof h))return new h(e);for(var a in this._overheadLength=0,this._valueLength=0,this._valuesToMeasure=[],n.call(this),e=e||{})this[a]=e[a]}i.inherits(h,n),h.LINE_BREAK="\r\n",h.DEFAULT_CONTENT_TYPE="application/octet-stream",h.prototype.append=function(e,a,t){"string"==typeof(t=t||{})&&(t={filename:t});var i=n.prototype.append.bind(this);if(("number"==typeof a||null==a)&&(a=String(a)),Array.isArray(a))return void this._error(Error("Arrays are not supported."));var o=this._multiPartHeader(e,a,t),s=this._multiPartFooter();i(o),i(a),i(s),this._trackLength(o,a,t)},h.prototype._trackLength=function(e,a,t){var n=0;null!=t.knownLength?n+=Number(t.knownLength):Buffer.isBuffer(a)?n=a.length:"string"==typeof a&&(n=Buffer.byteLength(a)),this._valueLength+=n,this._overheadLength+=Buffer.byteLength(e)+h.LINE_BREAK.length,a&&(a.path||a.readable&&f(a,"httpVersion")||a instanceof l)&&(t.knownLength||this._valuesToMeasure.push(a))},h.prototype._lengthRetriever=function(e,a){f(e,"fd")?void 0!=e.end&&e.end!=1/0&&void 0!=e.start?a(null,e.end+1-(e.start?e.start:0)):p.stat(e.path,function(t,n){if(t)return void a(t);a(null,n.size-(e.start?e.start:0))}):f(e,"httpVersion")?a(null,Number(e.headers["content-length"])):f(e,"httpModule")?(e.on("response",function(t){e.pause(),a(null,Number(t.headers["content-length"]))}),e.resume()):a("Unknown stream")},h.prototype._multiPartHeader=function(e,a,t){if("string"==typeof t.header)return t.header;var n,i=this._getContentDisposition(a,t),o=this._getContentType(a,t),s="",r={"Content-Disposition":["form-data",'name="'+e+'"'].concat(i||[]),"Content-Type":[].concat(o||[])};for(var c in"object"==typeof t.header&&x(r,t.header),r)if(f(r,c)){if(null==(n=r[c]))continue;Array.isArray(n)||(n=[n]),n.length&&(s+=c+": "+n.join("; ")+h.LINE_BREAK)}return"--"+this.getBoundary()+h.LINE_BREAK+s+h.LINE_BREAK},h.prototype._getContentDisposition=function(e,a){var t;if("string"==typeof a.filepath?t=o.normalize(a.filepath).replace(/\\/g,"/"):a.filename||e&&(e.name||e.path)?t=o.basename(a.filename||e&&(e.name||e.path)):e&&e.readable&&f(e,"httpVersion")&&(t=o.basename(e.client._httpMessage.path||"")),t)return'filename="'+t+'"'},h.prototype._getContentType=function(e,a){var t=a.contentType;return!t&&e&&e.name&&(t=u.lookup(e.name)),!t&&e&&e.path&&(t=u.lookup(e.path)),!t&&e&&e.readable&&f(e,"httpVersion")&&(t=e.headers["content-type"]),!t&&(a.filepath||a.filename)&&(t=u.lookup(a.filepath||a.filename)),!t&&e&&"object"==typeof e&&(t=h.DEFAULT_CONTENT_TYPE),t},h.prototype._multiPartFooter=function(){return(function(e){var a=h.LINE_BREAK;0===this._streams.length&&(a+=this._lastBoundary()),e(a)}).bind(this)},h.prototype._lastBoundary=function(){return"--"+this.getBoundary()+"--"+h.LINE_BREAK},h.prototype.getHeaders=function(e){var a,t={"content-type":"multipart/form-data; boundary="+this.getBoundary()};for(a in e)f(e,a)&&(t[a.toLowerCase()]=e[a]);return t},h.prototype.setBoundary=function(e){if("string"!=typeof e)throw TypeError("FormData boundary must be a string");this._boundary=e},h.prototype.getBoundary=function(){return this._boundary||this._generateBoundary(),this._boundary},h.prototype.getBuffer=function(){for(var e=new Buffer.alloc(0),a=this.getBoundary(),t=0,n=this._streams.length;t<n;t++)"function"!=typeof this._streams[t]&&(e=Buffer.isBuffer(this._streams[t])?Buffer.concat([e,this._streams[t]]):Buffer.concat([e,Buffer.from(this._streams[t])]),("string"!=typeof this._streams[t]||this._streams[t].substring(2,a.length+2)!==a)&&(e=Buffer.concat([e,Buffer.from(h.LINE_BREAK)])));return Buffer.concat([e,Buffer.from(this._lastBoundary())])},h.prototype._generateBoundary=function(){for(var e="--------------------------",a=0;a<24;a++)e+=Math.floor(10*Math.random()).toString(16);this._boundary=e},h.prototype.getLengthSync=function(){var e=this._overheadLength+this._valueLength;return this._streams.length&&(e+=this._lastBoundary().length),this.hasKnownLength()||this._error(Error("Cannot calculate proper length in synchronous way.")),e},h.prototype.hasKnownLength=function(){var e=!0;return this._valuesToMeasure.length&&(e=!1),e},h.prototype.getLength=function(e){var a=this._overheadLength+this._valueLength;if(this._streams.length&&(a+=this._lastBoundary().length),!this._valuesToMeasure.length)return void process.nextTick(e.bind(this,null,a));d.parallel(this._valuesToMeasure,this._lengthRetriever,function(t,n){if(t)return void e(t);n.forEach(function(e){a+=e}),e(null,a)})},h.prototype.submit=function(e,a){var t,n,i={method:"post"};return"string"==typeof e?n=x({port:(e=c(e)).port,path:e.pathname,host:e.hostname,protocol:e.protocol},i):(n=x(e,i)).port||(n.port="https:"===n.protocol?443:80),n.headers=this.getHeaders(e.headers),t="https:"===n.protocol?r.request(n):s.request(n),this.getLength((function(e,n){if(e&&"Unknown stream"!==e)return void this._error(e);if(n&&t.setHeader("Content-Length",n),this.pipe(t),a){var i,o=function(e,n){return t.removeListener("error",o),t.removeListener("response",i),a.call(this,e,n)};i=o.bind(this,null),t.on("error",o),t.on("response",i)}}).bind(this)),t},h.prototype._error=function(e){this.error||(this.error=e,this.pause(),this.emit("error",e))},h.prototype.toString=function(){return"[object FormData]"},m(h,"FormData"),e.exports=h},4957:e=>{"use strict";e.exports=Math.round},5149:e=>{"use strict";e.exports=Function.prototype.call},5493:(e,a,t)=>{e.exports=t(3394)},5511:e=>{"use strict";e.exports=require("crypto")},5566:(e,a,t)=>{"use strict";let n,i=t(1820),o=t(3997),s=t(3637),{env:r}=process;function c(e){return 0!==e&&{level:e,hasBasic:!0,has256:e>=2,has16m:e>=3}}function p(e,a){if(0===n)return 0;if(s("color=16m")||s("color=full")||s("color=truecolor"))return 3;if(s("color=256"))return 2;if(e&&!a&&void 0===n)return 0;let t=n||0;if("dumb"===r.TERM)return t;if("win32"===process.platform){let e=i.release().split(".");return Number(e[0])>=10&&Number(e[2])>=10586?Number(e[2])>=14931?3:2:1}if("CI"in r)return["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","GITHUB_ACTIONS","BUILDKITE"].some(e=>e in r)||"codeship"===r.CI_NAME?1:t;if("TEAMCITY_VERSION"in r)return+!!/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(r.TEAMCITY_VERSION);if("truecolor"===r.COLORTERM)return 3;if("TERM_PROGRAM"in r){let e=parseInt((r.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(r.TERM_PROGRAM){case"iTerm.app":return e>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(r.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(r.TERM)||"COLORTERM"in r?1:t}s("no-color")||s("no-colors")||s("color=false")||s("color=never")?n=0:(s("color")||s("colors")||s("color=true")||s("color=always"))&&(n=1),"FORCE_COLOR"in r&&(n="true"===r.FORCE_COLOR?1:"false"===r.FORCE_COLOR?0:0===r.FORCE_COLOR.length?1:Math.min(parseInt(r.FORCE_COLOR,10),3)),e.exports={supportsColor:function(e){return c(p(e,e&&e.isTTY))},stdout:c(p(!0,o.isatty(1))),stderr:c(p(!0,o.isatty(2)))}},5580:e=>{"use strict";e.exports=Number.isNaN||function(e){return e!=e}},5586:e=>{"use strict";e.exports=Object.getOwnPropertyDescriptor},5591:e=>{"use strict";e.exports=require("https")},5860:(e,a,t)=>{"use strict";let n,i,o,s,r;t.r(a),t.d(a,{patchFetch:()=>tZ,routeModule:()=>tK,serverHooks:()=>tQ,workAsyncStorage:()=>tJ,workUnitAsyncStorage:()=>tX});var c,p,l,u={};t.r(u),t.d(u,{hasBrowserEnv:()=>ej,hasStandardBrowserEnv:()=>eS,hasStandardBrowserWebWorkerEnv:()=>eR,navigator:()=>eE,origin:()=>e_});var d={};t.r(d),t.d(d,{GET:()=>tY,POST:()=>tV});var m=t(6559),f=t(8088),x=t(7719),h=t(2190);function v(e,a){return function(){return e.apply(a,arguments)}}let{toString:b}=Object.prototype,{getPrototypeOf:g}=Object,{iterator:y,toStringTag:w}=Symbol,k=(e=>a=>{let t=b.call(a);return e[t]||(e[t]=t.slice(8,-1).toLowerCase())})(Object.create(null)),j=e=>(e=e.toLowerCase(),a=>k(a)===e),E=e=>a=>typeof a===e,{isArray:S}=Array,R=E("undefined"),_=j("ArrayBuffer"),O=E("string"),T=E("function"),A=E("number"),C=e=>null!==e&&"object"==typeof e,F=e=>{if("object"!==k(e))return!1;let a=g(e);return(null===a||a===Object.prototype||null===Object.getPrototypeOf(a))&&!(w in e)&&!(y in e)},D=j("Date"),P=j("File"),N=j("Blob"),z=j("FileList"),L=j("URLSearchParams"),[M,B,q,U]=["ReadableStream","Request","Response","Headers"].map(j);function I(e,a,{allOwnKeys:t=!1}={}){let n,i;if(null!=e)if("object"!=typeof e&&(e=[e]),S(e))for(n=0,i=e.length;n<i;n++)a.call(null,e[n],n,e);else{let i,o=t?Object.getOwnPropertyNames(e):Object.keys(e),s=o.length;for(n=0;n<s;n++)i=o[n],a.call(null,e[i],i,e)}}function H(e,a){let t;a=a.toLowerCase();let n=Object.keys(e),i=n.length;for(;i-- >0;)if(a===(t=n[i]).toLowerCase())return t;return null}let W="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:global,$=e=>!R(e)&&e!==W,G=(e=>a=>e&&a instanceof e)("undefined"!=typeof Uint8Array&&g(Uint8Array)),V=j("HTMLFormElement"),Y=(({hasOwnProperty:e})=>(a,t)=>e.call(a,t))(Object.prototype),K=j("RegExp"),J=(e,a)=>{let t=Object.getOwnPropertyDescriptors(e),n={};I(t,(t,i)=>{let o;!1!==(o=a(t,i,e))&&(n[i]=o||t)}),Object.defineProperties(e,n)},X=j("AsyncFunction"),Q=(c="function"==typeof setImmediate,p=T(W.postMessage),c?setImmediate:p?((e,a)=>(W.addEventListener("message",({source:t,data:n})=>{t===W&&n===e&&a.length&&a.shift()()},!1),t=>{a.push(t),W.postMessage(e,"*")}))(`axios@${Math.random()}`,[]):e=>setTimeout(e)),Z="undefined"!=typeof queueMicrotask?queueMicrotask.bind(W):"undefined"!=typeof process&&process.nextTick||Q,ee={isArray:S,isArrayBuffer:_,isBuffer:function(e){return null!==e&&!R(e)&&null!==e.constructor&&!R(e.constructor)&&T(e.constructor.isBuffer)&&e.constructor.isBuffer(e)},isFormData:e=>{let a;return e&&("function"==typeof FormData&&e instanceof FormData||T(e.append)&&("formdata"===(a=k(e))||"object"===a&&T(e.toString)&&"[object FormData]"===e.toString()))},isArrayBufferView:function(e){let a;return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&_(e.buffer)},isString:O,isNumber:A,isBoolean:e=>!0===e||!1===e,isObject:C,isPlainObject:F,isReadableStream:M,isRequest:B,isResponse:q,isHeaders:U,isUndefined:R,isDate:D,isFile:P,isBlob:N,isRegExp:K,isFunction:T,isStream:e=>C(e)&&T(e.pipe),isURLSearchParams:L,isTypedArray:G,isFileList:z,forEach:I,merge:function e(){let{caseless:a}=$(this)&&this||{},t={},n=(n,i)=>{let o=a&&H(t,i)||i;F(t[o])&&F(n)?t[o]=e(t[o],n):F(n)?t[o]=e({},n):S(n)?t[o]=n.slice():t[o]=n};for(let e=0,a=arguments.length;e<a;e++)arguments[e]&&I(arguments[e],n);return t},extend:(e,a,t,{allOwnKeys:n}={})=>(I(a,(a,n)=>{t&&T(a)?e[n]=v(a,t):e[n]=a},{allOwnKeys:n}),e),trim:e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:e=>(65279===e.charCodeAt(0)&&(e=e.slice(1)),e),inherits:(e,a,t,n)=>{e.prototype=Object.create(a.prototype,n),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:a.prototype}),t&&Object.assign(e.prototype,t)},toFlatObject:(e,a,t,n)=>{let i,o,s,r={};if(a=a||{},null==e)return a;do{for(o=(i=Object.getOwnPropertyNames(e)).length;o-- >0;)s=i[o],(!n||n(s,e,a))&&!r[s]&&(a[s]=e[s],r[s]=!0);e=!1!==t&&g(e)}while(e&&(!t||t(e,a))&&e!==Object.prototype);return a},kindOf:k,kindOfTest:j,endsWith:(e,a,t)=>{e=String(e),(void 0===t||t>e.length)&&(t=e.length),t-=a.length;let n=e.indexOf(a,t);return -1!==n&&n===t},toArray:e=>{if(!e)return null;if(S(e))return e;let a=e.length;if(!A(a))return null;let t=Array(a);for(;a-- >0;)t[a]=e[a];return t},forEachEntry:(e,a)=>{let t,n=(e&&e[y]).call(e);for(;(t=n.next())&&!t.done;){let n=t.value;a.call(e,n[0],n[1])}},matchAll:(e,a)=>{let t,n=[];for(;null!==(t=e.exec(a));)n.push(t);return n},isHTMLForm:V,hasOwnProperty:Y,hasOwnProp:Y,reduceDescriptors:J,freezeMethods:e=>{J(e,(a,t)=>{if(T(e)&&-1!==["arguments","caller","callee"].indexOf(t))return!1;if(T(e[t])){if(a.enumerable=!1,"writable"in a){a.writable=!1;return}a.set||(a.set=()=>{throw Error("Can not rewrite read-only method '"+t+"'")})}})},toObjectSet:(e,a)=>{let t={};return(S(e)?e:String(e).split(a)).forEach(e=>{t[e]=!0}),t},toCamelCase:e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(e,a,t){return a.toUpperCase()+t}),noop:()=>{},toFiniteNumber:(e,a)=>null!=e&&Number.isFinite(e*=1)?e:a,findKey:H,global:W,isContextDefined:$,isSpecCompliantForm:function(e){return!!(e&&T(e.append)&&"FormData"===e[w]&&e[y])},toJSONObject:e=>{let a=Array(10),t=(e,n)=>{if(C(e)){if(a.indexOf(e)>=0)return;if(!("toJSON"in e)){a[n]=e;let i=S(e)?[]:{};return I(e,(e,a)=>{let o=t(e,n+1);R(o)||(i[a]=o)}),a[n]=void 0,i}}return e};return t(e,0)},isAsyncFn:X,isThenable:e=>e&&(C(e)||T(e))&&T(e.then)&&T(e.catch),setImmediate:Q,asap:Z,isIterable:e=>null!=e&&T(e[y])};function ea(e,a,t,n,i){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=Error().stack,this.message=e,this.name="AxiosError",a&&(this.code=a),t&&(this.config=t),n&&(this.request=n),i&&(this.response=i,this.status=i.status?i.status:null)}ee.inherits(ea,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:ee.toJSONObject(this.config),code:this.code,status:this.status}}});let et=ea.prototype,en={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{en[e]={value:e}}),Object.defineProperties(ea,en),Object.defineProperty(et,"isAxiosError",{value:!0}),ea.from=(e,a,t,n,i,o)=>{let s=Object.create(et);return ee.toFlatObject(e,s,function(e){return e!==Error.prototype},e=>"isAxiosError"!==e),ea.call(s,e.message,a,t,n,i),s.cause=e,s.name=e.name,o&&Object.assign(s,o),s};var ei=t(4946);function eo(e){return ee.isPlainObject(e)||ee.isArray(e)}function es(e){return ee.endsWith(e,"[]")?e.slice(0,-2):e}function er(e,a,t){return e?e.concat(a).map(function(e,a){return e=es(e),!t&&a?"["+e+"]":e}).join(t?".":""):a}let ec=ee.toFlatObject(ee,{},null,function(e){return/^is[A-Z]/.test(e)}),ep=function(e,a,t){if(!ee.isObject(e))throw TypeError("target must be an object");a=a||new(ei||FormData);let n=(t=ee.toFlatObject(t,{metaTokens:!0,dots:!1,indexes:!1},!1,function(e,a){return!ee.isUndefined(a[e])})).metaTokens,i=t.visitor||p,o=t.dots,s=t.indexes,r=(t.Blob||"undefined"!=typeof Blob&&Blob)&&ee.isSpecCompliantForm(a);if(!ee.isFunction(i))throw TypeError("visitor must be a function");function c(e){if(null===e)return"";if(ee.isDate(e))return e.toISOString();if(ee.isBoolean(e))return e.toString();if(!r&&ee.isBlob(e))throw new ea("Blob is not supported. Use a Buffer instead.");return ee.isArrayBuffer(e)||ee.isTypedArray(e)?r&&"function"==typeof Blob?new Blob([e]):Buffer.from(e):e}function p(e,t,i){let r=e;if(e&&!i&&"object"==typeof e)if(ee.endsWith(t,"{}"))t=n?t:t.slice(0,-2),e=JSON.stringify(e);else{var p;if(ee.isArray(e)&&(p=e,ee.isArray(p)&&!p.some(eo))||(ee.isFileList(e)||ee.endsWith(t,"[]"))&&(r=ee.toArray(e)))return t=es(t),r.forEach(function(e,n){ee.isUndefined(e)||null===e||a.append(!0===s?er([t],n,o):null===s?t:t+"[]",c(e))}),!1}return!!eo(e)||(a.append(er(i,t,o),c(e)),!1)}let l=[],u=Object.assign(ec,{defaultVisitor:p,convertValue:c,isVisitable:eo});if(!ee.isObject(e))throw TypeError("data must be an object");return!function e(t,n){if(!ee.isUndefined(t)){if(-1!==l.indexOf(t))throw Error("Circular reference detected in "+n.join("."));l.push(t),ee.forEach(t,function(t,o){!0===(!(ee.isUndefined(t)||null===t)&&i.call(a,t,ee.isString(o)?o.trim():o,n,u))&&e(t,n?n.concat(o):[o])}),l.pop()}}(e),a};function el(e){let a={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(e){return a[e]})}function eu(e,a){this._pairs=[],e&&ep(e,this,a)}let ed=eu.prototype;function em(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function ef(e,a,t){let n;if(!a)return e;let i=t&&t.encode||em;ee.isFunction(t)&&(t={serialize:t});let o=t&&t.serialize;if(n=o?o(a,t):ee.isURLSearchParams(a)?a.toString():new eu(a,t).toString(i)){let a=e.indexOf("#");-1!==a&&(e=e.slice(0,a)),e+=(-1===e.indexOf("?")?"?":"&")+n}return e}ed.append=function(e,a){this._pairs.push([e,a])},ed.toString=function(e){let a=e?function(a){return e.call(this,a,el)}:el;return this._pairs.map(function(e){return a(e[0])+"="+a(e[1])},"").join("&")};class ex{constructor(){this.handlers=[]}use(e,a,t){return this.handlers.push({fulfilled:e,rejected:a,synchronous:!!t&&t.synchronous,runWhen:t?t.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){ee.forEach(this.handlers,function(a){null!==a&&e(a)})}}let eh={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1};var ev=t(5511);let eb=t(9551).URLSearchParams,eg="abcdefghijklmnopqrstuvwxyz",ey="0123456789",ew={DIGIT:ey,ALPHA:eg,ALPHA_DIGIT:eg+eg.toUpperCase()+ey},ek={isNode:!0,classes:{URLSearchParams:eb,FormData:ei,Blob:"undefined"!=typeof Blob&&Blob||null},ALPHABET:ew,generateString:(e=16,a=ew.ALPHA_DIGIT)=>{let t="",{length:n}=a,i=new Uint32Array(e);ev.randomFillSync(i);for(let o=0;o<e;o++)t+=a[i[o]%n];return t},protocols:["http","https","file","data"]},ej="undefined"!=typeof window&&"undefined"!=typeof document,eE="object"==typeof navigator&&navigator||void 0,eS=ej&&(!eE||0>["ReactNative","NativeScript","NS"].indexOf(eE.product)),eR="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"==typeof self.importScripts,e_=ej&&window.location.href||"http://localhost",eO={...u,...ek},eT=function(e){if(ee.isFormData(e)&&ee.isFunction(e.entries)){let a={};return ee.forEachEntry(e,(e,t)=>{!function e(a,t,n,i){let o=a[i++];if("__proto__"===o)return!0;let s=Number.isFinite(+o),r=i>=a.length;return(o=!o&&ee.isArray(n)?n.length:o,r)?ee.hasOwnProp(n,o)?n[o]=[n[o],t]:n[o]=t:(n[o]&&ee.isObject(n[o])||(n[o]=[]),e(a,t,n[o],i)&&ee.isArray(n[o])&&(n[o]=function(e){let a,t,n={},i=Object.keys(e),o=i.length;for(a=0;a<o;a++)n[t=i[a]]=e[t];return n}(n[o]))),!s}(ee.matchAll(/\w+|\[(\w*)]/g,e).map(e=>"[]"===e[0]?"":e[1]||e[0]),t,a,0)}),a}return null},eA={transitional:eh,adapter:["xhr","http","fetch"],transformRequest:[function(e,a){let t,n=a.getContentType()||"",i=n.indexOf("application/json")>-1,o=ee.isObject(e);if(o&&ee.isHTMLForm(e)&&(e=new FormData(e)),ee.isFormData(e))return i?JSON.stringify(eT(e)):e;if(ee.isArrayBuffer(e)||ee.isBuffer(e)||ee.isStream(e)||ee.isFile(e)||ee.isBlob(e)||ee.isReadableStream(e))return e;if(ee.isArrayBufferView(e))return e.buffer;if(ee.isURLSearchParams(e))return a.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();if(o){if(n.indexOf("application/x-www-form-urlencoded")>-1){var s,r;return(s=e,r=this.formSerializer,ep(s,new eO.classes.URLSearchParams,Object.assign({visitor:function(e,a,t,n){return eO.isNode&&ee.isBuffer(e)?(this.append(a,e.toString("base64")),!1):n.defaultVisitor.apply(this,arguments)}},r))).toString()}if((t=ee.isFileList(e))||n.indexOf("multipart/form-data")>-1){let a=this.env&&this.env.FormData;return ep(t?{"files[]":e}:e,a&&new a,this.formSerializer)}}if(o||i){a.setContentType("application/json",!1);var c=e;if(ee.isString(c))try{return(0,JSON.parse)(c),ee.trim(c)}catch(e){if("SyntaxError"!==e.name)throw e}return(0,JSON.stringify)(c)}return e}],transformResponse:[function(e){let a=this.transitional||eA.transitional,t=a&&a.forcedJSONParsing,n="json"===this.responseType;if(ee.isResponse(e)||ee.isReadableStream(e))return e;if(e&&ee.isString(e)&&(t&&!this.responseType||n)){let t=a&&a.silentJSONParsing;try{return JSON.parse(e)}catch(e){if(!t&&n){if("SyntaxError"===e.name)throw ea.from(e,ea.ERR_BAD_RESPONSE,this,null,this.response);throw e}}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:eO.classes.FormData,Blob:eO.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};ee.forEach(["delete","get","head","post","put","patch"],e=>{eA.headers[e]={}});let eC=ee.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),eF=e=>{let a,t,n,i={};return e&&e.split("\n").forEach(function(e){n=e.indexOf(":"),a=e.substring(0,n).trim().toLowerCase(),t=e.substring(n+1).trim(),!a||i[a]&&eC[a]||("set-cookie"===a?i[a]?i[a].push(t):i[a]=[t]:i[a]=i[a]?i[a]+", "+t:t)}),i},eD=Symbol("internals");function eP(e){return e&&String(e).trim().toLowerCase()}function eN(e){return!1===e||null==e?e:ee.isArray(e)?e.map(eN):String(e)}let ez=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function eL(e,a,t,n,i){if(ee.isFunction(n))return n.call(this,a,t);if(i&&(a=t),ee.isString(a)){if(ee.isString(n))return -1!==a.indexOf(n);if(ee.isRegExp(n))return n.test(a)}}class eM{constructor(e){e&&this.set(e)}set(e,a,t){let n=this;function i(e,a,t){let i=eP(a);if(!i)throw Error("header name must be a non-empty string");let o=ee.findKey(n,i);o&&void 0!==n[o]&&!0!==t&&(void 0!==t||!1===n[o])||(n[o||a]=eN(e))}let o=(e,a)=>ee.forEach(e,(e,t)=>i(e,t,a));if(ee.isPlainObject(e)||e instanceof this.constructor)o(e,a);else if(ee.isString(e)&&(e=e.trim())&&!ez(e))o(eF(e),a);else if(ee.isObject(e)&&ee.isIterable(e)){let t={},n,i;for(let a of e){if(!ee.isArray(a))throw TypeError("Object iterator must return a key-value pair");t[i=a[0]]=(n=t[i])?ee.isArray(n)?[...n,a[1]]:[n,a[1]]:a[1]}o(t,a)}else null!=e&&i(a,e,t);return this}get(e,a){if(e=eP(e)){let t=ee.findKey(this,e);if(t){let e=this[t];if(!a)return e;if(!0===a){let a,t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;for(;a=n.exec(e);)t[a[1]]=a[2];return t}if(ee.isFunction(a))return a.call(this,e,t);if(ee.isRegExp(a))return a.exec(e);throw TypeError("parser must be boolean|regexp|function")}}}has(e,a){if(e=eP(e)){let t=ee.findKey(this,e);return!!(t&&void 0!==this[t]&&(!a||eL(this,this[t],t,a)))}return!1}delete(e,a){let t=this,n=!1;function i(e){if(e=eP(e)){let i=ee.findKey(t,e);i&&(!a||eL(t,t[i],i,a))&&(delete t[i],n=!0)}}return ee.isArray(e)?e.forEach(i):i(e),n}clear(e){let a=Object.keys(this),t=a.length,n=!1;for(;t--;){let i=a[t];(!e||eL(this,this[i],i,e,!0))&&(delete this[i],n=!0)}return n}normalize(e){let a=this,t={};return ee.forEach(this,(n,i)=>{let o=ee.findKey(t,i);if(o){a[o]=eN(n),delete a[i];return}let s=e?i.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(e,a,t)=>a.toUpperCase()+t):String(i).trim();s!==i&&delete a[i],a[s]=eN(n),t[s]=!0}),this}concat(...e){return this.constructor.concat(this,...e)}toJSON(e){let a=Object.create(null);return ee.forEach(this,(t,n)=>{null!=t&&!1!==t&&(a[n]=e&&ee.isArray(t)?t.join(", "):t)}),a}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([e,a])=>e+": "+a).join("\n")}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e,...a){let t=new this(e);return a.forEach(e=>t.set(e)),t}static accessor(e){let a=(this[eD]=this[eD]={accessors:{}}).accessors,t=this.prototype;function n(e){let n=eP(e);if(!a[n]){let i=ee.toCamelCase(" "+e);["get","set","has"].forEach(a=>{Object.defineProperty(t,a+i,{value:function(t,n,i){return this[a].call(this,e,t,n,i)},configurable:!0})}),a[n]=!0}}return ee.isArray(e)?e.forEach(n):n(e),this}}function eB(e,a){let t=this||eA,n=a||t,i=eM.from(n.headers),o=n.data;return ee.forEach(e,function(e){o=e.call(t,o,i.normalize(),a?a.status:void 0)}),i.normalize(),o}function eq(e){return!!(e&&e.__CANCEL__)}function eU(e,a,t){ea.call(this,null==e?"canceled":e,ea.ERR_CANCELED,a,t),this.name="CanceledError"}function eI(e,a,t){let n=t.config.validateStatus;!t.status||!n||n(t.status)?e(t):a(new ea("Request failed with status code "+t.status,[ea.ERR_BAD_REQUEST,ea.ERR_BAD_RESPONSE][Math.floor(t.status/100)-4],t.config,t.request,t))}function eH(e,a,t){let n=!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(a);return e&&(n||!1==t)?a?e.replace(/\/?\/$/,"")+"/"+a.replace(/^\/+/,""):e:a}eM.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),ee.reduceDescriptors(eM.prototype,({value:e},a)=>{let t=a[0].toUpperCase()+a.slice(1);return{get:()=>e,set(e){this[t]=e}}}),ee.freezeMethods(eM),ee.inherits(eU,ea,{__CANCEL__:!0});var eW=t(7065),e$=t(1630),eG=t(5591),eV=t(8354),eY=t(6373);let eK=require("zlib"),eJ="1.10.0";function eX(e){let a=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return a&&a[1]||""}let eQ=/^(?:([^;]+);)?(?:[^;]+;)?(base64|),([\s\S]*)$/;var eZ=t(7910);let e0=Symbol("internals");class e1 extends eZ.Transform{constructor(e){super({readableHighWaterMark:(e=ee.toFlatObject(e,{maxRate:0,chunkSize:65536,minChunkSize:100,timeWindow:500,ticksRate:2,samplesCount:15},null,(e,a)=>!ee.isUndefined(a[e]))).chunkSize});let a=this[e0]={timeWindow:e.timeWindow,chunkSize:e.chunkSize,maxRate:e.maxRate,minChunkSize:e.minChunkSize,bytesSeen:0,isCaptured:!1,notifiedBytesLoaded:0,ts:Date.now(),bytes:0,onReadCallback:null};this.on("newListener",e=>{"progress"!==e||a.isCaptured||(a.isCaptured=!0)})}_read(e){let a=this[e0];return a.onReadCallback&&a.onReadCallback(),super._read(e)}_transform(e,a,t){let n=this[e0],i=n.maxRate,o=this.readableHighWaterMark,s=n.timeWindow,r=i/(1e3/s),c=!1!==n.minChunkSize?Math.max(n.minChunkSize,.01*r):0,p=(e,a)=>{let t=Buffer.byteLength(e);n.bytesSeen+=t,n.bytes+=t,n.isCaptured&&this.emit("progress",n.bytesSeen),this.push(e)?process.nextTick(a):n.onReadCallback=()=>{n.onReadCallback=null,process.nextTick(a)}},l=(e,a)=>{let t,l=Buffer.byteLength(e),u=null,d=o,m=0;if(i){let e=Date.now();(!n.ts||(m=e-n.ts)>=s)&&(n.ts=e,t=r-n.bytes,n.bytes=t<0?-t:0,m=0),t=r-n.bytes}if(i){if(t<=0)return setTimeout(()=>{a(null,e)},s-m);t<d&&(d=t)}d&&l>d&&l-d>c&&(u=e.subarray(d),e=e.subarray(0,d)),p(e,u?()=>{process.nextTick(a,null,u)}:a)};l(e,function e(a,n){if(a)return t(a);n?l(n,e):t(null)})}}let e2=require("events"),{asyncIterator:e3}=Symbol,e5=async function*(e){e.stream?yield*e.stream():e.arrayBuffer?yield await e.arrayBuffer():e[e3]?yield*e[e3]():yield e},e6=eO.ALPHABET.ALPHA_DIGIT+"-_",e4="function"==typeof TextEncoder?new TextEncoder:new eV.TextEncoder,e8=e4.encode("\r\n");class e9{constructor(e,a){let{escapeName:t}=this.constructor,n=ee.isString(a),i=`Content-Disposition: form-data; name="${t(e)}"${!n&&a.name?`; filename="${t(a.name)}"`:""}\r
`;n?a=e4.encode(String(a).replace(/\r?\n|\r\n?/g,"\r\n")):i+=`Content-Type: ${a.type||"application/octet-stream"}\r
`,this.headers=e4.encode(i+"\r\n"),this.contentLength=n?a.byteLength:a.size,this.size=this.headers.byteLength+this.contentLength+2,this.name=e,this.value=a}async *encode(){yield this.headers;let{value:e}=this;ee.isTypedArray(e)?yield e:yield*e5(e),yield e8}static escapeName(e){return String(e).replace(/[\r\n"]/g,e=>({"\r":"%0D","\n":"%0A",'"':"%22"})[e])}}let e7=(e,a,t)=>{let{tag:n="form-data-boundary",size:i=25,boundary:o=n+"-"+eO.generateString(i,e6)}=t||{};if(!ee.isFormData(e))throw TypeError("FormData instance required");if(o.length<1||o.length>70)throw Error("boundary must be 10-70 characters long");let s=e4.encode("--"+o+"\r\n"),r=e4.encode("--"+o+"--\r\n"),c=r.byteLength,p=Array.from(e.entries()).map(([e,a])=>{let t=new e9(e,a);return c+=t.size,t});c+=s.byteLength*p.length;let l={"Content-Type":`multipart/form-data; boundary=${o}`};return Number.isFinite(c=ee.toFiniteNumber(c))&&(l["Content-Length"]=c),a&&a(l),eZ.Readable.from(async function*(){for(let e of p)yield s,yield*e.encode();yield r}())};class ae extends eZ.Transform{__transform(e,a,t){this.push(e),t()}_transform(e,a,t){if(0!==e.length&&(this._transform=this.__transform,120!==e[0])){let e=Buffer.alloc(2);e[0]=120,e[1]=156,this.push(e,a)}this.__transform(e,a,t)}}let aa=(e,a)=>ee.isAsyncFn(e)?function(...t){let n=t.pop();e.apply(this,t).then(e=>{try{a?n(null,...a(e)):n(null,e)}catch(e){n(e)}},n)}:e,at=function(e,a){let t,n=Array(e=e||10),i=Array(e),o=0,s=0;return a=void 0!==a?a:1e3,function(r){let c=Date.now(),p=i[s];t||(t=c),n[o]=r,i[o]=c;let l=s,u=0;for(;l!==o;)u+=n[l++],l%=e;if((o=(o+1)%e)===s&&(s=(s+1)%e),c-t<a)return;let d=p&&c-p;return d?Math.round(1e3*u/d):void 0}},an=function(e,a){let t,n,i=0,o=1e3/a,s=(a,o=Date.now())=>{i=o,t=null,n&&(clearTimeout(n),n=null),e.apply(null,a)};return[(...e)=>{let a=Date.now(),r=a-i;r>=o?s(e,a):(t=e,n||(n=setTimeout(()=>{n=null,s(t)},o-r)))},()=>t&&s(t)]},ai=(e,a,t=3)=>{let n=0,i=at(50,250);return an(t=>{let o=t.loaded,s=t.lengthComputable?t.total:void 0,r=o-n,c=i(r);n=o,e({loaded:o,total:s,progress:s?o/s:void 0,bytes:r,rate:c||void 0,estimated:c&&s&&o<=s?(s-o)/c:void 0,event:t,lengthComputable:null!=s,[a?"download":"upload"]:!0})},t)},ao=(e,a)=>{let t=null!=e;return[n=>a[0]({lengthComputable:t,total:e,loaded:n}),a[1]]},as=e=>(...a)=>ee.asap(()=>e(...a)),ar={flush:eK.constants.Z_SYNC_FLUSH,finishFlush:eK.constants.Z_SYNC_FLUSH},ac={flush:eK.constants.BROTLI_OPERATION_FLUSH,finishFlush:eK.constants.BROTLI_OPERATION_FLUSH},ap=ee.isFunction(eK.createBrotliDecompress),{http:al,https:au}=eY,ad=/https:?/,am=eO.protocols.map(e=>e+":"),af=(e,[a,t])=>(e.on("end",t).on("error",t),a);function ax(e,a){e.beforeRedirects.proxy&&e.beforeRedirects.proxy(e),e.beforeRedirects.config&&e.beforeRedirects.config(e,a)}let ah="undefined"!=typeof process&&"process"===ee.kindOf(process),av=e=>new Promise((a,t)=>{let n,i,o=(e,a)=>{!i&&(i=!0,n&&n(e,a))},s=e=>{o(e,!0),t(e)};e(e=>{o(e),a(e)},s,e=>n=e).catch(s)}),ab=({address:e,family:a})=>{if(!ee.isString(e))throw TypeError("address must be a string");return{address:e,family:a||(0>e.indexOf(".")?6:4)}},ag=(e,a)=>ab(ee.isObject(e)?e:{address:e,family:a}),ay=ah&&function(e){return av(async function(a,t,n){let i,o,s,r,c,p,l,{data:u,lookup:d,family:m}=e,{responseType:f,responseEncoding:x}=e,h=e.method.toUpperCase(),v=!1;if(d){let e=aa(d,e=>ee.isArray(e)?e:[e]);d=(a,t,n)=>{e(a,t,(e,a,i)=>{if(e)return n(e);let o=ee.isArray(a)?a.map(e=>ag(e)):[ag(a,i)];t.all?n(e,o):n(e,o[0].address,o[0].family)})}}let b=new e2.EventEmitter,g=()=>{e.cancelToken&&e.cancelToken.unsubscribe(y),e.signal&&e.signal.removeEventListener("abort",y),b.removeAllListeners()};function y(a){b.emit("abort",!a||a.type?new eU(null,e,c):a)}n((e,a)=>{r=!0,a&&(v=!0,g())}),b.once("abort",t),(e.cancelToken||e.signal)&&(e.cancelToken&&e.cancelToken.subscribe(y),e.signal&&(e.signal.aborted?y():e.signal.addEventListener("abort",y)));let w=new URL(eH(e.baseURL,e.url,e.allowAbsoluteUrls),eO.hasBrowserEnv?eO.origin:void 0),k=w.protocol||am[0];if("data:"===k){let n;if("GET"!==h)return eI(a,t,{status:405,statusText:"method not allowed",headers:{},config:e});try{n=function(e,a,t){let n=t&&t.Blob||eO.classes.Blob,i=eX(e);if(void 0===a&&n&&(a=!0),"data"===i){e=i.length?e.slice(i.length+1):e;let t=eQ.exec(e);if(!t)throw new ea("Invalid URL",ea.ERR_INVALID_URL);let o=t[1],s=t[2],r=t[3],c=Buffer.from(decodeURIComponent(r),s?"base64":"utf8");if(a){if(!n)throw new ea("Blob is not supported",ea.ERR_NOT_SUPPORT);return new n([c],{type:o})}return c}throw new ea("Unsupported protocol "+i,ea.ERR_NOT_SUPPORT)}(e.url,"blob"===f,{Blob:e.env&&e.env.Blob})}catch(a){throw ea.from(a,ea.ERR_BAD_REQUEST,e)}return"text"===f?(n=n.toString(x),x&&"utf8"!==x||(n=ee.stripBOM(n))):"stream"===f&&(n=eZ.Readable.from(n)),eI(a,t,{data:n,status:200,statusText:"OK",headers:new eM,config:e})}if(-1===am.indexOf(k))return t(new ea("Unsupported protocol "+k,ea.ERR_BAD_REQUEST,e));let j=eM.from(e.headers).normalize();j.set("User-Agent","axios/"+eJ,!1);let{onUploadProgress:E,onDownloadProgress:S}=e,R=e.maxRate;if(ee.isSpecCompliantForm(u)){let e=j.getContentType(/boundary=([-_\w\d]{10,70})/i);u=e7(u,e=>{j.set(e)},{tag:`axios-${eJ}-boundary`,boundary:e&&e[1]||void 0})}else if(ee.isFormData(u)&&ee.isFunction(u.getHeaders)){if(j.set(u.getHeaders()),!j.hasContentLength())try{let e=await eV.promisify(u.getLength).call(u);Number.isFinite(e)&&e>=0&&j.setContentLength(e)}catch(e){}}else if(ee.isBlob(u)||ee.isFile(u))u.size&&j.setContentType(u.type||"application/octet-stream"),j.setContentLength(u.size||0),u=eZ.Readable.from(e5(u));else if(u&&!ee.isStream(u)){if(Buffer.isBuffer(u));else if(ee.isArrayBuffer(u))u=Buffer.from(new Uint8Array(u));else{if(!ee.isString(u))return t(new ea("Data after transformation must be a string, an ArrayBuffer, a Buffer, or a Stream",ea.ERR_BAD_REQUEST,e));u=Buffer.from(u,"utf-8")}if(j.setContentLength(u.length,!1),e.maxBodyLength>-1&&u.length>e.maxBodyLength)return t(new ea("Request body larger than maxBodyLength limit",ea.ERR_BAD_REQUEST,e))}let _=ee.toFiniteNumber(j.getContentLength());ee.isArray(R)?(i=R[0],o=R[1]):i=o=R,u&&(E||i)&&(ee.isStream(u)||(u=eZ.Readable.from(u,{objectMode:!1})),u=eZ.pipeline([u,new e1({maxRate:ee.toFiniteNumber(i)})],ee.noop),E&&u.on("progress",af(u,ao(_,ai(as(E),!1,3))))),e.auth&&(s=(e.auth.username||"")+":"+(e.auth.password||"")),!s&&w.username&&(s=w.username+":"+w.password),s&&j.delete("authorization");try{p=ef(w.pathname+w.search,e.params,e.paramsSerializer).replace(/^\?/,"")}catch(n){let a=Error(n.message);return a.config=e,a.url=e.url,a.exists=!0,t(a)}j.set("Accept-Encoding","gzip, compress, deflate"+(ap?", br":""),!1);let O={path:p,method:h,headers:j.toJSON(),agents:{http:e.httpAgent,https:e.httpsAgent},auth:s,protocol:k,family:m,beforeRedirect:ax,beforeRedirects:{}};ee.isUndefined(d)||(O.lookup=d),e.socketPath?O.socketPath=e.socketPath:(O.hostname=w.hostname.startsWith("[")?w.hostname.slice(1,-1):w.hostname,O.port=w.port,function e(a,t,n){let i=t;if(!i&&!1!==i){let e=eW.getProxyForUrl(n);e&&(i=new URL(e))}if(i){if(i.username&&(i.auth=(i.username||"")+":"+(i.password||"")),i.auth){(i.auth.username||i.auth.password)&&(i.auth=(i.auth.username||"")+":"+(i.auth.password||""));let e=Buffer.from(i.auth,"utf8").toString("base64");a.headers["Proxy-Authorization"]="Basic "+e}a.headers.host=a.hostname+(a.port?":"+a.port:"");let e=i.hostname||i.host;a.hostname=e,a.host=e,a.port=i.port,a.path=n,i.protocol&&(a.protocol=i.protocol.includes(":")?i.protocol:`${i.protocol}:`)}a.beforeRedirects.proxy=function(a){e(a,t,a.href)}}(O,e.proxy,k+"//"+w.hostname+(w.port?":"+w.port:"")+O.path));let T=ad.test(O.protocol);if(O.agent=T?e.httpsAgent:e.httpAgent,e.transport?l=e.transport:0===e.maxRedirects?l=T?eG:e$:(e.maxRedirects&&(O.maxRedirects=e.maxRedirects),e.beforeRedirect&&(O.beforeRedirects.config=e.beforeRedirect),l=T?au:al),e.maxBodyLength>-1?O.maxBodyLength=e.maxBodyLength:O.maxBodyLength=1/0,e.insecureHTTPParser&&(O.insecureHTTPParser=e.insecureHTTPParser),c=l.request(O,function(n){if(c.destroyed)return;let i=[n],s=+n.headers["content-length"];if(S||o){let e=new e1({maxRate:ee.toFiniteNumber(o)});S&&e.on("progress",af(e,ao(s,ai(as(S),!0,3)))),i.push(e)}let r=n,p=n.req||c;if(!1!==e.decompress&&n.headers["content-encoding"])switch(("HEAD"===h||204===n.statusCode)&&delete n.headers["content-encoding"],(n.headers["content-encoding"]||"").toLowerCase()){case"gzip":case"x-gzip":case"compress":case"x-compress":i.push(eK.createUnzip(ar)),delete n.headers["content-encoding"];break;case"deflate":i.push(new ae),i.push(eK.createUnzip(ar)),delete n.headers["content-encoding"];break;case"br":ap&&(i.push(eK.createBrotliDecompress(ac)),delete n.headers["content-encoding"])}r=i.length>1?eZ.pipeline(i,ee.noop):i[0];let l=eZ.finished(r,()=>{l(),g()}),u={status:n.statusCode,statusText:n.statusMessage,headers:new eM(n.headers),config:e,request:p};if("stream"===f)u.data=r,eI(a,t,u);else{let n=[],i=0;r.on("data",function(a){n.push(a),i+=a.length,e.maxContentLength>-1&&i>e.maxContentLength&&(v=!0,r.destroy(),t(new ea("maxContentLength size of "+e.maxContentLength+" exceeded",ea.ERR_BAD_RESPONSE,e,p)))}),r.on("aborted",function(){if(v)return;let a=new ea("stream has been aborted",ea.ERR_BAD_RESPONSE,e,p);r.destroy(a),t(a)}),r.on("error",function(a){c.destroyed||t(ea.from(a,null,e,p))}),r.on("end",function(){try{let e=1===n.length?n[0]:Buffer.concat(n);"arraybuffer"!==f&&(e=e.toString(x),x&&"utf8"!==x||(e=ee.stripBOM(e))),u.data=e}catch(a){return t(ea.from(a,null,e,u.request,u))}eI(a,t,u)})}b.once("abort",e=>{r.destroyed||(r.emit("error",e),r.destroy())})}),b.once("abort",e=>{t(e),c.destroy(e)}),c.on("error",function(a){t(ea.from(a,null,e,c))}),c.on("socket",function(e){e.setKeepAlive(!0,6e4)}),e.timeout){let a=parseInt(e.timeout,10);if(Number.isNaN(a))return void t(new ea("error trying to parse `config.timeout` to int",ea.ERR_BAD_OPTION_VALUE,e,c));c.setTimeout(a,function(){if(r)return;let a=e.timeout?"timeout of "+e.timeout+"ms exceeded":"timeout exceeded",n=e.transitional||eh;e.timeoutErrorMessage&&(a=e.timeoutErrorMessage),t(new ea(a,n.clarifyTimeoutError?ea.ETIMEDOUT:ea.ECONNABORTED,e,c)),y()})}if(ee.isStream(u)){let a=!1,t=!1;u.on("end",()=>{a=!0}),u.once("error",e=>{t=!0,c.destroy(e)}),u.on("close",()=>{a||t||y(new eU("Request stream has been aborted",e,c))}),u.pipe(c)}else c.end(u)})},aw=eO.hasStandardBrowserEnv?((e,a)=>t=>(t=new URL(t,eO.origin),e.protocol===t.protocol&&e.host===t.host&&(a||e.port===t.port)))(new URL(eO.origin),eO.navigator&&/(msie|trident)/i.test(eO.navigator.userAgent)):()=>!0,ak=eO.hasStandardBrowserEnv?{write(e,a,t,n,i,o){let s=[e+"="+encodeURIComponent(a)];ee.isNumber(t)&&s.push("expires="+new Date(t).toGMTString()),ee.isString(n)&&s.push("path="+n),ee.isString(i)&&s.push("domain="+i),!0===o&&s.push("secure"),document.cookie=s.join("; ")},read(e){let a=document.cookie.match(RegExp("(^|;\\s*)("+e+")=([^;]*)"));return a?decodeURIComponent(a[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}},aj=e=>e instanceof eM?{...e}:e;function aE(e,a){a=a||{};let t={};function n(e,a,t,n){return ee.isPlainObject(e)&&ee.isPlainObject(a)?ee.merge.call({caseless:n},e,a):ee.isPlainObject(a)?ee.merge({},a):ee.isArray(a)?a.slice():a}function i(e,a,t,i){return ee.isUndefined(a)?ee.isUndefined(e)?void 0:n(void 0,e,t,i):n(e,a,t,i)}function o(e,a){if(!ee.isUndefined(a))return n(void 0,a)}function s(e,a){return ee.isUndefined(a)?ee.isUndefined(e)?void 0:n(void 0,e):n(void 0,a)}function r(t,i,o){return o in a?n(t,i):o in e?n(void 0,t):void 0}let c={url:o,method:o,data:o,baseURL:s,transformRequest:s,transformResponse:s,paramsSerializer:s,timeout:s,timeoutMessage:s,withCredentials:s,withXSRFToken:s,adapter:s,responseType:s,xsrfCookieName:s,xsrfHeaderName:s,onUploadProgress:s,onDownloadProgress:s,decompress:s,maxContentLength:s,maxBodyLength:s,beforeRedirect:s,transport:s,httpAgent:s,httpsAgent:s,cancelToken:s,socketPath:s,responseEncoding:s,validateStatus:r,headers:(e,a,t)=>i(aj(e),aj(a),t,!0)};return ee.forEach(Object.keys(Object.assign({},e,a)),function(n){let o=c[n]||i,s=o(e[n],a[n],n);ee.isUndefined(s)&&o!==r||(t[n]=s)}),t}let aS=e=>{let a,t=aE({},e),{data:n,withXSRFToken:i,xsrfHeaderName:o,xsrfCookieName:s,headers:r,auth:c}=t;if(t.headers=r=eM.from(r),t.url=ef(eH(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),c&&r.set("Authorization","Basic "+btoa((c.username||"")+":"+(c.password?unescape(encodeURIComponent(c.password)):""))),ee.isFormData(n)){if(eO.hasStandardBrowserEnv||eO.hasStandardBrowserWebWorkerEnv)r.setContentType(void 0);else if(!1!==(a=r.getContentType())){let[e,...t]=a?a.split(";").map(e=>e.trim()).filter(Boolean):[];r.setContentType([e||"multipart/form-data",...t].join("; "))}}if(eO.hasStandardBrowserEnv&&(i&&ee.isFunction(i)&&(i=i(t)),i||!1!==i&&aw(t.url))){let e=o&&s&&ak.read(s);e&&r.set(o,e)}return t},aR="undefined"!=typeof XMLHttpRequest&&function(e){return new Promise(function(a,t){let n,i,o,s,r,c=aS(e),p=c.data,l=eM.from(c.headers).normalize(),{responseType:u,onUploadProgress:d,onDownloadProgress:m}=c;function f(){s&&s(),r&&r(),c.cancelToken&&c.cancelToken.unsubscribe(n),c.signal&&c.signal.removeEventListener("abort",n)}let x=new XMLHttpRequest;function h(){if(!x)return;let n=eM.from("getAllResponseHeaders"in x&&x.getAllResponseHeaders());eI(function(e){a(e),f()},function(e){t(e),f()},{data:u&&"text"!==u&&"json"!==u?x.response:x.responseText,status:x.status,statusText:x.statusText,headers:n,config:e,request:x}),x=null}x.open(c.method.toUpperCase(),c.url,!0),x.timeout=c.timeout,"onloadend"in x?x.onloadend=h:x.onreadystatechange=function(){x&&4===x.readyState&&(0!==x.status||x.responseURL&&0===x.responseURL.indexOf("file:"))&&setTimeout(h)},x.onabort=function(){x&&(t(new ea("Request aborted",ea.ECONNABORTED,e,x)),x=null)},x.onerror=function(){t(new ea("Network Error",ea.ERR_NETWORK,e,x)),x=null},x.ontimeout=function(){let a=c.timeout?"timeout of "+c.timeout+"ms exceeded":"timeout exceeded",n=c.transitional||eh;c.timeoutErrorMessage&&(a=c.timeoutErrorMessage),t(new ea(a,n.clarifyTimeoutError?ea.ETIMEDOUT:ea.ECONNABORTED,e,x)),x=null},void 0===p&&l.setContentType(null),"setRequestHeader"in x&&ee.forEach(l.toJSON(),function(e,a){x.setRequestHeader(a,e)}),ee.isUndefined(c.withCredentials)||(x.withCredentials=!!c.withCredentials),u&&"json"!==u&&(x.responseType=c.responseType),m&&([o,r]=ai(m,!0),x.addEventListener("progress",o)),d&&x.upload&&([i,s]=ai(d),x.upload.addEventListener("progress",i),x.upload.addEventListener("loadend",s)),(c.cancelToken||c.signal)&&(n=a=>{x&&(t(!a||a.type?new eU(null,e,x):a),x.abort(),x=null)},c.cancelToken&&c.cancelToken.subscribe(n),c.signal&&(c.signal.aborted?n():c.signal.addEventListener("abort",n)));let v=eX(c.url);if(v&&-1===eO.protocols.indexOf(v))return void t(new ea("Unsupported protocol "+v+":",ea.ERR_BAD_REQUEST,e));x.send(p||null)})},a_=(e,a)=>{let{length:t}=e=e?e.filter(Boolean):[];if(a||t){let t,n=new AbortController,i=function(e){if(!t){t=!0,s();let a=e instanceof Error?e:this.reason;n.abort(a instanceof ea?a:new eU(a instanceof Error?a.message:a))}},o=a&&setTimeout(()=>{o=null,i(new ea(`timeout ${a} of ms exceeded`,ea.ETIMEDOUT))},a),s=()=>{e&&(o&&clearTimeout(o),o=null,e.forEach(e=>{e.unsubscribe?e.unsubscribe(i):e.removeEventListener("abort",i)}),e=null)};e.forEach(e=>e.addEventListener("abort",i));let{signal:r}=n;return r.unsubscribe=()=>ee.asap(s),r}},aO=function*(e,a){let t,n=e.byteLength;if(!a||n<a)return void(yield e);let i=0;for(;i<n;)t=i+a,yield e.slice(i,t),i=t},aT=async function*(e,a){for await(let t of aA(e))yield*aO(t,a)},aA=async function*(e){if(e[Symbol.asyncIterator])return void(yield*e);let a=e.getReader();try{for(;;){let{done:e,value:t}=await a.read();if(e)break;yield t}}finally{await a.cancel()}},aC=(e,a,t,n)=>{let i,o=aT(e,a),s=0,r=e=>{!i&&(i=!0,n&&n(e))};return new ReadableStream({async pull(e){try{let{done:a,value:n}=await o.next();if(a){r(),e.close();return}let i=n.byteLength;if(t){let e=s+=i;t(e)}e.enqueue(new Uint8Array(n))}catch(e){throw r(e),e}},cancel:e=>(r(e),o.return())},{highWaterMark:2})},aF="function"==typeof fetch&&"function"==typeof Request&&"function"==typeof Response,aD=aF&&"function"==typeof ReadableStream,aP=aF&&("function"==typeof TextEncoder?(r=new TextEncoder,e=>r.encode(e)):async e=>new Uint8Array(await new Response(e).arrayBuffer())),aN=(e,...a)=>{try{return!!e(...a)}catch(e){return!1}},az=aD&&aN(()=>{let e=!1,a=new Request(eO.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!a}),aL=aD&&aN(()=>ee.isReadableStream(new Response("").body)),aM={stream:aL&&(e=>e.body)};aF&&(l=new Response,["text","arrayBuffer","blob","formData","stream"].forEach(e=>{aM[e]||(aM[e]=ee.isFunction(l[e])?a=>a[e]():(a,t)=>{throw new ea(`Response type '${e}' is not supported`,ea.ERR_NOT_SUPPORT,t)})}));let aB=async e=>{if(null==e)return 0;if(ee.isBlob(e))return e.size;if(ee.isSpecCompliantForm(e)){let a=new Request(eO.origin,{method:"POST",body:e});return(await a.arrayBuffer()).byteLength}return ee.isArrayBufferView(e)||ee.isArrayBuffer(e)?e.byteLength:(ee.isURLSearchParams(e)&&(e+=""),ee.isString(e))?(await aP(e)).byteLength:void 0},aq=async(e,a)=>{let t=ee.toFiniteNumber(e.getContentLength());return null==t?aB(a):t},aU={http:ay,xhr:aR,fetch:aF&&(async e=>{let a,t,{url:n,method:i,data:o,signal:s,cancelToken:r,timeout:c,onDownloadProgress:p,onUploadProgress:l,responseType:u,headers:d,withCredentials:m="same-origin",fetchOptions:f}=aS(e);u=u?(u+"").toLowerCase():"text";let x=a_([s,r&&r.toAbortSignal()],c),h=x&&x.unsubscribe&&(()=>{x.unsubscribe()});try{if(l&&az&&"get"!==i&&"head"!==i&&0!==(t=await aq(d,o))){let e,a=new Request(n,{method:"POST",body:o,duplex:"half"});if(ee.isFormData(o)&&(e=a.headers.get("content-type"))&&d.setContentType(e),a.body){let[e,n]=ao(t,ai(as(l)));o=aC(a.body,65536,e,n)}}ee.isString(m)||(m=m?"include":"omit");let s="credentials"in Request.prototype;a=new Request(n,{...f,signal:x,method:i.toUpperCase(),headers:d.normalize().toJSON(),body:o,duplex:"half",credentials:s?m:void 0});let r=await fetch(a,f),c=aL&&("stream"===u||"response"===u);if(aL&&(p||c&&h)){let e={};["status","statusText","headers"].forEach(a=>{e[a]=r[a]});let a=ee.toFiniteNumber(r.headers.get("content-length")),[t,n]=p&&ao(a,ai(as(p),!0))||[];r=new Response(aC(r.body,65536,t,()=>{n&&n(),h&&h()}),e)}u=u||"text";let v=await aM[ee.findKey(aM,u)||"text"](r,e);return!c&&h&&h(),await new Promise((t,n)=>{eI(t,n,{data:v,headers:eM.from(r.headers),status:r.status,statusText:r.statusText,config:e,request:a})})}catch(t){if(h&&h(),t&&"TypeError"===t.name&&/Load failed|fetch/i.test(t.message))throw Object.assign(new ea("Network Error",ea.ERR_NETWORK,e,a),{cause:t.cause||t});throw ea.from(t,t&&t.code,e,a)}})};ee.forEach(aU,(e,a)=>{if(e){try{Object.defineProperty(e,"name",{value:a})}catch(e){}Object.defineProperty(e,"adapterName",{value:a})}});let aI=e=>`- ${e}`,aH=e=>ee.isFunction(e)||null===e||!1===e,aW={getAdapter:e=>{let a,t,{length:n}=e=ee.isArray(e)?e:[e],i={};for(let o=0;o<n;o++){let n;if(t=a=e[o],!aH(a)&&void 0===(t=aU[(n=String(a)).toLowerCase()]))throw new ea(`Unknown adapter '${n}'`);if(t)break;i[n||"#"+o]=t}if(!t){let e=Object.entries(i).map(([e,a])=>`adapter ${e} `+(!1===a?"is not supported by the environment":"is not available in the build"));throw new ea("There is no suitable adapter to dispatch the request "+(n?e.length>1?"since :\n"+e.map(aI).join("\n"):" "+aI(e[0]):"as no adapter specified"),"ERR_NOT_SUPPORT")}return t}};function a$(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new eU(null,e)}function aG(e){return a$(e),e.headers=eM.from(e.headers),e.data=eB.call(e,e.transformRequest),-1!==["post","put","patch"].indexOf(e.method)&&e.headers.setContentType("application/x-www-form-urlencoded",!1),aW.getAdapter(e.adapter||eA.adapter)(e).then(function(a){return a$(e),a.data=eB.call(e,e.transformResponse,a),a.headers=eM.from(a.headers),a},function(a){return!eq(a)&&(a$(e),a&&a.response&&(a.response.data=eB.call(e,e.transformResponse,a.response),a.response.headers=eM.from(a.response.headers))),Promise.reject(a)})}let aV={};["object","boolean","number","function","string","symbol"].forEach((e,a)=>{aV[e]=function(t){return typeof t===e||"a"+(a<1?"n ":" ")+e}});let aY={};aV.transitional=function(e,a,t){function n(e,a){return"[Axios v"+eJ+"] Transitional option '"+e+"'"+a+(t?". "+t:"")}return(t,i,o)=>{if(!1===e)throw new ea(n(i," has been removed"+(a?" in "+a:"")),ea.ERR_DEPRECATED);return a&&!aY[i]&&(aY[i]=!0,console.warn(n(i," has been deprecated since v"+a+" and will be removed in the near future"))),!e||e(t,i,o)}},aV.spelling=function(e){return(a,t)=>(console.warn(`${t} is likely a misspelling of ${e}`),!0)};let aK={assertOptions:function(e,a,t){if("object"!=typeof e)throw new ea("options must be an object",ea.ERR_BAD_OPTION_VALUE);let n=Object.keys(e),i=n.length;for(;i-- >0;){let o=n[i],s=a[o];if(s){let a=e[o],t=void 0===a||s(a,o,e);if(!0!==t)throw new ea("option "+o+" must be "+t,ea.ERR_BAD_OPTION_VALUE);continue}if(!0!==t)throw new ea("Unknown option "+o,ea.ERR_BAD_OPTION)}},validators:aV},aJ=aK.validators;class aX{constructor(e){this.defaults=e||{},this.interceptors={request:new ex,response:new ex}}async request(e,a){try{return await this._request(e,a)}catch(e){if(e instanceof Error){let a={};Error.captureStackTrace?Error.captureStackTrace(a):a=Error();let t=a.stack?a.stack.replace(/^.+\n/,""):"";try{e.stack?t&&!String(e.stack).endsWith(t.replace(/^.+\n.+\n/,""))&&(e.stack+="\n"+t):e.stack=t}catch(e){}}throw e}}_request(e,a){let t,n;"string"==typeof e?(a=a||{}).url=e:a=e||{};let{transitional:i,paramsSerializer:o,headers:s}=a=aE(this.defaults,a);void 0!==i&&aK.assertOptions(i,{silentJSONParsing:aJ.transitional(aJ.boolean),forcedJSONParsing:aJ.transitional(aJ.boolean),clarifyTimeoutError:aJ.transitional(aJ.boolean)},!1),null!=o&&(ee.isFunction(o)?a.paramsSerializer={serialize:o}:aK.assertOptions(o,{encode:aJ.function,serialize:aJ.function},!0)),void 0!==a.allowAbsoluteUrls||(void 0!==this.defaults.allowAbsoluteUrls?a.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:a.allowAbsoluteUrls=!0),aK.assertOptions(a,{baseUrl:aJ.spelling("baseURL"),withXsrfToken:aJ.spelling("withXSRFToken")},!0),a.method=(a.method||this.defaults.method||"get").toLowerCase();let r=s&&ee.merge(s.common,s[a.method]);s&&ee.forEach(["delete","get","head","post","put","patch","common"],e=>{delete s[e]}),a.headers=eM.concat(r,s);let c=[],p=!0;this.interceptors.request.forEach(function(e){("function"!=typeof e.runWhen||!1!==e.runWhen(a))&&(p=p&&e.synchronous,c.unshift(e.fulfilled,e.rejected))});let l=[];this.interceptors.response.forEach(function(e){l.push(e.fulfilled,e.rejected)});let u=0;if(!p){let e=[aG.bind(this),void 0];for(e.unshift.apply(e,c),e.push.apply(e,l),n=e.length,t=Promise.resolve(a);u<n;)t=t.then(e[u++],e[u++]);return t}n=c.length;let d=a;for(u=0;u<n;){let e=c[u++],a=c[u++];try{d=e(d)}catch(e){a.call(this,e);break}}try{t=aG.call(this,d)}catch(e){return Promise.reject(e)}for(u=0,n=l.length;u<n;)t=t.then(l[u++],l[u++]);return t}getUri(e){return ef(eH((e=aE(this.defaults,e)).baseURL,e.url,e.allowAbsoluteUrls),e.params,e.paramsSerializer)}}ee.forEach(["delete","get","head","options"],function(e){aX.prototype[e]=function(a,t){return this.request(aE(t||{},{method:e,url:a,data:(t||{}).data}))}}),ee.forEach(["post","put","patch"],function(e){function a(a){return function(t,n,i){return this.request(aE(i||{},{method:e,headers:a?{"Content-Type":"multipart/form-data"}:{},url:t,data:n}))}}aX.prototype[e]=a(),aX.prototype[e+"Form"]=a(!0)});class aQ{constructor(e){let a;if("function"!=typeof e)throw TypeError("executor must be a function.");this.promise=new Promise(function(e){a=e});let t=this;this.promise.then(e=>{if(!t._listeners)return;let a=t._listeners.length;for(;a-- >0;)t._listeners[a](e);t._listeners=null}),this.promise.then=e=>{let a,n=new Promise(e=>{t.subscribe(e),a=e}).then(e);return n.cancel=function(){t.unsubscribe(a)},n},e(function(e,n,i){t.reason||(t.reason=new eU(e,n,i),a(t.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){if(this.reason)return void e(this.reason);this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;let a=this._listeners.indexOf(e);-1!==a&&this._listeners.splice(a,1)}toAbortSignal(){let e=new AbortController,a=a=>{e.abort(a)};return this.subscribe(a),e.signal.unsubscribe=()=>this.unsubscribe(a),e.signal}static source(){let e;return{token:new aQ(function(a){e=a}),cancel:e}}}let aZ={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(aZ).forEach(([e,a])=>{aZ[a]=e});let a0=function e(a){let t=new aX(a),n=v(aX.prototype.request,t);return ee.extend(n,aX.prototype,t,{allOwnKeys:!0}),ee.extend(n,t,null,{allOwnKeys:!0}),n.create=function(t){return e(aE(a,t))},n}(eA);a0.Axios=aX,a0.CanceledError=eU,a0.CancelToken=aQ,a0.isCancel=eq,a0.VERSION=eJ,a0.toFormData=ep,a0.AxiosError=ea,a0.Cancel=a0.CanceledError,a0.all=function(e){return Promise.all(e)},a0.spread=function(e){return function(a){return e.apply(null,a)}},a0.isAxiosError=function(e){return ee.isObject(e)&&!0===e.isAxiosError},a0.mergeConfig=aE,a0.AxiosHeaders=eM,a0.formToJSON=e=>eT(ee.isHTMLForm(e)?new FormData(e):e),a0.getAdapter=aW.getAdapter,a0.HttpStatusCode=aZ,a0.default=a0;var a1=t(3876);let a2={angelOne:{baseUrl:"https://apiconnect.angelone.in",version:"v1",endpoints:{login:"/rest/auth/angelbroking/user/v1/loginByPassword",profile:"/rest/secure/angelbroking/user/v1/getProfile",historicalData:"/rest/secure/angelbroking/historical/v1/getCandleData",ltp:"/rest/secure/angelbroking/order/v1/getLtpData",logout:"/rest/secure/angelbroking/user/v1/logout"}},rateLimit:{requestsPerSecond:10,requestsPerMinute:100}},a3={types:{GOLD:{name:"Gold",symbol:"GOLD",description:"Gold prices in INR per 10 grams"},FD:{name:"Fixed Deposit",symbol:"FD",description:"Average FD rates from major banks",defaultRate:6.5},NIFTY:{name:"Nifty 50",symbol:"NIFTY",description:"NSE Nifty 50 Index",token:"********"}}},a5={precision:{currency:2,percentage:2,cagr:2}};function a6(e){let a=["password","token","secret","key","auth","credential","totp"],t={};return Object.keys(e).forEach(n=>{let i=n.toLowerCase();a.some(e=>i.includes(e))?t[n]="[REDACTED]":"object"==typeof e[n]&&null!==e[n]?t[n]=a6(e[n]):t[n]=e[n]}),t}class a4{constructor(e,a){this.requests=[],this.maxRequests=e,this.timeWindow=1e3*a}isAllowed(){let e=Date.now();return this.requests=this.requests.filter(a=>e-a<this.timeWindow),this.requests.length<this.maxRequests&&(this.requests.push(e),!0)}getTimeUntilReset(){if(0===this.requests.length)return 0;let e=Math.min(...this.requests);return Math.max(0,this.timeWindow-(Date.now()-e))}}class a8{constructor(e){this.jwtToken=null,this.refreshToken=null,this.feedToken=null,this.apiKey=e.apiKey,this.clientId=e.clientId,this.password=e.password,this.totpSecret=e.totpSecret,this.rateLimiter=new a4(a2.rateLimit.requestsPerSecond,1),this.axiosInstance=a0.create({baseURL:a2.angelOne.baseUrl,timeout:3e4,headers:{"Content-Type":"application/json",Accept:"application/json","X-UserType":"USER","X-SourceID":"WEB","X-ClientLocalIP":"127.0.0.1","X-ClientPublicIP":"127.0.0.1","X-MACAddress":"00:00:00:00:00:00","X-PrivateKey":this.apiKey}}),this.axiosInstance.interceptors.request.use(async e=>{for(;!this.rateLimiter.isAllowed();){let e=this.rateLimiter.getTimeUntilReset();await new Promise(a=>setTimeout(a,e))}return this.jwtToken&&(e.headers.Authorization=`Bearer ${this.jwtToken}`),e},e=>Promise.reject(e)),this.axiosInstance.interceptors.response.use(e=>e,async e=>{if(e.response?.status===401&&this.refreshToken)try{return await this.refreshAuthToken(),this.axiosInstance.request(e.config)}catch{throw this.clearTokens(),Error("Authentication failed. Please login again.")}return Promise.reject(e)})}generateTOTP(){return a1.authenticator.generate(this.totpSecret)}clearTokens(){this.jwtToken=null,this.refreshToken=null,this.feedToken=null}async login(){try{let e=this.generateTOTP(),a={clientcode:this.clientId,password:this.password,totp:e};console.log("Attempting login with:",a6(a));let t=await this.axiosInstance.post(a2.angelOne.endpoints.login,a);if(t.data.status&&t.data.data)return this.jwtToken=t.data.data.jwtToken,this.refreshToken=t.data.data.refreshToken,this.feedToken=t.data.data.feedToken,{success:!0,message:"Login successful"};return{success:!1,message:t.data.message||"Login failed"}}catch(e){return console.error("Login error:",a6({error:e})),{success:!1,message:e instanceof Error?e.message:"Login failed"}}}async refreshAuthToken(){if(!this.refreshToken)throw Error("No refresh token available");let e=await this.axiosInstance.post("/rest/auth/angelbroking/jwt/v1/generateTokens",{refreshToken:this.refreshToken});if(e.data.status&&e.data.data)this.jwtToken=e.data.data.jwtToken,this.refreshToken=e.data.data.refreshToken;else throw Error("Token refresh failed")}isAuthenticated(){return null!==this.jwtToken}async getHistoricalData(e){if(!this.isAuthenticated())throw Error("Not authenticated. Please login first.");try{let a=await this.axiosInstance.post(a2.angelOne.endpoints.historicalData,e);if(a.data.status&&a.data.data)return a.data.data.map(e=>({date:new Date(e[0]),open:e[1],high:e[2],low:e[3],close:e[4],volume:e[5]}));throw Error(a.data.message||"Failed to fetch historical data")}catch(a){throw console.error("Historical data error:",a6({error:a,request:e})),a}}async getCurrentPrice(e){if(!this.isAuthenticated())throw Error("Not authenticated. Please login first.");try{let a=await this.axiosInstance.post(a2.angelOne.endpoints.ltp,e);if(a.data.status&&a.data.data){let e=a.data.data;return{symbol:e.tradingsymbol,name:e.tradingsymbol,exchange:e.exchange,token:e.symboltoken,currentPrice:e.ltp,lastUpdated:new Date}}throw Error(a.data.message||"Failed to fetch current price")}catch(a){throw console.error("Current price error:",a6({error:a,request:e})),a}}async logout(){if(!this.isAuthenticated())return{success:!0,message:"Already logged out"};try{return await this.axiosInstance.post(a2.angelOne.endpoints.logout,{clientcode:this.clientId}),this.clearTokens(),{success:!0,message:"Logout successful"}}catch(e){return console.error("Logout error:",a6({error:e})),this.clearTokens(),{success:!1,message:e instanceof Error?e.message:"Logout failed"}}}}let a9={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}};function a7(e){return (a={})=>{let t=a.width?String(a.width):e.defaultWidth;return e.formats[t]||e.formats[e.defaultWidth]}}let te={date:a7({formats:{full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},defaultWidth:"full"}),time:a7({formats:{full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},defaultWidth:"full"}),dateTime:a7({formats:{full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},ta={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"};function tt(e){return(a,t)=>{let n;if("formatting"===(t?.context?String(t.context):"standalone")&&e.formattingValues){let a=e.defaultFormattingWidth||e.defaultWidth,i=t?.width?String(t.width):a;n=e.formattingValues[i]||e.formattingValues[a]}else{let a=e.defaultWidth,i=t?.width?String(t.width):e.defaultWidth;n=e.values[i]||e.values[a]}return n[e.argumentCallback?e.argumentCallback(a):a]}}function tn(e){return(a,t={})=>{let n,i=t.width,o=i&&e.matchPatterns[i]||e.matchPatterns[e.defaultMatchWidth],s=a.match(o);if(!s)return null;let r=s[0],c=i&&e.parsePatterns[i]||e.parsePatterns[e.defaultParseWidth],p=Array.isArray(c)?function(e,a){for(let t=0;t<e.length;t++)if(a(e[t]))return t}(c,e=>e.test(r)):function(e,a){for(let t in e)if(Object.prototype.hasOwnProperty.call(e,t)&&a(e[t]))return t}(c,e=>e.test(r));return n=e.valueCallback?e.valueCallback(p):p,{value:n=t.valueCallback?t.valueCallback(n):n,rest:a.slice(r.length)}}}let ti={code:"en-US",formatDistance:(e,a,t)=>{let n,i=a9[e];if(n="string"==typeof i?i:1===a?i.one:i.other.replace("{{count}}",a.toString()),t?.addSuffix)if(t.comparison&&t.comparison>0)return"in "+n;else return n+" ago";return n},formatLong:te,formatRelative:(e,a,t,n)=>ta[e],localize:{ordinalNumber:(e,a)=>{let t=Number(e),n=t%100;if(n>20||n<10)switch(n%10){case 1:return t+"st";case 2:return t+"nd";case 3:return t+"rd"}return t+"th"},era:tt({values:{narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},defaultWidth:"wide"}),quarter:tt({values:{narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},defaultWidth:"wide",argumentCallback:e=>e-1}),month:tt({values:{narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},defaultWidth:"wide"}),day:tt({values:{narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},defaultWidth:"wide"}),dayPeriod:tt({values:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},defaultWidth:"wide",formattingValues:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},defaultFormattingWidth:"wide"})},match:{ordinalNumber:function(e){return(a,t={})=>{let n=a.match(e.matchPattern);if(!n)return null;let i=n[0],o=a.match(e.parsePattern);if(!o)return null;let s=e.valueCallback?e.valueCallback(o[0]):o[0];return{value:s=t.valueCallback?t.valueCallback(s):s,rest:a.slice(i.length)}}}({matchPattern:/^(\d+)(th|st|nd|rd)?/i,parsePattern:/\d+/i,valueCallback:e=>parseInt(e,10)}),era:tn({matchPatterns:{narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^b/i,/^(a|c)/i]},defaultParseWidth:"any"}),quarter:tn({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:e=>e+1}),month:tn({matchPatterns:{narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:tn({matchPatterns:{narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},defaultParseWidth:"any"}),dayPeriod:tn({matchPatterns:{narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},defaultParseWidth:"any"})},options:{weekStartsOn:0,firstWeekContainsDate:1}},to={},ts=Symbol.for("constructDateFrom");function tr(e,a){return"function"==typeof e?e(a):e&&"object"==typeof e&&ts in e?e[ts](a):e instanceof Date?new e.constructor(a):new Date(a)}function tc(e,a){return tr(a||e,e)}function tp(e){let a=tc(e),t=new Date(Date.UTC(a.getFullYear(),a.getMonth(),a.getDate(),a.getHours(),a.getMinutes(),a.getSeconds(),a.getMilliseconds()));return t.setUTCFullYear(a.getFullYear()),e-t}function tl(e,a){let t=tc(e,a?.in);return t.setHours(0,0,0,0),t}function tu(e,a){let t=a?.weekStartsOn??a?.locale?.options?.weekStartsOn??to.weekStartsOn??to.locale?.options?.weekStartsOn??0,n=tc(e,a?.in),i=n.getDay();return n.setDate(n.getDate()-(7*(i<t)+i-t)),n.setHours(0,0,0,0),n}function td(e,a){return tu(e,{...a,weekStartsOn:1})}function tm(e,a){let t=tc(e,a?.in),n=t.getFullYear(),i=tr(t,0);i.setFullYear(n+1,0,4),i.setHours(0,0,0,0);let o=td(i),s=tr(t,0);s.setFullYear(n,0,4),s.setHours(0,0,0,0);let r=td(s);return t.getTime()>=o.getTime()?n+1:t.getTime()>=r.getTime()?n:n-1}function tf(e,a){let t=tc(e,a?.in),n=t.getFullYear(),i=a?.firstWeekContainsDate??a?.locale?.options?.firstWeekContainsDate??to.firstWeekContainsDate??to.locale?.options?.firstWeekContainsDate??1,o=tr(a?.in||e,0);o.setFullYear(n+1,0,i),o.setHours(0,0,0,0);let s=tu(o,a),r=tr(a?.in||e,0);r.setFullYear(n,0,i),r.setHours(0,0,0,0);let c=tu(r,a);return+t>=+s?n+1:+t>=+c?n:n-1}function tx(e,a){let t=Math.abs(e).toString().padStart(a,"0");return(e<0?"-":"")+t}let th={y(e,a){let t=e.getFullYear(),n=t>0?t:1-t;return tx("yy"===a?n%100:n,a.length)},M(e,a){let t=e.getMonth();return"M"===a?String(t+1):tx(t+1,2)},d:(e,a)=>tx(e.getDate(),a.length),a(e,a){let t=e.getHours()/12>=1?"pm":"am";switch(a){case"a":case"aa":return t.toUpperCase();case"aaa":return t;case"aaaaa":return t[0];default:return"am"===t?"a.m.":"p.m."}},h:(e,a)=>tx(e.getHours()%12||12,a.length),H:(e,a)=>tx(e.getHours(),a.length),m:(e,a)=>tx(e.getMinutes(),a.length),s:(e,a)=>tx(e.getSeconds(),a.length),S(e,a){let t=a.length;return tx(Math.trunc(e.getMilliseconds()*Math.pow(10,t-3)),a.length)}},tv={midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},tb={G:function(e,a,t){let n=+(e.getFullYear()>0);switch(a){case"G":case"GG":case"GGG":return t.era(n,{width:"abbreviated"});case"GGGGG":return t.era(n,{width:"narrow"});default:return t.era(n,{width:"wide"})}},y:function(e,a,t){if("yo"===a){let a=e.getFullYear();return t.ordinalNumber(a>0?a:1-a,{unit:"year"})}return th.y(e,a)},Y:function(e,a,t,n){let i=tf(e,n),o=i>0?i:1-i;return"YY"===a?tx(o%100,2):"Yo"===a?t.ordinalNumber(o,{unit:"year"}):tx(o,a.length)},R:function(e,a){return tx(tm(e),a.length)},u:function(e,a){return tx(e.getFullYear(),a.length)},Q:function(e,a,t){let n=Math.ceil((e.getMonth()+1)/3);switch(a){case"Q":return String(n);case"QQ":return tx(n,2);case"Qo":return t.ordinalNumber(n,{unit:"quarter"});case"QQQ":return t.quarter(n,{width:"abbreviated",context:"formatting"});case"QQQQQ":return t.quarter(n,{width:"narrow",context:"formatting"});default:return t.quarter(n,{width:"wide",context:"formatting"})}},q:function(e,a,t){let n=Math.ceil((e.getMonth()+1)/3);switch(a){case"q":return String(n);case"qq":return tx(n,2);case"qo":return t.ordinalNumber(n,{unit:"quarter"});case"qqq":return t.quarter(n,{width:"abbreviated",context:"standalone"});case"qqqqq":return t.quarter(n,{width:"narrow",context:"standalone"});default:return t.quarter(n,{width:"wide",context:"standalone"})}},M:function(e,a,t){let n=e.getMonth();switch(a){case"M":case"MM":return th.M(e,a);case"Mo":return t.ordinalNumber(n+1,{unit:"month"});case"MMM":return t.month(n,{width:"abbreviated",context:"formatting"});case"MMMMM":return t.month(n,{width:"narrow",context:"formatting"});default:return t.month(n,{width:"wide",context:"formatting"})}},L:function(e,a,t){let n=e.getMonth();switch(a){case"L":return String(n+1);case"LL":return tx(n+1,2);case"Lo":return t.ordinalNumber(n+1,{unit:"month"});case"LLL":return t.month(n,{width:"abbreviated",context:"standalone"});case"LLLLL":return t.month(n,{width:"narrow",context:"standalone"});default:return t.month(n,{width:"wide",context:"standalone"})}},w:function(e,a,t,n){let i=function(e,a){let t=tc(e,a?.in);return Math.round((tu(t,a)-function(e,a){let t=a?.firstWeekContainsDate??a?.locale?.options?.firstWeekContainsDate??to.firstWeekContainsDate??to.locale?.options?.firstWeekContainsDate??1,n=tf(e,a),i=tr(a?.in||e,0);return i.setFullYear(n,0,t),i.setHours(0,0,0,0),tu(i,a)}(t,a))/6048e5)+1}(e,n);return"wo"===a?t.ordinalNumber(i,{unit:"week"}):tx(i,a.length)},I:function(e,a,t){let n=function(e,a){let t=tc(e,void 0);return Math.round((td(t)-function(e,a){let t=tm(e,void 0),n=tr(e,0);return n.setFullYear(t,0,4),n.setHours(0,0,0,0),td(n)}(t))/6048e5)+1}(e);return"Io"===a?t.ordinalNumber(n,{unit:"week"}):tx(n,a.length)},d:function(e,a,t){return"do"===a?t.ordinalNumber(e.getDate(),{unit:"date"}):th.d(e,a)},D:function(e,a,t){let n=function(e,a){let t=tc(e,void 0);return function(e,a,t){let[n,i]=function(e,...a){let t=tr.bind(null,e||a.find(e=>"object"==typeof e));return a.map(t)}(void 0,e,a),o=tl(n),s=tl(i);return Math.round((o-tp(o)-(s-tp(s)))/864e5)}(t,function(e,a){let t=tc(e,void 0);return t.setFullYear(t.getFullYear(),0,1),t.setHours(0,0,0,0),t}(t))+1}(e);return"Do"===a?t.ordinalNumber(n,{unit:"dayOfYear"}):tx(n,a.length)},E:function(e,a,t){let n=e.getDay();switch(a){case"E":case"EE":case"EEE":return t.day(n,{width:"abbreviated",context:"formatting"});case"EEEEE":return t.day(n,{width:"narrow",context:"formatting"});case"EEEEEE":return t.day(n,{width:"short",context:"formatting"});default:return t.day(n,{width:"wide",context:"formatting"})}},e:function(e,a,t,n){let i=e.getDay(),o=(i-n.weekStartsOn+8)%7||7;switch(a){case"e":return String(o);case"ee":return tx(o,2);case"eo":return t.ordinalNumber(o,{unit:"day"});case"eee":return t.day(i,{width:"abbreviated",context:"formatting"});case"eeeee":return t.day(i,{width:"narrow",context:"formatting"});case"eeeeee":return t.day(i,{width:"short",context:"formatting"});default:return t.day(i,{width:"wide",context:"formatting"})}},c:function(e,a,t,n){let i=e.getDay(),o=(i-n.weekStartsOn+8)%7||7;switch(a){case"c":return String(o);case"cc":return tx(o,a.length);case"co":return t.ordinalNumber(o,{unit:"day"});case"ccc":return t.day(i,{width:"abbreviated",context:"standalone"});case"ccccc":return t.day(i,{width:"narrow",context:"standalone"});case"cccccc":return t.day(i,{width:"short",context:"standalone"});default:return t.day(i,{width:"wide",context:"standalone"})}},i:function(e,a,t){let n=e.getDay(),i=0===n?7:n;switch(a){case"i":return String(i);case"ii":return tx(i,a.length);case"io":return t.ordinalNumber(i,{unit:"day"});case"iii":return t.day(n,{width:"abbreviated",context:"formatting"});case"iiiii":return t.day(n,{width:"narrow",context:"formatting"});case"iiiiii":return t.day(n,{width:"short",context:"formatting"});default:return t.day(n,{width:"wide",context:"formatting"})}},a:function(e,a,t){let n=e.getHours()/12>=1?"pm":"am";switch(a){case"a":case"aa":return t.dayPeriod(n,{width:"abbreviated",context:"formatting"});case"aaa":return t.dayPeriod(n,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return t.dayPeriod(n,{width:"narrow",context:"formatting"});default:return t.dayPeriod(n,{width:"wide",context:"formatting"})}},b:function(e,a,t){let n,i=e.getHours();switch(n=12===i?tv.noon:0===i?tv.midnight:i/12>=1?"pm":"am",a){case"b":case"bb":return t.dayPeriod(n,{width:"abbreviated",context:"formatting"});case"bbb":return t.dayPeriod(n,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return t.dayPeriod(n,{width:"narrow",context:"formatting"});default:return t.dayPeriod(n,{width:"wide",context:"formatting"})}},B:function(e,a,t){let n,i=e.getHours();switch(n=i>=17?tv.evening:i>=12?tv.afternoon:i>=4?tv.morning:tv.night,a){case"B":case"BB":case"BBB":return t.dayPeriod(n,{width:"abbreviated",context:"formatting"});case"BBBBB":return t.dayPeriod(n,{width:"narrow",context:"formatting"});default:return t.dayPeriod(n,{width:"wide",context:"formatting"})}},h:function(e,a,t){if("ho"===a){let a=e.getHours()%12;return 0===a&&(a=12),t.ordinalNumber(a,{unit:"hour"})}return th.h(e,a)},H:function(e,a,t){return"Ho"===a?t.ordinalNumber(e.getHours(),{unit:"hour"}):th.H(e,a)},K:function(e,a,t){let n=e.getHours()%12;return"Ko"===a?t.ordinalNumber(n,{unit:"hour"}):tx(n,a.length)},k:function(e,a,t){let n=e.getHours();return(0===n&&(n=24),"ko"===a)?t.ordinalNumber(n,{unit:"hour"}):tx(n,a.length)},m:function(e,a,t){return"mo"===a?t.ordinalNumber(e.getMinutes(),{unit:"minute"}):th.m(e,a)},s:function(e,a,t){return"so"===a?t.ordinalNumber(e.getSeconds(),{unit:"second"}):th.s(e,a)},S:function(e,a){return th.S(e,a)},X:function(e,a,t){let n=e.getTimezoneOffset();if(0===n)return"Z";switch(a){case"X":return ty(n);case"XXXX":case"XX":return tw(n);default:return tw(n,":")}},x:function(e,a,t){let n=e.getTimezoneOffset();switch(a){case"x":return ty(n);case"xxxx":case"xx":return tw(n);default:return tw(n,":")}},O:function(e,a,t){let n=e.getTimezoneOffset();switch(a){case"O":case"OO":case"OOO":return"GMT"+tg(n,":");default:return"GMT"+tw(n,":")}},z:function(e,a,t){let n=e.getTimezoneOffset();switch(a){case"z":case"zz":case"zzz":return"GMT"+tg(n,":");default:return"GMT"+tw(n,":")}},t:function(e,a,t){return tx(Math.trunc(e/1e3),a.length)},T:function(e,a,t){return tx(+e,a.length)}};function tg(e,a=""){let t=e>0?"-":"+",n=Math.abs(e),i=Math.trunc(n/60),o=n%60;return 0===o?t+String(i):t+String(i)+a+tx(o,2)}function ty(e,a){return e%60==0?(e>0?"-":"+")+tx(Math.abs(e)/60,2):tw(e,a)}function tw(e,a=""){let t=Math.abs(e);return(e>0?"-":"+")+tx(Math.trunc(t/60),2)+a+tx(t%60,2)}let tk=(e,a)=>{switch(e){case"P":return a.date({width:"short"});case"PP":return a.date({width:"medium"});case"PPP":return a.date({width:"long"});default:return a.date({width:"full"})}},tj=(e,a)=>{switch(e){case"p":return a.time({width:"short"});case"pp":return a.time({width:"medium"});case"ppp":return a.time({width:"long"});default:return a.time({width:"full"})}},tE={p:tj,P:(e,a)=>{let t,n=e.match(/(P+)(p+)?/)||[],i=n[1],o=n[2];if(!o)return tk(e,a);switch(i){case"P":t=a.dateTime({width:"short"});break;case"PP":t=a.dateTime({width:"medium"});break;case"PPP":t=a.dateTime({width:"long"});break;default:t=a.dateTime({width:"full"})}return t.replace("{{date}}",tk(i,a)).replace("{{time}}",tj(o,a))}},tS=/^D+$/,tR=/^Y+$/,t_=["D","DD","YY","YYYY"],tO=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,tT=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,tA=/^'([^]*?)'?$/,tC=/''/g,tF=/[a-zA-Z]/;function tD(e,a,t){let n=t?.locale??to.locale??ti,i=t?.firstWeekContainsDate??t?.locale?.options?.firstWeekContainsDate??to.firstWeekContainsDate??to.locale?.options?.firstWeekContainsDate??1,o=t?.weekStartsOn??t?.locale?.options?.weekStartsOn??to.weekStartsOn??to.locale?.options?.weekStartsOn??0,s=tc(e,t?.in);if(!(s instanceof Date||"object"==typeof s&&"[object Date]"===Object.prototype.toString.call(s))&&"number"!=typeof s||isNaN(+tc(s)))throw RangeError("Invalid time value");let r=a.match(tT).map(e=>{let a=e[0];return"p"===a||"P"===a?(0,tE[a])(e,n.formatLong):e}).join("").match(tO).map(e=>{if("''"===e)return{isToken:!1,value:"'"};let a=e[0];if("'"===a)return{isToken:!1,value:function(e){let a=e.match(tA);return a?a[1].replace(tC,"'"):e}(e)};if(tb[a])return{isToken:!0,value:e};if(a.match(tF))throw RangeError("Format string contains an unescaped latin alphabet character `"+a+"`");return{isToken:!1,value:e}});n.localize.preprocessor&&(r=n.localize.preprocessor(s,r));let c={firstWeekContainsDate:i,weekStartsOn:o,locale:n};return r.map(i=>{if(!i.isToken)return i.value;let o=i.value;return(!t?.useAdditionalWeekYearTokens&&tR.test(o)||!t?.useAdditionalDayOfYearTokens&&tS.test(o))&&function(e,a,t){let n=function(e,a,t){let n="Y"===e[0]?"years":"days of the month";return`Use \`${e.toLowerCase()}\` instead of \`${e}\` (in \`${a}\`) for formatting ${n} to the input \`${t}\`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md`}(e,a,t);if(console.warn(n),t_.includes(e))throw RangeError(n)}(o,a,String(e)),(0,tb[o[0]])(s,o,n.localize,c)}).join("")}function tP(e,a,t){return e<=0||a<=0||t<=0?0:Number(((Math.pow(a/e,1/t)-1)*100).toFixed(a5.precision.cagr))}function tN(e,a){return e<=0?0:Number(((a-e)/e*100).toFixed(a5.precision.percentage))}function tz(e,a){return(a.getTime()-e.getTime())/864e5/365.25}function tL(e,a="INR"){return new Intl.NumberFormat("en-IN",{style:"currency",currency:a,minimumFractionDigits:a5.precision.currency,maximumFractionDigits:a5.precision.currency}).format(e)}function tM(e){return`${e.toFixed(a5.precision.percentage)}%`}function tB(e,a){let t=new Date;return e>=a?{isValid:!1,error:"Start date must be before end date"}:e>t?{isValid:!1,error:"Start date cannot be in the future"}:a>t?{isValid:!1,error:"End date cannot be in the future"}:e<new Date("2000-01-01")?{isValid:!1,error:"Start date is too far in the past"}:{isValid:!0}}function tq(){return`${Date.now()}-${Math.random().toString(36).substr(2,9)}`}class tU{constructor(e){this.angelOneClient=e}async getHistoricalPrices(e,a,t,n,i="ONE_DAY"){let o=tB(t,n);if(!o.isValid)throw Error(o.error);let s=tD(t,"yyyy-MM-dd HH:mm"),r=tD(n,"yyyy-MM-dd HH:mm");try{return(await this.angelOneClient.getHistoricalData({exchange:a,symboltoken:e,interval:i,fromdate:s,todate:r})).sort((e,a)=>e.date.getTime()-a.date.getTime())}catch(e){throw console.error("Error fetching historical data:",e),Error(`Failed to fetch historical data: ${e instanceof Error?e.message:"Unknown error"}`)}}async getCurrentPrice(e,a,t){try{return await this.angelOneClient.getCurrentPrice({exchange:t,tradingsymbol:e,symboltoken:a})}catch(e){throw console.error("Error fetching current price:",e),Error(`Failed to fetch current price: ${e instanceof Error?e.message:"Unknown error"}`)}}async calculateInvestmentResult(e){try{let a=await this.getHistoricalPrices(e.stockSymbol,"NSE",e.startDate,e.endDate);if(0===a.length)throw Error("No historical data available for the specified period");let t=a[0].close,n=a[a.length-1].close,i=e.investmentAmount/t*n,o=(i-e.investmentAmount)/e.investmentAmount*100,s=tz(e.startDate,e.endDate),r=s>0?(Math.pow(i/e.investmentAmount,1/s)-1)*100:0;return{scenario:e,initialValue:e.investmentAmount,currentValue:i,absoluteReturn:o,cagr:Number(r.toFixed(2)),totalReturn:i-e.investmentAmount,annualizedReturn:r}}catch(e){throw console.error("Error calculating investment result:",e),Error(`Failed to calculate investment result: ${e instanceof Error?e.message:"Unknown error"}`)}}async getPriceAtDate(e,a,t){try{let n=new Date(t);n.setDate(n.getDate()-5);let i=new Date(t);i.setDate(i.getDate()+5);let o=await this.getHistoricalPrices(e,a,n,i);if(0===o.length)return null;let s=o[0],r=Math.abs(s.date.getTime()-t.getTime());for(let e of o){let a=Math.abs(e.date.getTime()-t.getTime());a<r&&(r=a,s=e)}return{price:s.close,actualDate:s.date}}catch(e){return console.error("Error getting price at date:",e),null}}async validateStock(e,a,t){try{let n=await this.getCurrentPrice(e,a,t);return{isValid:!0,stockData:n}}catch(e){return{isValid:!1,error:e instanceof Error?e.message:"Unknown error"}}}async getMultipleCurrentPrices(e){let a=[];for(let t of e)try{let e=await this.getCurrentPrice(t.tradingSymbol,t.symbolToken,t.exchange);a.push(e)}catch(e){console.error(`Error fetching price for ${t.tradingSymbol}:`,e)}return a}}class tI{constructor(e){this.angelOneClient=e}async getNiftyData(e,a){try{let t=await this.angelOneClient.getHistoricalData({exchange:"NSE",symboltoken:a3.types.NIFTY.token,interval:"ONE_DAY",fromdate:e.toISOString().slice(0,16).replace("T"," "),todate:a.toISOString().slice(0,16).replace("T"," ")});return{type:"NIFTY",name:a3.types.NIFTY.name,returns:t.map(e=>({date:e.date,value:e.close}))}}catch(e){throw console.error("Error fetching Nifty data:",e),Error(`Failed to fetch Nifty data: ${e instanceof Error?e.message:"Unknown error"}`)}}async getGoldData(e,a){try{let t=this.simulateGoldPrices(e,a);return{type:"GOLD",name:a3.types.GOLD.name,returns:t}}catch(e){throw console.error("Error fetching gold data:",e),Error(`Failed to fetch gold data: ${e instanceof Error?e.message:"Unknown error"}`)}}async getFDData(e,a){try{let t=this.calculateFDReturns(e,a);return{type:"FD",name:a3.types.FD.name,returns:t}}catch(e){throw console.error("Error calculating FD data:",e),Error(`Failed to calculate FD data: ${e instanceof Error?e.message:"Unknown error"}`)}}async calculateBenchmarkReturns(e,a,t,n){let i;switch(e){case"NIFTY":i=await this.getNiftyData(t,n);break;case"GOLD":i=await this.getGoldData(t,n);break;case"FD":i=await tH(t,n);break;default:throw Error(`Unsupported benchmark type: ${e}`)}if(0===i.returns.length)throw Error(`No data available for ${e} in the specified period`);let o=i.returns[0].value,s=a/o*i.returns[i.returns.length-1].value,r=tP(a,s,tz(t,n)),c=tN(a,s);return{initialValue:a,currentValue:s,cagr:r,absoluteReturn:c}}simulateGoldPrices(e,a){let t=[],n=new Date(e),i=5e4,o=Math.pow(1.09,1/365)-1;for(;n<=a;)i*=1+(o+(Math.random()-.5)*.04),t.push({date:new Date(n),value:Math.round(100*i)/100}),n.setDate(n.getDate()+1);return t}calculateFDReturns(e,a){let t=[],n=new Date(e),i=a3.types.FD.defaultRate/100/365,o=100;for(;n<=a;)o*=1+i,t.push({date:new Date(n),value:Math.round(100*o)/100}),n.setDate(n.getDate()+1);return t}async getAllBenchmarkData(e,a,t){let n={};for(let i of["GOLD","FD","NIFTY"])try{n[i]=await this.calculateBenchmarkReturns(i,e,a,t)}catch(a){console.error(`Error calculating ${i} returns:`,a),n[i]={initialValue:e,currentValue:e,cagr:0,absoluteReturn:0}}return n}}async function tH(e,a){let t=[],n=new Date(e),i=a3.types.FD.defaultRate/100/365,o=100;for(;n<=a;)o*=1+i,t.push({date:new Date(n),value:Math.round(100*o)/100}),n.setDate(n.getDate()+1);return{type:"FD",name:a3.types.FD.name,returns:t}}class tW{constructor(e,a){this.stockDataService=e,this.benchmarkDataService=a}createScenario(e,a,t,n=new Date){if(a<=0)throw Error("Investment amount must be greater than 0");let i=tB(t,n);if(!i.isValid)throw Error(i.error);return{id:tq(),stockSymbol:e,investmentAmount:a,startDate:t,endDate:n,createdAt:new Date}}async calculateInvestmentResult(e){try{return await this.stockDataService.calculateInvestmentResult(e)}catch(e){throw console.error("Error calculating investment result:",e),e}}async calculateWithComparisons(e){try{let a=await this.calculateInvestmentResult(e),t=await this.benchmarkDataService.getAllBenchmarkData(e.investmentAmount,e.startDate,e.endDate);return{investment:a,benchmarks:t}}catch(e){throw console.error("Error calculating investment with comparisons:",e),e}}async calculateSensitivityAnalysis(e,a,t,n,i=[.5,.75,1,1.25,1.5,2]){let o=[];for(let s of i){let i=a*s,r=this.createScenario(e,i,t,n);try{let e=await this.calculateInvestmentResult(r);o.push({amount:i,result:e})}catch(e){console.error(`Error calculating for amount ${i}:`,e)}}return o}async calculateTimeBasedAnalysis(e,a,t,n=[{label:"6 months",months:6},{label:"1 year",months:12},{label:"2 years",months:24},{label:"3 years",months:36},{label:"5 years",months:60}]){let i=[];for(let o of n){let n=new Date(t),s=new Date(t);if(s.setMonth(s.getMonth()+o.months),s>new Date)continue;let r=this.createScenario(e,a,n,s);try{let e=await this.calculateInvestmentResult(r);i.push({period:o.label,startDate:n,endDate:s,result:e})}catch(e){console.error(`Error calculating for period ${o.label}:`,e)}}return i}async calculateSIPReturns(e,a,t,n){let i=[],o=0,s=0,r=new Date(t);for(;r<=n;){try{let t=await this.stockDataService.getPriceAtDate(e,"NSE",r);if(t){let e=a/t.price;s+=e,o+=a,i.push({date:new Date(r),amount:a,price:t.price,units:e,cumulativeUnits:s,cumulativeInvestment:o})}}catch(e){console.error(`Error calculating SIP for ${r}:`,e)}r.setMonth(r.getMonth()+1)}if(0===i.length)throw Error("No SIP installments could be calculated");try{let a=await this.stockDataService.getCurrentPrice(e.split("-")[0],e,"NSE"),r=s*a.currentPrice,c=r-o,p=tz(t,n),l=tP(o,r,p),u=tN(o,r);return{totalInvested:o,currentValue:r,totalReturn:c,cagr:l,absoluteReturn:u,installments:i}}catch(e){throw Error(`Failed to calculate SIP returns: ${e instanceof Error?e.message:"Unknown error"}`)}}async calculateOptimalTiming(e,a,t,n,i=[{name:"Lump Sum at Start",type:"lump_sum"},{name:"Monthly SIP",type:"monthly_sip"},{name:"Quarterly SIP",type:"quarterly_sip"}]){let o=[];for(let s of i)try{let i;switch(s.type){case"lump_sum":let r=this.createScenario(e,a,t,n),c=await this.calculateInvestmentResult(r);i={strategy:s.name,totalInvested:c.initialValue,currentValue:c.currentValue,cagr:c.cagr,absoluteReturn:c.absoluteReturn};break;case"monthly_sip":let p=Math.ceil(12*tz(t,n)),l=a/p,u=await this.calculateSIPReturns(e,l,t,n);i={strategy:s.name,totalInvested:u.totalInvested,currentValue:u.currentValue,cagr:u.cagr,absoluteReturn:u.absoluteReturn};break;case"quarterly_sip":let d=Math.ceil(4*tz(t,n)),m=a/d,f=await this.calculateSIPReturns(e,m,t,n);i={strategy:s.name,totalInvested:f.totalInvested,currentValue:f.currentValue,cagr:f.cagr,absoluteReturn:f.absoluteReturn};break;default:continue}o.push(i)}catch(e){console.error(`Error calculating ${s.name}:`,e)}return o}}class t${constructor(e,a){this.investmentCalculator=e,this.benchmarkDataService=a}async generateComparisonSummary(e){try{let a=await this.investmentCalculator.calculateWithComparisons(e),t={name:`${e.stockSymbol} Investment`,initialValue:a.investment.initialValue,currentValue:a.investment.currentValue,totalReturn:a.investment.totalReturn,cagr:a.investment.cagr,absoluteReturn:a.investment.absoluteReturn,rank:0},n=Object.entries(a.benchmarks).map(([e,a])=>({name:this.getBenchmarkDisplayName(e),type:e,initialValue:a.initialValue,currentValue:a.currentValue,totalReturn:a.currentValue-a.initialValue,cagr:a.cagr,absoluteReturn:a.absoluteReturn,rank:0,outperformance:t.cagr-a.cagr})),i=[t,...n];i.sort((e,a)=>a.cagr-e.cagr),i.forEach((e,a)=>{e.rank=a+1});let o=this.generateInsights(t,n,e);return{investment:t,benchmarks:n,insights:o}}catch(e){throw console.error("Error generating comparison summary:",e),e}}calculatePerformanceMetrics(e){let a=[e.investment,...e.benchmarks],t=[...a].sort((e,a)=>a.cagr-e.cagr),n={name:t[0].name,cagr:t[0].cagr,absoluteReturn:t[0].absoluteReturn},i={name:t[t.length-1].name,cagr:t[t.length-1].cagr,absoluteReturn:t[t.length-1].absoluteReturn},o=a.reduce((e,a)=>e+a.cagr,0)/a.length,s=a.map(e=>({name:e.name,volatility:Math.abs(e.absoluteReturn-e.cagr)})).sort((e,a)=>e.volatility-a.volatility);return{bestPerformer:n,worstPerformer:i,averageCagr:Number(o.toFixed(2)),volatilityRanking:s}}async generateComparisonChartData(e){try{let a=await this.investmentCalculator.calculateWithComparisons(e),t=await this.generateTimeSeriesData(e,a),n=[{name:e.stockSymbol,cagr:a.investment.cagr,absoluteReturn:a.investment.absoluteReturn,currentValue:a.investment.currentValue},...Object.entries(a.benchmarks).map(([e,a])=>({name:this.getBenchmarkDisplayName(e),cagr:a.cagr,absoluteReturn:a.absoluteReturn,currentValue:a.currentValue}))];return{timeSeriesData:t,barChartData:n}}catch(e){throw console.error("Error generating chart data:",e),e}}async compareMultipleStocks(e){let a=[];for(let t of e)try{let e=await this.generateComparisonSummary(t);a.push({scenario:t,summary:e})}catch(e){console.error(`Error comparing scenario ${t.id}:`,e)}return a}generateInsights(e,a,t){let n=[],i=tz(t.startDate,t.endDate);if(1===e.rank)n.push(`🎉 Your ${t.stockSymbol} investment outperformed all benchmarks with a CAGR of ${tM(e.cagr)}.`);else{let t=a.filter(a=>a.rank<e.rank);if(t.length>0){let a=t[0];n.push(`📊 Your investment ranked #${e.rank}. ${a.name} performed better with ${tM(a.cagr)} CAGR.`)}}let o=a.find(e=>"GOLD"===e.type),s=a.find(e=>"FD"===e.type),r=a.find(e=>"NIFTY"===e.type);return o&&e.cagr>o.cagr&&n.push(`🥇 Your investment beat gold by ${tM(e.cagr-o.cagr)} annually.`),s&&e.cagr>s.cagr&&n.push(`🏦 Your investment outperformed Fixed Deposits by ${tM(e.cagr-s.cagr)} annually.`),r&&(e.cagr>r.cagr?n.push(`📈 Your stock selection beat the market (Nifty 50) by ${tM(e.cagr-r.cagr)} annually.`):n.push(`📉 The market (Nifty 50) outperformed your stock by ${tM(r.cagr-e.cagr)} annually.`)),i>=5?n.push(`⏰ Over ${Math.round(i)} years, your ${tL(t.investmentAmount)} investment grew to ${tL(e.currentValue)}.`):i>=1&&n.push(`📅 In ${Math.round(12*i)} months, your investment generated ${tL(e.totalReturn)} in returns.`),e.absoluteReturn>100?n.push(`💰 Your investment more than doubled your money with ${tM(e.absoluteReturn)} total returns.`):e.absoluteReturn>50?n.push(`💵 Your investment generated strong returns of ${tM(e.absoluteReturn)}.`):e.absoluteReturn<0&&n.push(`⚠️ Your investment resulted in a loss of ${tM(Math.abs(e.absoluteReturn))}.`),n}getBenchmarkDisplayName(e){return({GOLD:"Gold",FD:"Fixed Deposit",NIFTY:"Nifty 50"})[e]||e}async generateTimeSeriesData(e,a){let t=[],n=new Date(e.startDate),i=new Date(e.endDate),o=new Date(n),s=365*tz(n,i),r=Math.pow(a.investment.currentValue/a.investment.initialValue,1/s),c=Math.pow(a.benchmarks.GOLD.currentValue/a.benchmarks.GOLD.initialValue,1/s),p=Math.pow(a.benchmarks.FD.currentValue/a.benchmarks.FD.initialValue,1/s),l=Math.pow(a.benchmarks.NIFTY.currentValue/a.benchmarks.NIFTY.initialValue,1/s),u=0;for(;o<=i;){let a=e.investmentAmount*Math.pow(r,u),n=e.investmentAmount*Math.pow(c,u),i=e.investmentAmount*Math.pow(p,u),s=e.investmentAmount*Math.pow(l,u);t.push({date:o.toISOString().split("T")[0],investment:Math.round(a),gold:Math.round(n),fd:Math.round(i),nifty:Math.round(s)}),o.setDate(o.getDate()+7),u+=7}return t}}async function tG(){n||(n=new a8({apiKey:process.env.ANGEL_ONE_API_KEY,clientId:process.env.ANGEL_ONE_CLIENT_ID,password:process.env.ANGEL_ONE_PASSWORD,totpSecret:process.env.ANGEL_ONE_TOTP_SECRET}),await n.login(),s=new t$(o=new tW(new tU(n),i=new tI(n)),i))}async function tV(e){try{await tG();let{stockSymbol:a,investmentAmount:t,startDate:n,endDate:i}=await e.json();if(!a||!t||!n||!i)return h.NextResponse.json({error:"Missing required fields"},{status:400});let r={id:tq(),stockSymbol:a,investmentAmount:parseFloat(t),startDate:new Date(n),endDate:new Date(i),createdAt:new Date};if(r.startDate>=r.endDate)return h.NextResponse.json({error:"Start date must be before end date"},{status:400});if(r.startDate>new Date)return h.NextResponse.json({error:"Start date cannot be in the future"},{status:400});let c=await o.calculateInvestmentResult(r),p=await o.calculateWithComparisons(r),l=await s.generateComparisonSummary(r),u=await s.generateComparisonChartData(r);return h.NextResponse.json({investmentResult:c,comparisonResult:p,comparisonSummary:l,chartData:u})}catch(e){if(console.error("Analysis API error:",e),e instanceof Error){if(e.message.includes("Stock not found")||e.message.includes("Invalid stock symbol"))return h.NextResponse.json({error:"Stock symbol not found. Please check the symbol and try again."},{status:404});if(e.message.includes("Rate limit"))return h.NextResponse.json({error:"Too many requests. Please wait a moment and try again."},{status:429});if(e.message.includes("Authentication"))return h.NextResponse.json({error:"Authentication failed. Please try again later."},{status:401})}return h.NextResponse.json({error:"An error occurred while analyzing the investment. Please try again."},{status:500})}}async function tY(){try{return await tG(),h.NextResponse.json({status:"OK",message:"Analysis API is running"})}catch(e){return console.error("Health check failed:",e),h.NextResponse.json({status:"ERROR",message:"Service unavailable"},{status:503})}}let tK=new m.AppRouteRouteModule({definition:{kind:f.RouteKind.APP_ROUTE,page:"/api/analyze/route",pathname:"/api/analyze",filename:"route",bundlePath:"app/api/analyze/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\app\\api\\analyze\\route.ts",nextConfigOutput:"",userland:d}),{workAsyncStorage:tJ,workUnitAsyncStorage:tX,serverHooks:tQ}=tK;function tZ(){return(0,x.patchFetch)({workAsyncStorage:tJ,workUnitAsyncStorage:tX})}},5907:(e,a,t)=>{"use strict";var n=t(2545),i=t(2595),o=t(9581);e.exports=n?function(e){return n(e)}:i?function(e){if(!e||"object"!=typeof e&&"function"!=typeof e)throw TypeError("getProto: not an object");return i(e)}:o?function(e){return o(e)}:null},5946:(e,a,t)=>{a.formatArgs=function(a){if(a[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+a[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;let t="color: "+this.color;a.splice(1,0,t,"color: inherit");let n=0,i=0;a[0].replace(/%[a-zA-Z%]/g,e=>{"%%"!==e&&(n++,"%c"===e&&(i=n))}),a.splice(i,0,t)},a.save=function(e){try{e?a.storage.setItem("debug",e):a.storage.removeItem("debug")}catch(e){}},a.load=function(){let e;try{e=a.storage.getItem("debug")||a.storage.getItem("DEBUG")}catch(e){}return!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG),e},a.useColors=function(){let e;return"undefined"!=typeof window&&!!window.process&&("renderer"===window.process.type||!!window.process.__nwjs)||!("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))&&("undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/))},a.storage=function(){try{return localStorage}catch(e){}}(),a.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),a.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],a.log=console.debug||console.log||(()=>{}),e.exports=t(2297)(a);let{formatters:n}=e.exports;n.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},6067:(e,a,t)=>{var n=t(6280);a.encode=n.encode,a.decode=n.decode},6086:(e,a,t)=>{"use strict";Object.defineProperty(a,"__esModule",{value:!0});var n=t(2044),i=t(4318),o=t(8490);let s=new o.HOTP({createDigest:n.createDigest}),r=new o.TOTP({createDigest:n.createDigest});a.authenticator=new o.Authenticator({createDigest:n.createDigest,createRandomBytes:n.createRandomBytes,keyDecoder:i.keyDecoder,keyEncoder:i.keyEncoder}),a.hotp=s,a.totp=r},6164:(e,a,t)=>{"use strict";var n=t(3632);e.exports=Function.prototype.bind||n},6280:(e,a)=>{"use strict";var t=[255,255,26,27,28,29,30,31,255,255,255,255,255,255,255,255,255,0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,255,255,255,255,255,255,0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,255,255,255,255,255];a.encode=function(e){Buffer.isBuffer(e)||(e=new Buffer(e));for(var a,t,n=0,i=0,o=0,s=0,r=new Buffer(8*(t=Math.floor((a=e).length/5),a.length%5==0?t:t+1));n<e.length;){var c=e[n];o>3?(s=(s=c&255>>o)<<(o=(o+5)%8)|(n+1<e.length?e[n+1]:0)>>8-o,n++):(s=c>>8-(o+5)&31,0==(o=(o+5)%8)&&n++),r[i]="ABCDEFGHIJKLMNOPQRSTUVWXYZ234567".charCodeAt(s),i++}for(n=i;n<r.length;n++)r[n]=61;return r},a.decode=function(e){var a,n=0,i=0,o=0;Buffer.isBuffer(e)||(e=new Buffer(e));for(var s=new Buffer(Math.ceil(5*e.length/8)),r=0;r<e.length&&61!==e[r];r++){var c=e[r]-48;if(c<t.length)i=t[c],n<=3?0==(n=(n+5)%8)?(a|=i,s[o]=a,o++,a=0):a|=255&i<<8-n:(a|=255&i>>>(n=(n+5)%8),s[o]=a,o++,a=255&i<<8-n);else throw Error("Invalid input - it is not base32 encoded string")}return s.slice(0,o)}},6300:(e,a,t)=>{let n=t(3997),i=t(8354);a.init=function(e){e.inspectOpts={};let t=Object.keys(a.inspectOpts);for(let n=0;n<t.length;n++)e.inspectOpts[t[n]]=a.inspectOpts[t[n]]},a.log=function(...e){return process.stderr.write(i.formatWithOptions(a.inspectOpts,...e)+"\n")},a.formatArgs=function(t){let{namespace:n,useColors:i}=this;if(i){let a=this.color,i="\x1b[3"+(a<8?a:"8;5;"+a),o=`  ${i};1m${n} \u001B[0m`;t[0]=o+t[0].split("\n").join("\n"+o),t.push(i+"m+"+e.exports.humanize(this.diff)+"\x1b[0m")}else t[0]=(a.inspectOpts.hideDate?"":new Date().toISOString()+" ")+n+" "+t[0]},a.save=function(e){e?process.env.DEBUG=e:delete process.env.DEBUG},a.load=function(){return process.env.DEBUG},a.useColors=function(){return"colors"in a.inspectOpts?!!a.inspectOpts.colors:n.isatty(process.stderr.fd)},a.destroy=i.deprecate(()=>{},"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."),a.colors=[6,2,3,4,5,1];try{let e=t(5566);e&&(e.stderr||e).level>=2&&(a.colors=[20,21,26,27,32,33,38,39,40,41,42,43,44,45,56,57,62,63,68,69,74,75,76,77,78,79,80,81,92,93,98,99,112,113,128,129,134,135,148,149,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,179,184,185,196,197,198,199,200,201,202,203,204,205,206,207,208,209,214,215,220,221])}catch(e){}a.inspectOpts=Object.keys(process.env).filter(e=>/^debug_/i.test(e)).reduce((e,a)=>{let t=a.substring(6).toLowerCase().replace(/_([a-z])/g,(e,a)=>a.toUpperCase()),n=process.env[a];return n=!!/^(yes|on|true|enabled)$/i.test(n)||!/^(no|off|false|disabled)$/i.test(n)&&("null"===n?null:Number(n)),e[t]=n,e},{}),e.exports=t(2297)(a);let{formatters:o}=e.exports;o.o=function(e){return this.inspectOpts.colors=this.useColors,i.inspect(e,this.inspectOpts).split("\n").map(e=>e.trim()).join(" ")},o.O=function(e){return this.inspectOpts.colors=this.useColors,i.inspect(e,this.inspectOpts)}},6373:(e,a,t)=>{var n=t(9551),i=n.URL,o=t(1630),s=t(5591),r=t(7910).Writable,c=t(2412),p=t(1118);!function(){var e="undefined"!=typeof process,a="undefined"!=typeof window&&"undefined"!=typeof document,t=A(Error.captureStackTrace);e||!a&&t||console.warn("The follow-redirects package should be excluded from browser builds.")}();var l=!1;try{c(new i(""))}catch(e){l="ERR_INVALID_URL"===e.code}var u=["auth","host","hostname","href","path","pathname","port","protocol","query","search","hash"],d=["abort","aborted","connect","error","socket","timeout"],m=Object.create(null);d.forEach(function(e){m[e]=function(a,t,n){this._redirectable.emit(e,a,t,n)}});var f=_("ERR_INVALID_URL","Invalid URL",TypeError),x=_("ERR_FR_REDIRECTION_FAILURE","Redirected request failed"),h=_("ERR_FR_TOO_MANY_REDIRECTS","Maximum number of redirects exceeded",x),v=_("ERR_FR_MAX_BODY_LENGTH_EXCEEDED","Request body larger than maxBodyLength limit"),b=_("ERR_STREAM_WRITE_AFTER_END","write after end"),g=r.prototype.destroy||k;function y(e,a){r.call(this),this._sanitizeOptions(e),this._options=e,this._ended=!1,this._ending=!1,this._redirectCount=0,this._redirects=[],this._requestBodyLength=0,this._requestBodyBuffers=[],a&&this.on("response",a);var t=this;this._onNativeResponse=function(e){try{t._processResponse(e)}catch(e){t.emit("error",e instanceof x?e:new x({cause:e}))}},this._performRequest()}function w(e){var a={maxRedirects:21,maxBodyLength:0xa00000},t={};return Object.keys(e).forEach(function(n){var o=n+":",s=t[o]=e[n],r=a[n]=Object.create(s);Object.defineProperties(r,{request:{value:function(e,n,s){var r;return(r=e,i&&r instanceof i)?e=S(e):T(e)?e=S(j(e)):(s=n,n=E(e),e={protocol:o}),A(n)&&(s=n,n=null),(n=Object.assign({maxRedirects:a.maxRedirects,maxBodyLength:a.maxBodyLength},e,n)).nativeProtocols=t,T(n.host)||T(n.hostname)||(n.hostname="::1"),c.equal(n.protocol,o,"protocol mismatch"),p("options",n),new y(n,s)},configurable:!0,enumerable:!0,writable:!0},get:{value:function(e,a,t){var n=r.request(e,a,t);return n.end(),n},configurable:!0,enumerable:!0,writable:!0}})}),a}function k(){}function j(e){var a;if(l)a=new i(e);else if(!T((a=E(n.parse(e))).protocol))throw new f({input:e});return a}function E(e){if(/^\[/.test(e.hostname)&&!/^\[[:0-9a-f]+\]$/i.test(e.hostname)||/^\[/.test(e.host)&&!/^\[[:0-9a-f]+\](:\d+)?$/i.test(e.host))throw new f({input:e.href||e});return e}function S(e,a){var t=a||{};for(var n of u)t[n]=e[n];return t.hostname.startsWith("[")&&(t.hostname=t.hostname.slice(1,-1)),""!==t.port&&(t.port=Number(t.port)),t.path=t.search?t.pathname+t.search:t.pathname,t}function R(e,a){var t;for(var n in a)e.test(n)&&(t=a[n],delete a[n]);return null==t?void 0:String(t).trim()}function _(e,a,t){function n(t){A(Error.captureStackTrace)&&Error.captureStackTrace(this,this.constructor),Object.assign(this,t||{}),this.code=e,this.message=this.cause?a+": "+this.cause.message:a}return n.prototype=new(t||Error),Object.defineProperties(n.prototype,{constructor:{value:n,enumerable:!1},name:{value:"Error ["+e+"]",enumerable:!1}}),n}function O(e,a){for(var t of d)e.removeListener(t,m[t]);e.on("error",k),e.destroy(a)}function T(e){return"string"==typeof e||e instanceof String}function A(e){return"function"==typeof e}y.prototype=Object.create(r.prototype),y.prototype.abort=function(){O(this._currentRequest),this._currentRequest.abort(),this.emit("abort")},y.prototype.destroy=function(e){return O(this._currentRequest,e),g.call(this,e),this},y.prototype.write=function(e,a,t){var n;if(this._ending)throw new b;if(!T(e)&&!("object"==typeof(n=e)&&"length"in n))throw TypeError("data should be a string, Buffer or Uint8Array");if(A(a)&&(t=a,a=null),0===e.length){t&&t();return}this._requestBodyLength+e.length<=this._options.maxBodyLength?(this._requestBodyLength+=e.length,this._requestBodyBuffers.push({data:e,encoding:a}),this._currentRequest.write(e,a,t)):(this.emit("error",new v),this.abort())},y.prototype.end=function(e,a,t){if(A(e)?(t=e,e=a=null):A(a)&&(t=a,a=null),e){var n=this,i=this._currentRequest;this.write(e,a,function(){n._ended=!0,i.end(null,null,t)}),this._ending=!0}else this._ended=this._ending=!0,this._currentRequest.end(null,null,t)},y.prototype.setHeader=function(e,a){this._options.headers[e]=a,this._currentRequest.setHeader(e,a)},y.prototype.removeHeader=function(e){delete this._options.headers[e],this._currentRequest.removeHeader(e)},y.prototype.setTimeout=function(e,a){var t=this;function n(a){a.setTimeout(e),a.removeListener("timeout",a.destroy),a.addListener("timeout",a.destroy)}function i(a){t._timeout&&clearTimeout(t._timeout),t._timeout=setTimeout(function(){t.emit("timeout"),o()},e),n(a)}function o(){t._timeout&&(clearTimeout(t._timeout),t._timeout=null),t.removeListener("abort",o),t.removeListener("error",o),t.removeListener("response",o),t.removeListener("close",o),a&&t.removeListener("timeout",a),t.socket||t._currentRequest.removeListener("socket",i)}return a&&this.on("timeout",a),this.socket?i(this.socket):this._currentRequest.once("socket",i),this.on("socket",n),this.on("abort",o),this.on("error",o),this.on("response",o),this.on("close",o),this},["flushHeaders","getHeader","setNoDelay","setSocketKeepAlive"].forEach(function(e){y.prototype[e]=function(a,t){return this._currentRequest[e](a,t)}}),["aborted","connection","socket"].forEach(function(e){Object.defineProperty(y.prototype,e,{get:function(){return this._currentRequest[e]}})}),y.prototype._sanitizeOptions=function(e){if(e.headers||(e.headers={}),e.host&&(e.hostname||(e.hostname=e.host),delete e.host),!e.pathname&&e.path){var a=e.path.indexOf("?");a<0?e.pathname=e.path:(e.pathname=e.path.substring(0,a),e.search=e.path.substring(a))}},y.prototype._performRequest=function(){var e=this._options.protocol,a=this._options.nativeProtocols[e];if(!a)throw TypeError("Unsupported protocol "+e);if(this._options.agents){var t=e.slice(0,-1);this._options.agent=this._options.agents[t]}var i=this._currentRequest=a.request(this._options,this._onNativeResponse);for(var o of(i._redirectable=this,d))i.on(o,m[o]);if(this._currentUrl=/^\//.test(this._options.path)?n.format(this._options):this._options.path,this._isRedirect){var s=0,r=this,c=this._requestBodyBuffers;!function e(a){if(i===r._currentRequest)if(a)r.emit("error",a);else if(s<c.length){var t=c[s++];i.finished||i.write(t.data,t.encoding,e)}else r._ended&&i.end()}()}},y.prototype._processResponse=function(e){var a,t,o,s,r,u,d=e.statusCode;this._options.trackRedirects&&this._redirects.push({url:this._currentUrl,headers:e.headers,statusCode:d});var m=e.headers.location;if(!m||!1===this._options.followRedirects||d<300||d>=400){e.responseUrl=this._currentUrl,e.redirects=this._redirects,this.emit("response",e),this._requestBodyBuffers=[];return}if(O(this._currentRequest),e.destroy(),++this._redirectCount>this._options.maxRedirects)throw new h;var f=this._options.beforeRedirect;f&&(u=Object.assign({Host:e.req.getHeader("host")},this._options.headers));var x=this._options.method;(301!==d&&302!==d||"POST"!==this._options.method)&&(303!==d||/^(?:GET|HEAD)$/.test(this._options.method))||(this._options.method="GET",this._requestBodyBuffers=[],R(/^content-/i,this._options.headers));var v=R(/^host$/i,this._options.headers),b=j(this._currentUrl),g=v||b.host,y=/^\w+:/.test(m)?this._currentUrl:n.format(Object.assign(b,{host:g})),w=(a=m,t=y,l?new i(a,t):j(n.resolve(t,a)));if(p("redirecting to",w.href),this._isRedirect=!0,S(w,this._options),(w.protocol===b.protocol||"https:"===w.protocol)&&(w.host===g||(o=w.host,s=g,c(T(o)&&T(s)),(r=o.length-s.length-1)>0&&"."===o[r]&&o.endsWith(s)))||R(/^(?:(?:proxy-)?authorization|cookie)$/i,this._options.headers),A(f)){var k={headers:e.headers,statusCode:d},E={url:y,method:x,headers:u};f(this._options,k,E),this._sanitizeOptions(this._options)}this._performRequest()},e.exports=w({http:o,https:s}),e.exports.wrap=w},6449:(e,a,t)=>{var n=t(8530),i=t(8321),o=t(1531);function s(e,a){return e<a?-1:+(e>a)}e.exports=function(e,a,t,s){var r=i(e,t);return n(e,a,r,function t(i,o){return i?void s(i,o):(r.index++,r.index<(r.keyedList||e).length)?void n(e,a,r,t):void s(null,r.results)}),o.bind(r,s)},e.exports.ascending=s,e.exports.descending=function(e,a){return -1*s(e,a)}},6456:(e,a,t)=>{"use strict";var n="undefined"!=typeof Symbol&&Symbol,i=t(2590);e.exports=function(){return"function"==typeof n&&"function"==typeof Symbol&&"symbol"==typeof n("foo")&&"symbol"==typeof Symbol("bar")&&i()}},6487:()=>{},6961:e=>{"use strict";e.exports=Math.min},6992:e=>{e.exports=function(e){var a="function"==typeof setImmediate?setImmediate:"object"==typeof process&&"function"==typeof process.nextTick?process.nextTick:null;a?a(e):setTimeout(e,0)}},7028:(e,a,t)=>{"use strict";var n=t(1330)("%Object.defineProperty%",!0),i=t(4667)(),o=t(7284),s=t(8486),r=i?Symbol.toStringTag:null;e.exports=function(e,a){var t=arguments.length>2&&!!arguments[2]&&arguments[2].force,i=arguments.length>2&&!!arguments[2]&&arguments[2].nonConfigurable;if(void 0!==t&&"boolean"!=typeof t||void 0!==i&&"boolean"!=typeof i)throw new s("if provided, the `overrideIfSet` and `nonConfigurable` options must be booleans");r&&(t||!o(e,r))&&(n?n(e,r,{configurable:!i,enumerable:!1,value:a,writable:!1}):e[r]=a)}},7065:(e,a,t)=>{"use strict";var n=t(9551).parse,i={ftp:21,gopher:70,http:80,https:443,ws:80,wss:443},o=String.prototype.endsWith||function(e){return e.length<=this.length&&-1!==this.indexOf(e,this.length-e.length)};function s(e){return process.env[e.toLowerCase()]||process.env[e.toUpperCase()]||""}a.getProxyForUrl=function(e){var a,t,r,c="string"==typeof e?n(e):e||{},p=c.protocol,l=c.host,u=c.port;if("string"!=typeof l||!l||"string"!=typeof p)return"";if(p=p.split(":",1)[0],a=l=l.replace(/:\d*$/,""),t=u=parseInt(u)||i[p]||0,!(!(r=(s("npm_config_no_proxy")||s("no_proxy")).toLowerCase())||"*"!==r&&r.split(/[,\s]/).every(function(e){if(!e)return!0;var n=e.match(/^(.+):(\d+)$/),i=n?n[1]:e,s=n?parseInt(n[2]):0;return!!s&&s!==t||(/^[.*]/.test(i)?("*"===i.charAt(0)&&(i=i.slice(1)),!o.call(a,i)):a!==i)})))return"";var d=s("npm_config_"+p+"_proxy")||s(p+"_proxy")||s("npm_config_proxy")||s("all_proxy");return d&&-1===d.indexOf("://")&&(d=p+"://"+d),d}},7284:(e,a,t)=>{"use strict";var n=Function.prototype.call,i=Object.prototype.hasOwnProperty;e.exports=t(6164).call(n,i)},7910:e=>{"use strict";e.exports=require("stream")},7952:(e,a,t)=>{"use strict";var n=t(5493),i=t(3873).extname,o=/^\s*([^;\s]*)(?:;|\s|$)/,s=/^text\//i;function r(e){if(!e||"string"!=typeof e)return!1;var a=o.exec(e),t=a&&n[a[1].toLowerCase()];return t&&t.charset?t.charset:!!(a&&s.test(a[1]))&&"UTF-8"}a.charset=r,a.charsets={lookup:r},a.contentType=function(e){if(!e||"string"!=typeof e)return!1;var t=-1===e.indexOf("/")?a.lookup(e):e;if(!t)return!1;if(-1===t.indexOf("charset")){var n=a.charset(t);n&&(t+="; charset="+n.toLowerCase())}return t},a.extension=function(e){if(!e||"string"!=typeof e)return!1;var t=o.exec(e),n=t&&a.extensions[t[1].toLowerCase()];return!!n&&!!n.length&&n[0]},a.extensions=Object.create(null),a.lookup=function(e){if(!e||"string"!=typeof e)return!1;var t=i("x."+e).toLowerCase().substr(1);return!!t&&(a.types[t]||!1)},a.types=Object.create(null),function(e,a){var t=["nginx","apache",void 0,"iana"];Object.keys(n).forEach(function(i){var o=n[i],s=o.extensions;if(s&&s.length){e[i]=s;for(var r=0;r<s.length;r++){var c=s[r];if(a[c]){var p=t.indexOf(n[a[c]].source),l=t.indexOf(o.source);if("application/octet-stream"!==a[c]&&(p>l||p===l&&"application/"===a[c].substr(0,12)))continue}a[c]=i}}})}(a.extensions,a.types)},8022:e=>{"use strict";e.exports=URIError},8196:(e,a,t)=>{e.exports={parallel:t(985),serial:t(3514),serialOrdered:t(6449)}},8321:e=>{e.exports=function(e,a){var t=!Array.isArray(e),n={index:0,keyedList:t||a?Object.keys(e):null,jobs:{},results:t?{}:[],size:t?Object.keys(e).length:e.length};return a&&n.keyedList.sort(t?a:function(t,n){return a(e[t],e[n])}),n}},8335:()=>{},8354:e=>{"use strict";e.exports=require("util")},8363:e=>{"use strict";e.exports=Object},8486:e=>{"use strict";e.exports=TypeError},8490:(e,a)=>{"use strict";function t(e){return Object.keys(e).map(a=>e[a])}Object.defineProperty(a,"__esModule",{value:!0}),function(e){e.SHA1="sha1",e.SHA256="sha256",e.SHA512="sha512"}(a.HashAlgorithms||(a.HashAlgorithms={}));let n=t(a.HashAlgorithms);!function(e){e.ASCII="ascii",e.BASE64="base64",e.HEX="hex",e.LATIN1="latin1",e.UTF8="utf8"}(a.KeyEncodings||(a.KeyEncodings={}));let i=t(a.KeyEncodings);!function(e){e.HOTP="hotp",e.TOTP="totp"}(a.Strategy||(a.Strategy={}));let o=t(a.Strategy),s=()=>{throw Error("Please provide an options.createDigest implementation.")};function r(e){return/^(\d+)$/.test(e)}function c(e,a,t){if(e.length>=a)return e;let n=Array(a+1).join(t);return`${n}${e}`.slice(-1*a)}function p(e){let a=`otpauth://${e.type}/{labelPrefix}:{accountName}?secret={secret}{query}`,t=[];if(0>o.indexOf(e.type))throw Error(`Expecting options.type to be one of ${o.join(", ")}. Received ${e.type}.`);if("hotp"===e.type){if(null==e.counter||"number"!=typeof e.counter)throw Error('Expecting options.counter to be a number when options.type is "hotp".');t.push(`&counter=${e.counter}`)}return"totp"===e.type&&e.step&&t.push(`&period=${e.step}`),e.digits&&t.push(`&digits=${e.digits}`),e.algorithm&&t.push(`&algorithm=${e.algorithm.toUpperCase()}`),e.issuer&&t.push(`&issuer=${encodeURIComponent(e.issuer)}`),a.replace("{labelPrefix}",encodeURIComponent(e.issuer||e.accountName)).replace("{accountName}",encodeURIComponent(e.accountName)).replace("{secret}",e.secret).replace("{query}",t.join(""))}class l{constructor(e={}){this._defaultOptions=Object.freeze({...e}),this._options=Object.freeze({})}create(e={}){return new l(e)}clone(e={}){let a=this.create({...this._defaultOptions,...e});return a.options=this._options,a}get options(){return Object.freeze({...this._defaultOptions,...this._options})}set options(e){this._options=Object.freeze({...this._options,...e})}allOptions(){return this.options}resetOptions(){this._options=Object.freeze({})}}function u(e){if("function"!=typeof e.createDigest)throw Error("Expecting options.createDigest to be a function.");if("function"!=typeof e.createHmacKey)throw Error("Expecting options.createHmacKey to be a function.");if("number"!=typeof e.digits)throw Error("Expecting options.digits to be a number.");if(!e.algorithm||0>n.indexOf(e.algorithm))throw Error(`Expecting options.algorithm to be one of ${n.join(", ")}. Received ${e.algorithm}.`);if(!e.encoding||0>i.indexOf(e.encoding))throw Error(`Expecting options.encoding to be one of ${i.join(", ")}. Received ${e.encoding}.`)}let d=(e,a,t)=>Buffer.from(a,t).toString("hex");function m(){return{algorithm:a.HashAlgorithms.SHA1,createHmacKey:d,createDigest:s,digits:6,encoding:a.KeyEncodings.ASCII}}function f(e){let a={...m(),...e};return u(a),Object.freeze(a)}function x(e){return c(e.toString(16),16,"0")}function h(e,a){let t=Buffer.from(e,"hex"),n=15&t[t.length-1];return c(String(((127&t[n])<<24|(255&t[n+1])<<16|(255&t[n+2])<<8|255&t[n+3])%Math.pow(10,a)),a,"0")}function v(e,a,t){return h(t.digest||function(e,a,t){let n=x(a),i=t.createHmacKey(t.algorithm,e,t.encoding);return t.createDigest(t.algorithm,i,n)}(e,a,t),t.digits)}function b(e,a,t,n){return!!r(e)&&e===v(a,t,n)}function g(e,t,n,i,o){return p({algorithm:o.algorithm,digits:o.digits,type:a.Strategy.HOTP,accountName:e,counter:i,issuer:t,secret:n})}class y extends l{create(e={}){return new y(e)}allOptions(){return f(this.options)}generate(e,a){return v(e,a,this.allOptions())}check(e,a,t){return b(e,a,t,this.allOptions())}verify(e){if("object"!=typeof e)throw Error("Expecting argument 0 of verify to be an object");return this.check(e.token,e.secret,e.counter)}keyuri(e,a,t,n){return g(e,a,t,n,this.allOptions())}}function w(e){if("number"==typeof e)return[Math.abs(e),Math.abs(e)];if(Array.isArray(e)){let[a,t]=e;if("number"==typeof a&&"number"==typeof t)return[Math.abs(a),Math.abs(t)]}throw Error("Expecting options.window to be an number or [number, number].")}function k(e){if(u(e),w(e.window),"number"!=typeof e.epoch)throw Error("Expecting options.epoch to be a number.");if("number"!=typeof e.step)throw Error("Expecting options.step to be a number.")}let j=(e,a,t)=>{let n=e.length,i=Buffer.from(e,a).toString("hex");if(n<t){let e=Array(t-n+1).join(i);return Buffer.from(e,"hex").slice(0,t).toString("hex")}return i},E=(e,t,i)=>{switch(e){case a.HashAlgorithms.SHA1:return j(t,i,20);case a.HashAlgorithms.SHA256:return j(t,i,32);case a.HashAlgorithms.SHA512:return j(t,i,64);default:throw Error(`Expecting algorithm to be one of ${n.join(", ")}. Received ${e}.`)}};function S(){return{algorithm:a.HashAlgorithms.SHA1,createDigest:s,createHmacKey:E,digits:6,encoding:a.KeyEncodings.ASCII,epoch:Date.now(),step:30,window:0}}function R(e){let a={...S(),...e};return k(a),Object.freeze(a)}function _(e,a){return Math.floor(e/a/1e3)}function O(e,a){return v(e,_(a.epoch,a.step),a)}function T(e,a,t,n){let i=[];if(0===n)return i;for(let o=1;o<=n;o++){let n=a*o*t;i.push(e+n)}return i}function A(e,a,t){let n=w(t),i=1e3*a;return{current:e,past:T(e,-1,i,n[0]),future:T(e,1,i,n[1])}}function C(e,a,t){return!!r(e)&&e===O(a,t)}function F(e,a,t,n){let i=null;return e.some((e,o)=>!!C(a,t,{...n,epoch:e})&&(i=o+1,!0)),i}function D(e,a,t){if(C(e,a,t))return 0;let n=A(t.epoch,t.step,t.window),i=F(n.past,e,a,t);return null!==i?-1*i:F(n.future,e,a,t)}function P(e,a){return Math.floor(e/1e3)%a}function N(e,a){return a-P(e,a)}function z(e,t,n,i){return p({algorithm:i.algorithm,digits:i.digits,step:i.step,type:a.Strategy.TOTP,accountName:e,issuer:t,secret:n})}class L extends y{create(e={}){return new L(e)}allOptions(){return R(this.options)}generate(e){return O(e,this.allOptions())}checkDelta(e,a){return D(e,a,this.allOptions())}check(e,a){return"number"==typeof this.checkDelta(e,a)}verify(e){if("object"!=typeof e)throw Error("Expecting argument 0 of verify to be an object");return this.check(e.token,e.secret)}timeRemaining(){let e=this.allOptions();return N(e.epoch,e.step)}timeUsed(){let e=this.allOptions();return P(e.epoch,e.step)}keyuri(e,a,t){return z(e,a,t,this.allOptions())}}function M(e){if(k(e),"function"!=typeof e.keyDecoder)throw Error("Expecting options.keyDecoder to be a function.");if(e.keyEncoder&&"function"!=typeof e.keyEncoder)throw Error("Expecting options.keyEncoder to be a function.")}function B(){return{algorithm:a.HashAlgorithms.SHA1,createDigest:s,createHmacKey:E,digits:6,encoding:a.KeyEncodings.HEX,epoch:Date.now(),step:30,window:0}}function q(e){let a={...B(),...e};return M(a),Object.freeze(a)}function U(e,a){return a.keyEncoder(e,a.encoding)}function I(e,a){return a.keyDecoder(e,a.encoding)}function H(e,a){return U(a.createRandomBytes(e,a.encoding),a)}function W(e,a){return O(I(e,a),a)}function $(e,a,t){return D(e,I(a,t),t)}class G extends L{create(e={}){return new G(e)}allOptions(){return q(this.options)}generate(e){return W(e,this.allOptions())}checkDelta(e,a){return $(e,a,this.allOptions())}encode(e){return U(e,this.allOptions())}decode(e){return I(e,this.allOptions())}generateSecret(e=10){return H(e,this.allOptions())}}a.Authenticator=G,a.HASH_ALGORITHMS=n,a.HOTP=y,a.KEY_ENCODINGS=i,a.OTP=l,a.STRATEGY=o,a.TOTP=L,a.authenticatorCheckWithWindow=$,a.authenticatorDecoder=I,a.authenticatorDefaultOptions=B,a.authenticatorEncoder=U,a.authenticatorGenerateSecret=H,a.authenticatorOptionValidator=M,a.authenticatorOptions=q,a.authenticatorToken=W,a.createDigestPlaceholder=s,a.hotpCheck=b,a.hotpCounter=x,a.hotpCreateHmacKey=d,a.hotpDefaultOptions=m,a.hotpDigestToToken=h,a.hotpKeyuri=g,a.hotpOptions=f,a.hotpOptionsValidator=u,a.hotpToken=v,a.isTokenValid=r,a.keyuri=p,a.objectValues=t,a.padStart=c,a.totpCheck=C,a.totpCheckByEpoch=F,a.totpCheckWithWindow=D,a.totpCounter=_,a.totpCreateHmacKey=E,a.totpDefaultOptions=S,a.totpEpochAvailable=A,a.totpKeyuri=z,a.totpOptions=R,a.totpOptionsValidator=k,a.totpPadSecret=j,a.totpTimeRemaining=N,a.totpTimeUsed=P,a.totpToken=O},8492:(e,a,t)=>{var n=t(6992);e.exports=function(e){var a=!1;return n(function(){a=!0}),function(t,i){a?e(t,i):n(function(){e(t,i)})}}},8530:(e,a,t)=>{var n=t(8492),i=t(8790);e.exports=function(e,a,t,o){var s,r,c,p,l,u=t.keyedList?t.keyedList[t.index]:t.index;t.jobs[u]=(s=a,r=u,c=e[u],p=function(e,a){u in t.jobs&&(delete t.jobs[u],e?i(t):t.results[u]=a,o(e,t.results))},2==s.length?s(c,n(p)):s(c,r,n(p)))}},8674:(e,a,t)=>{var n=t(7910).Stream,i=t(8354);function o(){this.source=null,this.dataSize=0,this.maxDataSize=1048576,this.pauseStream=!0,this._maxDataSizeExceeded=!1,this._released=!1,this._bufferedEvents=[]}e.exports=o,i.inherits(o,n),o.create=function(e,a){var t=new this;for(var n in a=a||{})t[n]=a[n];t.source=e;var i=e.emit;return e.emit=function(){return t._handleEmit(arguments),i.apply(e,arguments)},e.on("error",function(){}),t.pauseStream&&e.pause(),t},Object.defineProperty(o.prototype,"readable",{configurable:!0,enumerable:!0,get:function(){return this.source.readable}}),o.prototype.setEncoding=function(){return this.source.setEncoding.apply(this.source,arguments)},o.prototype.resume=function(){this._released||this.release(),this.source.resume()},o.prototype.pause=function(){this.source.pause()},o.prototype.release=function(){this._released=!0,this._bufferedEvents.forEach((function(e){this.emit.apply(this,e)}).bind(this)),this._bufferedEvents=[]},o.prototype.pipe=function(){var e=n.prototype.pipe.apply(this,arguments);return this.resume(),e},o.prototype._handleEmit=function(e){if(this._released)return void this.emit.apply(this,e);"data"===e[0]&&(this.dataSize+=e[1].length,this._checkIfMaxDataSizeExceeded()),this._bufferedEvents.push(e)},o.prototype._checkIfMaxDataSizeExceeded=function(){if(!this._maxDataSizeExceeded&&!(this.dataSize<=this.maxDataSize)){this._maxDataSizeExceeded=!0;var e="DelayedStream#maxDataSize of "+this.maxDataSize+" bytes exceeded.";this.emit("error",Error(e))}}},8790:e=>{e.exports=function(e){Object.keys(e.jobs).forEach(a.bind(e)),e.jobs={}};function a(e){"function"==typeof this.jobs[e]&&this.jobs[e]()}},8794:(e,a,t)=>{"undefined"==typeof process||"renderer"===process.type||process.__nwjs?e.exports=t(5946):e.exports=t(6300)},8803:(e,a,t)=>{"use strict";var n=t(6164),i=t(3377),o=t(5149);e.exports=t(3926)||n.call(o,i)},9021:e=>{"use strict";e.exports=require("fs")},9178:(e,a,t)=>{var n=t(8354),i=t(7910).Stream,o=t(8674);function s(){this.writable=!1,this.readable=!0,this.dataSize=0,this.maxDataSize=2097152,this.pauseStreams=!0,this._released=!1,this._streams=[],this._currentStream=null,this._insideLoop=!1,this._pendingNext=!1}e.exports=s,n.inherits(s,i),s.create=function(e){var a=new this;for(var t in e=e||{})a[t]=e[t];return a},s.isStreamLike=function(e){return"function"!=typeof e&&"string"!=typeof e&&"boolean"!=typeof e&&"number"!=typeof e&&!Buffer.isBuffer(e)},s.prototype.append=function(e){if(s.isStreamLike(e)){if(!(e instanceof o)){var a=o.create(e,{maxDataSize:1/0,pauseStream:this.pauseStreams});e.on("data",this._checkDataSize.bind(this)),e=a}this._handleErrors(e),this.pauseStreams&&e.pause()}return this._streams.push(e),this},s.prototype.pipe=function(e,a){return i.prototype.pipe.call(this,e,a),this.resume(),e},s.prototype._getNext=function(){if(this._currentStream=null,this._insideLoop){this._pendingNext=!0;return}this._insideLoop=!0;try{do this._pendingNext=!1,this._realGetNext();while(this._pendingNext)}finally{this._insideLoop=!1}},s.prototype._realGetNext=function(){var e=this._streams.shift();return void 0===e?void this.end():"function"!=typeof e?void this._pipeNext(e):void e((function(e){s.isStreamLike(e)&&(e.on("data",this._checkDataSize.bind(this)),this._handleErrors(e)),this._pipeNext(e)}).bind(this))},s.prototype._pipeNext=function(e){if(this._currentStream=e,s.isStreamLike(e)){e.on("end",this._getNext.bind(this)),e.pipe(this,{end:!1});return}this.write(e),this._getNext()},s.prototype._handleErrors=function(e){var a=this;e.on("error",function(e){a._emitError(e)})},s.prototype.write=function(e){this.emit("data",e)},s.prototype.pause=function(){this.pauseStreams&&(this.pauseStreams&&this._currentStream&&"function"==typeof this._currentStream.pause&&this._currentStream.pause(),this.emit("pause"))},s.prototype.resume=function(){this._released||(this._released=!0,this.writable=!0,this._getNext()),this.pauseStreams&&this._currentStream&&"function"==typeof this._currentStream.resume&&this._currentStream.resume(),this.emit("resume")},s.prototype.end=function(){this._reset(),this.emit("end")},s.prototype.destroy=function(){this._reset(),this.emit("close")},s.prototype._reset=function(){this.writable=!1,this._streams=[],this._currentStream=null},s.prototype._checkDataSize=function(){if(this._updateDataSize(),!(this.dataSize<=this.maxDataSize)){var e="DelayedStream#maxDataSize of "+this.maxDataSize+" bytes exceeded.";this._emitError(Error(e))}},s.prototype._updateDataSize=function(){this.dataSize=0;var e=this;this._streams.forEach(function(a){a.dataSize&&(e.dataSize+=a.dataSize)}),this._currentStream&&this._currentStream.dataSize&&(this.dataSize+=this._currentStream.dataSize)},s.prototype._emitError=function(e){this._reset(),this.emit("error",e)}},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")},9581:(e,a,t)=>{"use strict";var n,i=t(3030),o=t(4198);try{n=[].__proto__===Array.prototype}catch(e){if(!e||"object"!=typeof e||!("code"in e)||"ERR_PROTO_ACCESS"!==e.code)throw e}var s=!!n&&o&&o(Object.prototype,"__proto__"),r=Object,c=r.getPrototypeOf;e.exports=s&&"function"==typeof s.get?i([s.get]):"function"==typeof c&&function(e){return c(null==e?e:r(e))}},9703:e=>{"use strict";e.exports=Math.floor}};var a=require("../../../webpack-runtime.js");a.C(e);var t=e=>a(a.s=e),n=a.X(0,[447,580],()=>t(5860));module.exports=n})();