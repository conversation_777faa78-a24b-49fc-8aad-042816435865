{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 164, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/whatif/whatif/src/lib/config/index.ts"], "sourcesContent": ["// Configuration for the What If investment analysis tool\n\nexport const APP_CONFIG = {\n  name: 'What If',\n  description: 'Investment Analysis Tool for Indian Equities',\n  version: '1.0.0',\n  author: 'What If Team',\n} as const;\n\nexport const API_CONFIG = {\n  angelOne: {\n    baseUrl: 'https://apiconnect.angelone.in',\n    version: 'v1',\n    endpoints: {\n      login: '/rest/auth/angelbroking/user/v1/loginByPassword',\n      profile: '/rest/secure/angelbroking/user/v1/getProfile',\n      historicalData: '/rest/secure/angelbroking/historical/v1/getCandleData',\n      ltp: '/rest/secure/angelbroking/order/v1/getLtpData',\n      logout: '/rest/secure/angelbroking/user/v1/logout',\n    },\n  },\n  rateLimit: {\n    requestsPerSecond: 10,\n    requestsPerMinute: 100,\n  },\n} as const;\n\nexport const MARKET_CONFIG = {\n  exchanges: ['NSE', 'BSE'] as const,\n  segments: ['EQUITY'] as const, // Excluding OPTIONS as per requirement\n  tradingHours: {\n    start: '09:15',\n    end: '15:30',\n    timezone: 'Asia/Kolkata',\n  },\n  holidays: [], // To be populated with market holidays\n} as const;\n\nexport const BENCHMARK_CONFIG = {\n  types: {\n    GOLD: {\n      name: 'Gold',\n      symbol: 'GOLD',\n      description: 'Gold prices in INR per 10 grams',\n    },\n    FD: {\n      name: 'Fixed Deposit',\n      symbol: 'FD',\n      description: 'Average FD rates from major banks',\n      defaultRate: 6.5, // Default FD rate percentage\n    },\n    NIFTY: {\n      name: 'Nifty 50',\n      symbol: 'NIFTY',\n      description: 'NSE Nifty 50 Index',\n      token: '********', // Angel One token for Nifty 50\n    },\n  },\n} as const;\n\nexport const CALCULATION_CONFIG = {\n  precision: {\n    currency: 2,\n    percentage: 2,\n    cagr: 2,\n  },\n  defaults: {\n    fdRate: 6.5, // Default FD rate percentage\n    inflationRate: 6.0, // Default inflation rate\n  },\n} as const;\n\nexport const UI_CONFIG = {\n  theme: {\n    primary: '#1f2937',\n    secondary: '#374151',\n    accent: '#3b82f6',\n    success: '#10b981',\n    warning: '#f59e0b',\n    error: '#ef4444',\n  },\n  charts: {\n    defaultHeight: 400,\n    colors: {\n      investment: '#3b82f6',\n      gold: '#f59e0b',\n      fd: '#10b981',\n      nifty: '#8b5cf6',\n    },\n  },\n} as const;\n\nexport const STORAGE_CONFIG = {\n  keys: {\n    userPreferences: 'whatif_user_preferences',\n    savedScenarios: 'whatif_saved_scenarios',\n    apiCredentials: 'whatif_api_credentials',\n  },\n  encryption: {\n    enabled: true,\n    algorithm: 'AES-256-GCM',\n  },\n} as const;\n\n// Environment-specific configuration\nexport const getEnvironmentConfig = () => {\n  const isDevelopment = process.env.NODE_ENV === 'development';\n  const isProduction = process.env.NODE_ENV === 'production';\n\n  return {\n    isDevelopment,\n    isProduction,\n    apiUrl: process.env.NEXT_PUBLIC_API_URL || API_CONFIG.angelOne.baseUrl,\n    enableLogging: isDevelopment,\n    enableAnalytics: isProduction,\n  };\n};\n"], "names": [], "mappings": "AAAA,yDAAyD;;;;;;;;;;;AAElD,MAAM,aAAa;IACxB,MAAM;IACN,aAAa;IACb,SAAS;IACT,QAAQ;AACV;AAEO,MAAM,aAAa;IACxB,UAAU;QACR,SAAS;QACT,SAAS;QACT,WAAW;YACT,OAAO;YACP,SAAS;YACT,gBAAgB;YAChB,KAAK;YACL,QAAQ;QACV;IACF;IACA,WAAW;QACT,mBAAmB;QACnB,mBAAmB;IACrB;AACF;AAEO,MAAM,gBAAgB;IAC3B,WAAW;QAAC;QAAO;KAAM;IACzB,UAAU;QAAC;KAAS;IACpB,cAAc;QACZ,OAAO;QACP,KAAK;QACL,UAAU;IACZ;IACA,UAAU,EAAE;AACd;AAEO,MAAM,mBAAmB;IAC9B,OAAO;QACL,MAAM;YAC<PERSON>,MAAM;YACN,QAAQ;YACR,aAAa;QACf;QACA,IAAI;YACF,MAAM;YACN,QAAQ;YACR,aAAa;YACb,aAAa;QACf;QACA,OAAO;YACL,MAAM;YACN,QAAQ;YACR,aAAa;YACb,OAAO;QACT;IACF;AACF;AAEO,MAAM,qBAAqB;IAChC,WAAW;QACT,UAAU;QACV,YAAY;QACZ,MAAM;IACR;IACA,UAAU;QACR,QAAQ;QACR,eAAe;IACjB;AACF;AAEO,MAAM,YAAY;IACvB,OAAO;QACL,SAAS;QACT,WAAW;QACX,QAAQ;QACR,SAAS;QACT,SAAS;QACT,OAAO;IACT;IACA,QAAQ;QACN,eAAe;QACf,QAAQ;YACN,YAAY;YACZ,MAAM;YACN,IAAI;YACJ,OAAO;QACT;IACF;AACF;AAEO,MAAM,iBAAiB;IAC5B,MAAM;QACJ,iBAAiB;QACjB,gBAAgB;QAChB,gBAAgB;IAClB;IACA,YAAY;QACV,SAAS;QACT,WAAW;IACb;AACF;AAGO,MAAM,uBAAuB;IAClC,MAAM,gBAAgB,oDAAyB;IAC/C,MAAM,eAAe,oDAAyB;IAE9C,OAAO;QACL;QACA;QACA,QAAQ,QAAQ,GAAG,CAAC,mBAAmB,IAAI,WAAW,QAAQ,CAAC,OAAO;QACtE,eAAe;QACf,iBAAiB;IACnB;AACF", "debugId": null}}, {"offset": {"line": 292, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/whatif/whatif/src/lib/security/index.ts"], "sourcesContent": ["// Security utilities for the What If investment analysis tool\n\nimport { STORAGE_CONFIG } from '../config';\n\n/**\n * Validate environment variables\n * @returns Validation result with missing variables\n */\nexport function validateEnvironment(): {\n  isValid: boolean;\n  missingVars: string[];\n  warnings: string[];\n} {\n  const requiredVars = [\n    'NEXT_PUBLIC_ANGEL_ONE_API_URL',\n    'NEXT_PUBLIC_APP_NAME',\n    'NEXT_PUBLIC_APP_VERSION',\n  ];\n\n  const optionalVars = [\n    'ANGEL_ONE_API_KEY',\n    'ANGEL_ONE_CLIENT_ID',\n    'ANGEL_ONE_PASSWORD',\n    'ANGEL_ONE_TOTP_SECRET',\n  ];\n\n  const missingVars: string[] = [];\n  const warnings: string[] = [];\n\n  // Check required variables\n  requiredVars.forEach(varName => {\n    if (!process.env[varName]) {\n      missingVars.push(varName);\n    }\n  });\n\n  // Check optional but important variables\n  optionalVars.forEach(varName => {\n    if (!process.env[varName]) {\n      warnings.push(`${varName} is not set - API functionality will be limited`);\n    }\n  });\n\n  // Check for development secrets in production\n  if (process.env.NODE_ENV === 'production') {\n    if (process.env.NEXTAUTH_SECRET === 'development_secret_change_in_production') {\n      missingVars.push('NEXTAUTH_SECRET (using development value in production)');\n    }\n  }\n\n  return {\n    isValid: missingVars.length === 0,\n    missingVars,\n    warnings,\n  };\n}\n\n/**\n * Sanitize sensitive data for logging\n * @param data - Data object to sanitize\n * @returns Sanitized data object\n */\nexport function sanitizeForLogging(data: Record<string, unknown>): Record<string, unknown> {\n  const sensitiveKeys = [\n    'password',\n    'token',\n    'secret',\n    'key',\n    'auth',\n    'credential',\n    'totp',\n  ];\n\n  const sanitized: Record<string, unknown> = {};\n\n  Object.keys(data).forEach(key => {\n    const lowerKey = key.toLowerCase();\n    const isSensitive = sensitiveKeys.some(sensitiveKey => \n      lowerKey.includes(sensitiveKey)\n    );\n\n    if (isSensitive) {\n      sanitized[key] = '[REDACTED]';\n    } else if (typeof data[key] === 'object' && data[key] !== null) {\n      sanitized[key] = sanitizeForLogging(data[key] as Record<string, unknown>);\n    } else {\n      sanitized[key] = data[key];\n    }\n  });\n\n  return sanitized;\n}\n\n/**\n * Simple encryption for local storage (basic obfuscation)\n * Note: This is not cryptographically secure, just basic obfuscation\n * @param text - Text to encrypt\n * @returns Encrypted text\n */\nexport function simpleEncrypt(text: string): string {\n  if (!STORAGE_CONFIG.encryption.enabled) {\n    return text;\n  }\n\n  // Simple base64 encoding with character shifting (not secure, just obfuscation)\n  const shifted = text\n    .split('')\n    .map(char => String.fromCharCode(char.charCodeAt(0) + 3))\n    .join('');\n  \n  return btoa(shifted);\n}\n\n/**\n * Simple decryption for local storage\n * @param encryptedText - Encrypted text to decrypt\n * @returns Decrypted text\n */\nexport function simpleDecrypt(encryptedText: string): string {\n  if (!STORAGE_CONFIG.encryption.enabled) {\n    return encryptedText;\n  }\n\n  try {\n    const decoded = atob(encryptedText);\n    return decoded\n      .split('')\n      .map(char => String.fromCharCode(char.charCodeAt(0) - 3))\n      .join('');\n  } catch {\n    return encryptedText; // Return as-is if decryption fails\n  }\n}\n\n/**\n * Validate API key format\n * @param apiKey - API key to validate\n * @returns Validation result\n */\nexport function validateApiKey(apiKey: string): {\n  isValid: boolean;\n  error?: string;\n} {\n  if (!apiKey || typeof apiKey !== 'string') {\n    return { isValid: false, error: 'API key is required' };\n  }\n\n  if (apiKey.length < 10) {\n    return { isValid: false, error: 'API key is too short' };\n  }\n\n  if (apiKey.includes(' ')) {\n    return { isValid: false, error: 'API key should not contain spaces' };\n  }\n\n  return { isValid: true };\n}\n\n/**\n * Rate limiting utility\n */\nexport class RateLimiter {\n  private requests: number[] = [];\n  private readonly maxRequests: number;\n  private readonly timeWindow: number; // in milliseconds\n\n  constructor(maxRequests: number, timeWindowSeconds: number) {\n    this.maxRequests = maxRequests;\n    this.timeWindow = timeWindowSeconds * 1000;\n  }\n\n  /**\n   * Check if request is allowed\n   * @returns Whether request is allowed\n   */\n  isAllowed(): boolean {\n    const now = Date.now();\n    \n    // Remove old requests outside the time window\n    this.requests = this.requests.filter(time => now - time < this.timeWindow);\n    \n    // Check if we're under the limit\n    if (this.requests.length < this.maxRequests) {\n      this.requests.push(now);\n      return true;\n    }\n    \n    return false;\n  }\n\n  /**\n   * Get time until next request is allowed\n   * @returns Milliseconds until next request\n   */\n  getTimeUntilReset(): number {\n    if (this.requests.length === 0) return 0;\n    \n    const oldestRequest = Math.min(...this.requests);\n    const timeUntilReset = this.timeWindow - (Date.now() - oldestRequest);\n    \n    return Math.max(0, timeUntilReset);\n  }\n}\n"], "names": [], "mappings": "AAAA,8DAA8D;;;;;;;;;AAE9D;;AAMO,SAAS;IAKd,MAAM,eAAe;QACnB;QACA;QACA;KACD;IAED,MAAM,eAAe;QACnB;QACA;QACA;QACA;KACD;IAED,MAAM,cAAwB,EAAE;IAChC,MAAM,WAAqB,EAAE;IAE7B,2BAA2B;IAC3B,aAAa,OAAO,CAAC,CAAA;QACnB,IAAI,CAAC,QAAQ,GAAG,CAAC,QAAQ,EAAE;YACzB,YAAY,IAAI,CAAC;QACnB;IACF;IAEA,yCAAyC;IACzC,aAAa,OAAO,CAAC,CAAA;QACnB,IAAI,CAAC,QAAQ,GAAG,CAAC,QAAQ,EAAE;YACzB,SAAS,IAAI,CAAC,GAAG,QAAQ,+CAA+C,CAAC;QAC3E;IACF;IAEA,8CAA8C;IAC9C,uCAA2C;;IAI3C;IAEA,OAAO;QACL,SAAS,YAAY,MAAM,KAAK;QAChC;QACA;IACF;AACF;AAOO,SAAS,mBAAmB,IAA6B;IAC9D,MAAM,gBAAgB;QACpB;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,YAAqC,CAAC;IAE5C,OAAO,IAAI,CAAC,MAAM,OAAO,CAAC,CAAA;QACxB,MAAM,WAAW,IAAI,WAAW;QAChC,MAAM,cAAc,cAAc,IAAI,CAAC,CAAA,eACrC,SAAS,QAAQ,CAAC;QAGpB,IAAI,aAAa;YACf,SAAS,CAAC,IAAI,GAAG;QACnB,OAAO,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,YAAY,IAAI,CAAC,IAAI,KAAK,MAAM;YAC9D,SAAS,CAAC,IAAI,GAAG,mBAAmB,IAAI,CAAC,IAAI;QAC/C,OAAO;YACL,SAAS,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI;QAC5B;IACF;IAEA,OAAO;AACT;AAQO,SAAS,cAAc,IAAY;IACxC,IAAI,CAAC,+HAAA,CAAA,iBAAc,CAAC,UAAU,CAAC,OAAO,EAAE;QACtC,OAAO;IACT;IAEA,gFAAgF;IAChF,MAAM,UAAU,KACb,KAAK,CAAC,IACN,GAAG,CAAC,CAAA,OAAQ,OAAO,YAAY,CAAC,KAAK,UAAU,CAAC,KAAK,IACrD,IAAI,CAAC;IAER,OAAO,KAAK;AACd;AAOO,SAAS,cAAc,aAAqB;IACjD,IAAI,CAAC,+HAAA,CAAA,iBAAc,CAAC,UAAU,CAAC,OAAO,EAAE;QACtC,OAAO;IACT;IAEA,IAAI;QACF,MAAM,UAAU,KAAK;QACrB,OAAO,QACJ,KAAK,CAAC,IACN,GAAG,CAAC,CAAA,OAAQ,OAAO,YAAY,CAAC,KAAK,UAAU,CAAC,KAAK,IACrD,IAAI,CAAC;IACV,EAAE,OAAM;QACN,OAAO,eAAe,mCAAmC;IAC3D;AACF;AAOO,SAAS,eAAe,MAAc;IAI3C,IAAI,CAAC,UAAU,OAAO,WAAW,UAAU;QACzC,OAAO;YAAE,SAAS;YAAO,OAAO;QAAsB;IACxD;IAEA,IAAI,OAAO,MAAM,GAAG,IAAI;QACtB,OAAO;YAAE,SAAS;YAAO,OAAO;QAAuB;IACzD;IAEA,IAAI,OAAO,QAAQ,CAAC,MAAM;QACxB,OAAO;YAAE,SAAS;YAAO,OAAO;QAAoC;IACtE;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAKO,MAAM;IACH,WAAqB,EAAE,CAAC;IACf,YAAoB;IACpB,WAAmB;IAEpC,YAAY,WAAmB,EAAE,iBAAyB,CAAE;QAC1D,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,UAAU,GAAG,oBAAoB;IACxC;IAEA;;;GAGC,GACD,YAAqB;QACnB,MAAM,MAAM,KAAK,GAAG;QAEpB,8CAA8C;QAC9C,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA,OAAQ,MAAM,OAAO,IAAI,CAAC,UAAU;QAEzE,iCAAiC;QACjC,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAC,WAAW,EAAE;YAC3C,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;YACnB,OAAO;QACT;QAEA,OAAO;IACT;IAEA;;;GAGC,GACD,oBAA4B;QAC1B,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,KAAK,GAAG,OAAO;QAEvC,MAAM,gBAAgB,KAAK,GAAG,IAAI,IAAI,CAAC,QAAQ;QAC/C,MAAM,iBAAiB,IAAI,CAAC,UAAU,GAAG,CAAC,KAAK,GAAG,KAAK,aAAa;QAEpE,OAAO,KAAK,GAAG,CAAC,GAAG;IACrB;AACF", "debugId": null}}, {"offset": {"line": 443, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/whatif/whatif/src/lib/api/angelone.ts"], "sourcesContent": ["// Angel One API client for the What If investment analysis tool\n\nimport axios, { AxiosInstance, AxiosResponse } from 'axios';\nimport { authenticator } from 'otplib';\nimport { API_CONFIG } from '../config';\nimport { RateLimiter, sanitizeForLogging } from '../security';\nimport {\n  AngelOneLoginRequest,\n  AngelOneLoginResponse,\n  AngelOneHistoricalRequest,\n  AngelOneHistoricalResponse,\n  AngelOneLTPRequest,\n  AngelOneLTPResponse,\n  HistoricalPrice,\n  StockData,\n} from '../types';\n\nexport class AngelOneClient {\n  private axiosInstance: AxiosInstance;\n  private jwtToken: string | null = null;\n  private refreshToken: string | null = null;\n  private feedToken: string | null = null;\n  private rateLimiter: RateLimiter;\n  private readonly apiKey: string;\n  private readonly clientId: string;\n  private readonly password: string;\n  private readonly totpSecret: string;\n\n  constructor(config: {\n    apiKey: string;\n    clientId: string;\n    password: string;\n    totpSecret: string;\n  }) {\n    this.apiKey = config.apiKey;\n    this.clientId = config.clientId;\n    this.password = config.password;\n    this.totpSecret = config.totpSecret;\n\n    // Initialize rate limiter\n    this.rateLimiter = new RateLimiter(\n      API_CONFIG.rateLimit.requestsPerSecond,\n      1\n    );\n\n    // Create axios instance\n    this.axiosInstance = axios.create({\n      baseURL: API_CONFIG.angelOne.baseUrl,\n      timeout: 30000,\n      headers: {\n        'Content-Type': 'application/json',\n        'Accept': 'application/json',\n        'X-UserType': 'USER',\n        'X-SourceID': 'WEB',\n        'X-ClientLocalIP': '127.0.0.1',\n        'X-ClientPublicIP': '127.0.0.1',\n        'X-MACAddress': '00:00:00:00:00:00',\n        'X-PrivateKey': this.apiKey,\n      },\n    });\n\n    // Add request interceptor for rate limiting\n    this.axiosInstance.interceptors.request.use(\n      async (config) => {\n        // Wait if rate limit exceeded\n        while (!this.rateLimiter.isAllowed()) {\n          const waitTime = this.rateLimiter.getTimeUntilReset();\n          await new Promise(resolve => setTimeout(resolve, waitTime));\n        }\n\n        // Add JWT token if available\n        if (this.jwtToken) {\n          config.headers.Authorization = `Bearer ${this.jwtToken}`;\n        }\n\n        return config;\n      },\n      (error) => Promise.reject(error)\n    );\n\n    // Add response interceptor for error handling\n    this.axiosInstance.interceptors.response.use(\n      (response) => response,\n      async (error) => {\n        if (error.response?.status === 401 && this.refreshToken) {\n          // Try to refresh token\n          try {\n            await this.refreshAuthToken();\n            // Retry the original request\n            return this.axiosInstance.request(error.config);\n          } catch {\n            // Refresh failed, need to re-login\n            this.clearTokens();\n            throw new Error('Authentication failed. Please login again.');\n          }\n        }\n        return Promise.reject(error);\n      }\n    );\n  }\n\n  /**\n   * Generate TOTP for authentication\n   */\n  private generateTOTP(): string {\n    return authenticator.generate(this.totpSecret);\n  }\n\n  /**\n   * Clear stored tokens\n   */\n  private clearTokens(): void {\n    this.jwtToken = null;\n    this.refreshToken = null;\n    this.feedToken = null;\n  }\n\n  /**\n   * Login to Angel One API\n   */\n  async login(): Promise<{ success: boolean; message: string }> {\n    try {\n      const totp = this.generateTOTP();\n      \n      const loginRequest: AngelOneLoginRequest = {\n        clientcode: this.clientId,\n        password: this.password,\n        totp: totp,\n      };\n\n      console.log('Attempting login with:', sanitizeForLogging(loginRequest as unknown as Record<string, unknown>));\n\n      const response: AxiosResponse<AngelOneLoginResponse> = await this.axiosInstance.post(\n        API_CONFIG.angelOne.endpoints.login,\n        loginRequest\n      );\n\n      if (response.data.status && response.data.data) {\n        this.jwtToken = response.data.data.jwtToken;\n        this.refreshToken = response.data.data.refreshToken;\n        this.feedToken = response.data.data.feedToken;\n\n        return {\n          success: true,\n          message: 'Login successful',\n        };\n      } else {\n        return {\n          success: false,\n          message: response.data.message || 'Login failed',\n        };\n      }\n    } catch (error) {\n      console.error('Login error:', sanitizeForLogging({ error } as Record<string, unknown>));\n      return {\n        success: false,\n        message: error instanceof Error ? error.message : 'Login failed',\n      };\n    }\n  }\n\n  /**\n   * Refresh authentication token\n   */\n  private async refreshAuthToken(): Promise<void> {\n    if (!this.refreshToken) {\n      throw new Error('No refresh token available');\n    }\n\n    const response = await this.axiosInstance.post('/rest/auth/angelbroking/jwt/v1/generateTokens', {\n      refreshToken: this.refreshToken,\n    });\n\n    if (response.data.status && response.data.data) {\n      this.jwtToken = response.data.data.jwtToken;\n      this.refreshToken = response.data.data.refreshToken;\n    } else {\n      throw new Error('Token refresh failed');\n    }\n  }\n\n  /**\n   * Check if client is authenticated\n   */\n  isAuthenticated(): boolean {\n    return this.jwtToken !== null;\n  }\n\n  /**\n   * Get historical data for a stock\n   */\n  async getHistoricalData(request: AngelOneHistoricalRequest): Promise<HistoricalPrice[]> {\n    if (!this.isAuthenticated()) {\n      throw new Error('Not authenticated. Please login first.');\n    }\n\n    try {\n      const response: AxiosResponse<AngelOneHistoricalResponse> = await this.axiosInstance.post(\n        API_CONFIG.angelOne.endpoints.historicalData,\n        request\n      );\n\n      if (response.data.status && response.data.data) {\n        return response.data.data.map(item => ({\n          date: new Date(item[0]),\n          open: item[1],\n          high: item[2],\n          low: item[3],\n          close: item[4],\n          volume: item[5],\n        }));\n      } else {\n        throw new Error(response.data.message || 'Failed to fetch historical data');\n      }\n    } catch (error) {\n      console.error('Historical data error:', sanitizeForLogging({ error, request } as Record<string, unknown>));\n      throw error;\n    }\n  }\n\n  /**\n   * Get current price (LTP) for a stock\n   */\n  async getCurrentPrice(request: AngelOneLTPRequest): Promise<StockData> {\n    if (!this.isAuthenticated()) {\n      throw new Error('Not authenticated. Please login first.');\n    }\n\n    try {\n      const response: AxiosResponse<AngelOneLTPResponse> = await this.axiosInstance.post(\n        API_CONFIG.angelOne.endpoints.ltp,\n        request\n      );\n\n      if (response.data.status && response.data.data) {\n        const data = response.data.data;\n        return {\n          symbol: data.tradingsymbol,\n          name: data.tradingsymbol, // Angel One doesn't provide company name in LTP response\n          exchange: data.exchange as 'NSE' | 'BSE',\n          token: data.symboltoken,\n          currentPrice: data.ltp,\n          lastUpdated: new Date(),\n        };\n      } else {\n        throw new Error(response.data.message || 'Failed to fetch current price');\n      }\n    } catch (error) {\n      console.error('Current price error:', sanitizeForLogging({ error, request } as Record<string, unknown>));\n      throw error;\n    }\n  }\n\n  /**\n   * Logout from Angel One API\n   */\n  async logout(): Promise<{ success: boolean; message: string }> {\n    if (!this.isAuthenticated()) {\n      return { success: true, message: 'Already logged out' };\n    }\n\n    try {\n      await this.axiosInstance.post(API_CONFIG.angelOne.endpoints.logout, {\n        clientcode: this.clientId,\n      });\n\n      this.clearTokens();\n      \n      return {\n        success: true,\n        message: 'Logout successful',\n      };\n    } catch (error) {\n      console.error('Logout error:', sanitizeForLogging({ error } as Record<string, unknown>));\n      this.clearTokens(); // Clear tokens anyway\n      return {\n        success: false,\n        message: error instanceof Error ? error.message : 'Logout failed',\n      };\n    }\n  }\n}\n"], "names": [], "mappings": "AAAA,gEAAgE;;;;AAEhE;AACA;AACA;AACA;;;;;AAYO,MAAM;IACH,cAA6B;IAC7B,WAA0B,KAAK;IAC/B,eAA8B,KAAK;IACnC,YAA2B,KAAK;IAChC,YAAyB;IAChB,OAAe;IACf,SAAiB;IACjB,SAAiB;IACjB,WAAmB;IAEpC,YAAY,MAKX,CAAE;QACD,IAAI,CAAC,MAAM,GAAG,OAAO,MAAM;QAC3B,IAAI,CAAC,QAAQ,GAAG,OAAO,QAAQ;QAC/B,IAAI,CAAC,QAAQ,GAAG,OAAO,QAAQ;QAC/B,IAAI,CAAC,UAAU,GAAG,OAAO,UAAU;QAEnC,0BAA0B;QAC1B,IAAI,CAAC,WAAW,GAAG,IAAI,iIAAA,CAAA,cAAW,CAChC,+HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,iBAAiB,EACtC;QAGF,wBAAwB;QACxB,IAAI,CAAC,aAAa,GAAG,uIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;YAChC,SAAS,+HAAA,CAAA,aAAU,CAAC,QAAQ,CAAC,OAAO;YACpC,SAAS;YACT,SAAS;gBACP,gBAAgB;gBAChB,UAAU;gBACV,cAAc;gBACd,cAAc;gBACd,mBAAmB;gBACnB,oBAAoB;gBACpB,gBAAgB;gBAChB,gBAAgB,IAAI,CAAC,MAAM;YAC7B;QACF;QAEA,4CAA4C;QAC5C,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CACzC,OAAO;YACL,8BAA8B;YAC9B,MAAO,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,GAAI;gBACpC,MAAM,WAAW,IAAI,CAAC,WAAW,CAAC,iBAAiB;gBACnD,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACnD;YAEA,6BAA6B;YAC7B,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACjB,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,EAAE;YAC1D;YAEA,OAAO;QACT,GACA,CAAC,QAAU,QAAQ,MAAM,CAAC;QAG5B,8CAA8C;QAC9C,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CAC1C,CAAC,WAAa,UACd,OAAO;YACL,IAAI,MAAM,QAAQ,EAAE,WAAW,OAAO,IAAI,CAAC,YAAY,EAAE;gBACvD,uBAAuB;gBACvB,IAAI;oBACF,MAAM,IAAI,CAAC,gBAAgB;oBAC3B,6BAA6B;oBAC7B,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,MAAM,MAAM;gBAChD,EAAE,OAAM;oBACN,mCAAmC;oBACnC,IAAI,CAAC,WAAW;oBAChB,MAAM,IAAI,MAAM;gBAClB;YACF;YACA,OAAO,QAAQ,MAAM,CAAC;QACxB;IAEJ;IAEA;;GAEC,GACD,AAAQ,eAAuB;QAC7B,OAAO,iIAAA,CAAA,gBAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU;IAC/C;IAEA;;GAEC,GACD,AAAQ,cAAoB;QAC1B,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,SAAS,GAAG;IACnB;IAEA;;GAEC,GACD,MAAM,QAAwD;QAC5D,IAAI;YACF,MAAM,OAAO,IAAI,CAAC,YAAY;YAE9B,MAAM,eAAqC;gBACzC,YAAY,IAAI,CAAC,QAAQ;gBACzB,UAAU,IAAI,CAAC,QAAQ;gBACvB,MAAM;YACR;YAEA,QAAQ,GAAG,CAAC,0BAA0B,CAAA,GAAA,iIAAA,CAAA,qBAAkB,AAAD,EAAE;YAEzD,MAAM,WAAiD,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAClF,+HAAA,CAAA,aAAU,CAAC,QAAQ,CAAC,SAAS,CAAC,KAAK,EACnC;YAGF,IAAI,SAAS,IAAI,CAAC,MAAM,IAAI,SAAS,IAAI,CAAC,IAAI,EAAE;gBAC9C,IAAI,CAAC,QAAQ,GAAG,SAAS,IAAI,CAAC,IAAI,CAAC,QAAQ;gBAC3C,IAAI,CAAC,YAAY,GAAG,SAAS,IAAI,CAAC,IAAI,CAAC,YAAY;gBACnD,IAAI,CAAC,SAAS,GAAG,SAAS,IAAI,CAAC,IAAI,CAAC,SAAS;gBAE7C,OAAO;oBACL,SAAS;oBACT,SAAS;gBACX;YACF,OAAO;gBACL,OAAO;oBACL,SAAS;oBACT,SAAS,SAAS,IAAI,CAAC,OAAO,IAAI;gBACpC;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gBAAgB,CAAA,GAAA,iIAAA,CAAA,qBAAkB,AAAD,EAAE;gBAAE;YAAM;YACzD,OAAO;gBACL,SAAS;gBACT,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACpD;QACF;IACF;IAEA;;GAEC,GACD,MAAc,mBAAkC;QAC9C,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACtB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,iDAAiD;YAC9F,cAAc,IAAI,CAAC,YAAY;QACjC;QAEA,IAAI,SAAS,IAAI,CAAC,MAAM,IAAI,SAAS,IAAI,CAAC,IAAI,EAAE;YAC9C,IAAI,CAAC,QAAQ,GAAG,SAAS,IAAI,CAAC,IAAI,CAAC,QAAQ;YAC3C,IAAI,CAAC,YAAY,GAAG,SAAS,IAAI,CAAC,IAAI,CAAC,YAAY;QACrD,OAAO;YACL,MAAM,IAAI,MAAM;QAClB;IACF;IAEA;;GAEC,GACD,kBAA2B;QACzB,OAAO,IAAI,CAAC,QAAQ,KAAK;IAC3B;IAEA;;GAEC,GACD,MAAM,kBAAkB,OAAkC,EAA8B;QACtF,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI;YAC3B,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI;YACF,MAAM,WAAsD,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CACvF,+HAAA,CAAA,aAAU,CAAC,QAAQ,CAAC,SAAS,CAAC,cAAc,EAC5C;YAGF,IAAI,SAAS,IAAI,CAAC,MAAM,IAAI,SAAS,IAAI,CAAC,IAAI,EAAE;gBAC9C,OAAO,SAAS,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC;wBACrC,MAAM,IAAI,KAAK,IAAI,CAAC,EAAE;wBACtB,MAAM,IAAI,CAAC,EAAE;wBACb,MAAM,IAAI,CAAC,EAAE;wBACb,KAAK,IAAI,CAAC,EAAE;wBACZ,OAAO,IAAI,CAAC,EAAE;wBACd,QAAQ,IAAI,CAAC,EAAE;oBACjB,CAAC;YACH,OAAO;gBACL,MAAM,IAAI,MAAM,SAAS,IAAI,CAAC,OAAO,IAAI;YAC3C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B,CAAA,GAAA,iIAAA,CAAA,qBAAkB,AAAD,EAAE;gBAAE;gBAAO;YAAQ;YAC5E,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,gBAAgB,OAA2B,EAAsB;QACrE,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI;YAC3B,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI;YACF,MAAM,WAA+C,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAChF,+HAAA,CAAA,aAAU,CAAC,QAAQ,CAAC,SAAS,CAAC,GAAG,EACjC;YAGF,IAAI,SAAS,IAAI,CAAC,MAAM,IAAI,SAAS,IAAI,CAAC,IAAI,EAAE;gBAC9C,MAAM,OAAO,SAAS,IAAI,CAAC,IAAI;gBAC/B,OAAO;oBACL,QAAQ,KAAK,aAAa;oBAC1B,MAAM,KAAK,aAAa;oBACxB,UAAU,KAAK,QAAQ;oBACvB,OAAO,KAAK,WAAW;oBACvB,cAAc,KAAK,GAAG;oBACtB,aAAa,IAAI;gBACnB;YACF,OAAO;gBACL,MAAM,IAAI,MAAM,SAAS,IAAI,CAAC,OAAO,IAAI;YAC3C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB,CAAA,GAAA,iIAAA,CAAA,qBAAkB,AAAD,EAAE;gBAAE;gBAAO;YAAQ;YAC1E,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,SAAyD;QAC7D,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI;YAC3B,OAAO;gBAAE,SAAS;gBAAM,SAAS;YAAqB;QACxD;QAEA,IAAI;YACF,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,+HAAA,CAAA,aAAU,CAAC,QAAQ,CAAC,SAAS,CAAC,MAAM,EAAE;gBAClE,YAAY,IAAI,CAAC,QAAQ;YAC3B;YAEA,IAAI,CAAC,WAAW;YAEhB,OAAO;gBACL,SAAS;gBACT,SAAS;YACX;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB,CAAA,GAAA,iIAAA,CAAA,qBAAkB,AAAD,EAAE;gBAAE;YAAM;YAC1D,IAAI,CAAC,WAAW,IAAI,sBAAsB;YAC1C,OAAO;gBACL,SAAS;gBACT,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACpD;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 679, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/whatif/whatif/src/lib/utils/index.ts"], "sourcesContent": ["// Utility functions for the What If investment analysis tool\n\nimport { CALCULATION_CONFIG } from '../config';\n\n/**\n * Calculate Compound Annual Growth Rate (CAGR)\n * @param initialValue - Initial investment value\n * @param finalValue - Final investment value\n * @param years - Number of years\n * @returns CAGR as a percentage\n */\nexport function calculateCAGR(\n  initialValue: number,\n  finalValue: number,\n  years: number\n): number {\n  if (initialValue <= 0 || finalValue <= 0 || years <= 0) {\n    return 0;\n  }\n  \n  const cagr = (Math.pow(finalValue / initialValue, 1 / years) - 1) * 100;\n  return Number(cagr.toFixed(CALCULATION_CONFIG.precision.cagr));\n}\n\n/**\n * Calculate absolute return\n * @param initialValue - Initial investment value\n * @param finalValue - Final investment value\n * @returns Absolute return as a percentage\n */\nexport function calculateAbsoluteReturn(\n  initialValue: number,\n  finalValue: number\n): number {\n  if (initialValue <= 0) return 0;\n  \n  const absoluteReturn = ((finalValue - initialValue) / initialValue) * 100;\n  return Number(absoluteReturn.toFixed(CALCULATION_CONFIG.precision.percentage));\n}\n\n/**\n * Calculate the number of years between two dates\n * @param startDate - Start date\n * @param endDate - End date\n * @returns Number of years (with decimals)\n */\nexport function calculateYearsBetweenDates(\n  startDate: Date,\n  endDate: Date\n): number {\n  const timeDiff = endDate.getTime() - startDate.getTime();\n  const daysDiff = timeDiff / (1000 * 3600 * 24);\n  return daysDiff / 365.25; // Account for leap years\n}\n\n/**\n * Format currency value\n * @param value - Numeric value\n * @param currency - Currency code (default: INR)\n * @returns Formatted currency string\n */\nexport function formatCurrency(value: number, currency: string = 'INR'): string {\n  return new Intl.NumberFormat('en-IN', {\n    style: 'currency',\n    currency: currency,\n    minimumFractionDigits: CALCULATION_CONFIG.precision.currency,\n    maximumFractionDigits: CALCULATION_CONFIG.precision.currency,\n  }).format(value);\n}\n\n/**\n * Format percentage value\n * @param value - Numeric value\n * @returns Formatted percentage string\n */\nexport function formatPercentage(value: number): string {\n  return `${value.toFixed(CALCULATION_CONFIG.precision.percentage)}%`;\n}\n\n/**\n * Format large numbers with Indian numbering system\n * @param value - Numeric value\n * @returns Formatted number string (e.g., 1,00,000)\n */\nexport function formatIndianNumber(value: number): string {\n  return new Intl.NumberFormat('en-IN').format(value);\n}\n\n/**\n * Validate date range\n * @param startDate - Start date\n * @param endDate - End date\n * @returns Validation result\n */\nexport function validateDateRange(startDate: Date, endDate: Date): {\n  isValid: boolean;\n  error?: string;\n} {\n  const now = new Date();\n  \n  if (startDate >= endDate) {\n    return { isValid: false, error: 'Start date must be before end date' };\n  }\n  \n  if (startDate > now) {\n    return { isValid: false, error: 'Start date cannot be in the future' };\n  }\n  \n  if (endDate > now) {\n    return { isValid: false, error: 'End date cannot be in the future' };\n  }\n  \n  const minDate = new Date('2000-01-01'); // Reasonable minimum date for stock data\n  if (startDate < minDate) {\n    return { isValid: false, error: 'Start date is too far in the past' };\n  }\n  \n  return { isValid: true };\n}\n\n/**\n * Generate unique ID\n * @returns Unique string ID\n */\nexport function generateId(): string {\n  return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\n}\n\n/**\n * Debounce function\n * @param func - Function to debounce\n * @param wait - Wait time in milliseconds\n * @returns Debounced function\n */\nexport function debounce<T extends (...args: unknown[]) => unknown>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\n/**\n * Check if market is open\n * @returns Boolean indicating if market is currently open\n */\nexport function isMarketOpen(): boolean {\n  const now = new Date();\n  const currentTime = now.toLocaleTimeString('en-IN', {\n    timeZone: 'Asia/Kolkata',\n    hour12: false,\n  });\n  \n  const isWeekday = now.getDay() >= 1 && now.getDay() <= 5;\n  const isWithinTradingHours = currentTime >= '09:15:00' && currentTime <= '15:30:00';\n  \n  return isWeekday && isWithinTradingHours;\n}\n\n/**\n * Safe JSON parse\n * @param jsonString - JSON string to parse\n * @param defaultValue - Default value if parsing fails\n * @returns Parsed object or default value\n */\nexport function safeJsonParse<T>(jsonString: string, defaultValue: T): T {\n  try {\n    return JSON.parse(jsonString);\n  } catch {\n    return defaultValue;\n  }\n}\n"], "names": [], "mappings": "AAAA,6DAA6D;;;;;;;;;;;;;;AAE7D;;AASO,SAAS,cACd,YAAoB,EACpB,UAAkB,EAClB,KAAa;IAEb,IAAI,gBAAgB,KAAK,cAAc,KAAK,SAAS,GAAG;QACtD,OAAO;IACT;IAEA,MAAM,OAAO,CAAC,KAAK,GAAG,CAAC,aAAa,cAAc,IAAI,SAAS,CAAC,IAAI;IACpE,OAAO,OAAO,KAAK,OAAO,CAAC,+HAAA,CAAA,qBAAkB,CAAC,SAAS,CAAC,IAAI;AAC9D;AAQO,SAAS,wBACd,YAAoB,EACpB,UAAkB;IAElB,IAAI,gBAAgB,GAAG,OAAO;IAE9B,MAAM,iBAAiB,AAAC,CAAC,aAAa,YAAY,IAAI,eAAgB;IACtE,OAAO,OAAO,eAAe,OAAO,CAAC,+HAAA,CAAA,qBAAkB,CAAC,SAAS,CAAC,UAAU;AAC9E;AAQO,SAAS,2BACd,SAAe,EACf,OAAa;IAEb,MAAM,WAAW,QAAQ,OAAO,KAAK,UAAU,OAAO;IACtD,MAAM,WAAW,WAAW,CAAC,OAAO,OAAO,EAAE;IAC7C,OAAO,WAAW,QAAQ,yBAAyB;AACrD;AAQO,SAAS,eAAe,KAAa,EAAE,WAAmB,KAAK;IACpE,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;QACV,uBAAuB,+HAAA,CAAA,qBAAkB,CAAC,SAAS,CAAC,QAAQ;QAC5D,uBAAuB,+HAAA,CAAA,qBAAkB,CAAC,SAAS,CAAC,QAAQ;IAC9D,GAAG,MAAM,CAAC;AACZ;AAOO,SAAS,iBAAiB,KAAa;IAC5C,OAAO,GAAG,MAAM,OAAO,CAAC,+HAAA,CAAA,qBAAkB,CAAC,SAAS,CAAC,UAAU,EAAE,CAAC,CAAC;AACrE;AAOO,SAAS,mBAAmB,KAAa;IAC9C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS,MAAM,CAAC;AAC/C;AAQO,SAAS,kBAAkB,SAAe,EAAE,OAAa;IAI9D,MAAM,MAAM,IAAI;IAEhB,IAAI,aAAa,SAAS;QACxB,OAAO;YAAE,SAAS;YAAO,OAAO;QAAqC;IACvE;IAEA,IAAI,YAAY,KAAK;QACnB,OAAO;YAAE,SAAS;YAAO,OAAO;QAAqC;IACvE;IAEA,IAAI,UAAU,KAAK;QACjB,OAAO;YAAE,SAAS;YAAO,OAAO;QAAmC;IACrE;IAEA,MAAM,UAAU,IAAI,KAAK,eAAe,yCAAyC;IACjF,IAAI,YAAY,SAAS;QACvB,OAAO;YAAE,SAAS;YAAO,OAAO;QAAoC;IACtE;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAMO,SAAS;IACd,OAAO,GAAG,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;AACnE;AAQO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAMO,SAAS;IACd,MAAM,MAAM,IAAI;IAChB,MAAM,cAAc,IAAI,kBAAkB,CAAC,SAAS;QAClD,UAAU;QACV,QAAQ;IACV;IAEA,MAAM,YAAY,IAAI,MAAM,MAAM,KAAK,IAAI,MAAM,MAAM;IACvD,MAAM,uBAAuB,eAAe,cAAc,eAAe;IAEzE,OAAO,aAAa;AACtB;AAQO,SAAS,cAAiB,UAAkB,EAAE,YAAe;IAClE,IAAI;QACF,OAAO,KAAK,KAAK,CAAC;IACpB,EAAE,OAAM;QACN,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 790, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/whatif/whatif/src/lib/services/stockData.ts"], "sourcesContent": ["// Stock data service for the What If investment analysis tool\n\nimport { format, parseISO } from 'date-fns';\nimport { AngelOneClient } from '../api/angelone';\nimport { validateDateRange, calculateYearsBetweenDates } from '../utils';\nimport {\n  HistoricalPrice,\n  StockData,\n  InvestmentScenario,\n  InvestmentResult,\n  AngelOneHistoricalRequest,\n  AngelOneLTPRequest,\n} from '../types';\n\nexport class StockDataService {\n  private angelOneClient: AngelOneClient;\n\n  constructor(angelOneClient: AngelOneClient) {\n    this.angelOneClient = angelOneClient;\n  }\n\n  /**\n   * Get historical price data for a stock\n   */\n  async getHistoricalPrices(\n    symbolToken: string,\n    exchange: 'NSE' | 'BSE',\n    startDate: Date,\n    endDate: Date,\n    interval: 'ONE_DAY' = 'ONE_DAY'\n  ): Promise<HistoricalPrice[]> {\n    // Validate date range\n    const validation = validateDateRange(startDate, endDate);\n    if (!validation.isValid) {\n      throw new Error(validation.error);\n    }\n\n    // Format dates for Angel One API\n    const fromdate = format(startDate, 'yyyy-MM-dd HH:mm');\n    const todate = format(endDate, 'yyyy-MM-dd HH:mm');\n\n    const request: AngelOneHistoricalRequest = {\n      exchange,\n      symboltoken: symbolToken,\n      interval,\n      fromdate,\n      todate,\n    };\n\n    try {\n      const historicalData = await this.angelOneClient.getHistoricalData(request);\n      \n      // Sort by date ascending\n      return historicalData.sort((a, b) => a.date.getTime() - b.date.getTime());\n    } catch (error) {\n      console.error('Error fetching historical data:', error);\n      throw new Error(`Failed to fetch historical data: ${error instanceof Error ? error.message : 'Unknown error'}`);\n    }\n  }\n\n  /**\n   * Get current stock price\n   */\n  async getCurrentPrice(\n    tradingSymbol: string,\n    symbolToken: string,\n    exchange: 'NSE' | 'BSE'\n  ): Promise<StockData> {\n    const request: AngelOneLTPRequest = {\n      exchange,\n      tradingsymbol: tradingSymbol,\n      symboltoken: symbolToken,\n    };\n\n    try {\n      return await this.angelOneClient.getCurrentPrice(request);\n    } catch (error) {\n      console.error('Error fetching current price:', error);\n      throw new Error(`Failed to fetch current price: ${error instanceof Error ? error.message : 'Unknown error'}`);\n    }\n  }\n\n  /**\n   * Calculate investment result for a scenario\n   */\n  async calculateInvestmentResult(scenario: InvestmentScenario): Promise<InvestmentResult> {\n    try {\n      // Get historical data for the investment period\n      const historicalData = await this.getHistoricalPrices(\n        scenario.stockSymbol, // Assuming stockSymbol contains the token for now\n        'NSE', // Default to NSE, should be configurable\n        scenario.startDate,\n        scenario.endDate\n      );\n\n      if (historicalData.length === 0) {\n        throw new Error('No historical data available for the specified period');\n      }\n\n      // Get start and end prices\n      const startPrice = historicalData[0].close;\n      const endPrice = historicalData[historicalData.length - 1].close;\n\n      // Calculate number of shares that could be bought\n      const numberOfShares = scenario.investmentAmount / startPrice;\n\n      // Calculate current value\n      const currentValue = numberOfShares * endPrice;\n\n      // Calculate returns\n      const absoluteReturn = ((currentValue - scenario.investmentAmount) / scenario.investmentAmount) * 100;\n      const years = calculateYearsBetweenDates(scenario.startDate, scenario.endDate);\n      const cagr = years > 0 ? (Math.pow(currentValue / scenario.investmentAmount, 1 / years) - 1) * 100 : 0;\n\n      return {\n        scenario,\n        initialValue: scenario.investmentAmount,\n        currentValue,\n        absoluteReturn,\n        cagr: Number(cagr.toFixed(2)),\n        totalReturn: currentValue - scenario.investmentAmount,\n        annualizedReturn: cagr,\n      };\n    } catch (error) {\n      console.error('Error calculating investment result:', error);\n      throw new Error(`Failed to calculate investment result: ${error instanceof Error ? error.message : 'Unknown error'}`);\n    }\n  }\n\n  /**\n   * Get price at a specific date (or closest available date)\n   */\n  async getPriceAtDate(\n    symbolToken: string,\n    exchange: 'NSE' | 'BSE',\n    targetDate: Date\n  ): Promise<{ price: number; actualDate: Date } | null> {\n    try {\n      // Get data for a small range around the target date\n      const startDate = new Date(targetDate);\n      startDate.setDate(startDate.getDate() - 5); // 5 days before\n\n      const endDate = new Date(targetDate);\n      endDate.setDate(endDate.getDate() + 5); // 5 days after\n\n      const historicalData = await this.getHistoricalPrices(\n        symbolToken,\n        exchange,\n        startDate,\n        endDate\n      );\n\n      if (historicalData.length === 0) {\n        return null;\n      }\n\n      // Find the closest date\n      let closestData = historicalData[0];\n      let minDiff = Math.abs(closestData.date.getTime() - targetDate.getTime());\n\n      for (const data of historicalData) {\n        const diff = Math.abs(data.date.getTime() - targetDate.getTime());\n        if (diff < minDiff) {\n          minDiff = diff;\n          closestData = data;\n        }\n      }\n\n      return {\n        price: closestData.close,\n        actualDate: closestData.date,\n      };\n    } catch (error) {\n      console.error('Error getting price at date:', error);\n      return null;\n    }\n  }\n\n  /**\n   * Validate stock symbol and get basic info\n   */\n  async validateStock(\n    tradingSymbol: string,\n    symbolToken: string,\n    exchange: 'NSE' | 'BSE'\n  ): Promise<{ isValid: boolean; stockData?: StockData; error?: string }> {\n    try {\n      const stockData = await this.getCurrentPrice(tradingSymbol, symbolToken, exchange);\n      return {\n        isValid: true,\n        stockData,\n      };\n    } catch (error) {\n      return {\n        isValid: false,\n        error: error instanceof Error ? error.message : 'Unknown error',\n      };\n    }\n  }\n\n  /**\n   * Get multiple stocks' current prices\n   */\n  async getMultipleCurrentPrices(\n    stocks: Array<{\n      tradingSymbol: string;\n      symbolToken: string;\n      exchange: 'NSE' | 'BSE';\n    }>\n  ): Promise<StockData[]> {\n    const results: StockData[] = [];\n    \n    // Process stocks sequentially to respect rate limits\n    for (const stock of stocks) {\n      try {\n        const stockData = await this.getCurrentPrice(\n          stock.tradingSymbol,\n          stock.symbolToken,\n          stock.exchange\n        );\n        results.push(stockData);\n      } catch (error) {\n        console.error(`Error fetching price for ${stock.tradingSymbol}:`, error);\n        // Continue with other stocks even if one fails\n      }\n    }\n\n    return results;\n  }\n}\n"], "names": [], "mappings": "AAAA,8DAA8D;;;;AAE9D;AAEA;;;AAUO,MAAM;IACH,eAA+B;IAEvC,YAAY,cAA8B,CAAE;QAC1C,IAAI,CAAC,cAAc,GAAG;IACxB;IAEA;;GAEC,GACD,MAAM,oBACJ,WAAmB,EACnB,QAAuB,EACvB,SAAe,EACf,OAAa,EACb,WAAsB,SAAS,EACH;QAC5B,sBAAsB;QACtB,MAAM,aAAa,CAAA,GAAA,8HAAA,CAAA,oBAAiB,AAAD,EAAE,WAAW;QAChD,IAAI,CAAC,WAAW,OAAO,EAAE;YACvB,MAAM,IAAI,MAAM,WAAW,KAAK;QAClC;QAEA,iCAAiC;QACjC,MAAM,WAAW,CAAA,GAAA,uJAAA,CAAA,SAAM,AAAD,EAAE,WAAW;QACnC,MAAM,SAAS,CAAA,GAAA,uJAAA,CAAA,SAAM,AAAD,EAAE,SAAS;QAE/B,MAAM,UAAqC;YACzC;YACA,aAAa;YACb;YACA;YACA;QACF;QAEA,IAAI;YACF,MAAM,iBAAiB,MAAM,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC;YAEnE,yBAAyB;YACzB,OAAO,eAAe,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,IAAI,CAAC,OAAO,KAAK,EAAE,IAAI,CAAC,OAAO;QACxE,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,MAAM,IAAI,MAAM,CAAC,iCAAiC,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;QAChH;IACF;IAEA;;GAEC,GACD,MAAM,gBACJ,aAAqB,EACrB,WAAmB,EACnB,QAAuB,EACH;QACpB,MAAM,UAA8B;YAClC;YACA,eAAe;YACf,aAAa;QACf;QAEA,IAAI;YACF,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC;QACnD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM,IAAI,MAAM,CAAC,+BAA+B,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;QAC9G;IACF;IAEA;;GAEC,GACD,MAAM,0BAA0B,QAA4B,EAA6B;QACvF,IAAI;YACF,gDAAgD;YAChD,MAAM,iBAAiB,MAAM,IAAI,CAAC,mBAAmB,CACnD,SAAS,WAAW,EACpB,OACA,SAAS,SAAS,EAClB,SAAS,OAAO;YAGlB,IAAI,eAAe,MAAM,KAAK,GAAG;gBAC/B,MAAM,IAAI,MAAM;YAClB;YAEA,2BAA2B;YAC3B,MAAM,aAAa,cAAc,CAAC,EAAE,CAAC,KAAK;YAC1C,MAAM,WAAW,cAAc,CAAC,eAAe,MAAM,GAAG,EAAE,CAAC,KAAK;YAEhE,kDAAkD;YAClD,MAAM,iBAAiB,SAAS,gBAAgB,GAAG;YAEnD,0BAA0B;YAC1B,MAAM,eAAe,iBAAiB;YAEtC,oBAAoB;YACpB,MAAM,iBAAiB,AAAC,CAAC,eAAe,SAAS,gBAAgB,IAAI,SAAS,gBAAgB,GAAI;YAClG,MAAM,QAAQ,CAAA,GAAA,8HAAA,CAAA,6BAA0B,AAAD,EAAE,SAAS,SAAS,EAAE,SAAS,OAAO;YAC7E,MAAM,OAAO,QAAQ,IAAI,CAAC,KAAK,GAAG,CAAC,eAAe,SAAS,gBAAgB,EAAE,IAAI,SAAS,CAAC,IAAI,MAAM;YAErG,OAAO;gBACL;gBACA,cAAc,SAAS,gBAAgB;gBACvC;gBACA;gBACA,MAAM,OAAO,KAAK,OAAO,CAAC;gBAC1B,aAAa,eAAe,SAAS,gBAAgB;gBACrD,kBAAkB;YACpB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;YACtD,MAAM,IAAI,MAAM,CAAC,uCAAuC,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;QACtH;IACF;IAEA;;GAEC,GACD,MAAM,eACJ,WAAmB,EACnB,QAAuB,EACvB,UAAgB,EACqC;QACrD,IAAI;YACF,oDAAoD;YACpD,MAAM,YAAY,IAAI,KAAK;YAC3B,UAAU,OAAO,CAAC,UAAU,OAAO,KAAK,IAAI,gBAAgB;YAE5D,MAAM,UAAU,IAAI,KAAK;YACzB,QAAQ,OAAO,CAAC,QAAQ,OAAO,KAAK,IAAI,eAAe;YAEvD,MAAM,iBAAiB,MAAM,IAAI,CAAC,mBAAmB,CACnD,aACA,UACA,WACA;YAGF,IAAI,eAAe,MAAM,KAAK,GAAG;gBAC/B,OAAO;YACT;YAEA,wBAAwB;YACxB,IAAI,cAAc,cAAc,CAAC,EAAE;YACnC,IAAI,UAAU,KAAK,GAAG,CAAC,YAAY,IAAI,CAAC,OAAO,KAAK,WAAW,OAAO;YAEtE,KAAK,MAAM,QAAQ,eAAgB;gBACjC,MAAM,OAAO,KAAK,GAAG,CAAC,KAAK,IAAI,CAAC,OAAO,KAAK,WAAW,OAAO;gBAC9D,IAAI,OAAO,SAAS;oBAClB,UAAU;oBACV,cAAc;gBAChB;YACF;YAEA,OAAO;gBACL,OAAO,YAAY,KAAK;gBACxB,YAAY,YAAY,IAAI;YAC9B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,OAAO;QACT;IACF;IAEA;;GAEC,GACD,MAAM,cACJ,aAAqB,EACrB,WAAmB,EACnB,QAAuB,EAC+C;QACtE,IAAI;YACF,MAAM,YAAY,MAAM,IAAI,CAAC,eAAe,CAAC,eAAe,aAAa;YACzE,OAAO;gBACL,SAAS;gBACT;YACF;QACF,EAAE,OAAO,OAAO;YACd,OAAO;gBACL,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;IACF;IAEA;;GAEC,GACD,MAAM,yBACJ,MAIE,EACoB;QACtB,MAAM,UAAuB,EAAE;QAE/B,qDAAqD;QACrD,KAAK,MAAM,SAAS,OAAQ;YAC1B,IAAI;gBACF,MAAM,YAAY,MAAM,IAAI,CAAC,eAAe,CAC1C,MAAM,aAAa,EACnB,MAAM,WAAW,EACjB,MAAM,QAAQ;gBAEhB,QAAQ,IAAI,CAAC;YACf,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,CAAC,yBAAyB,EAAE,MAAM,aAAa,CAAC,CAAC,CAAC,EAAE;YAClE,+CAA+C;YACjD;QACF;QAEA,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 950, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/whatif/whatif/src/lib/services/benchmarkData.ts"], "sourcesContent": ["// Benchmark data service for the What If investment analysis tool\n\nimport { AngelOneClient } from '../api/angelone';\nimport { BENCHMARK_CONFIG } from '../config';\nimport { calculateCAGR, calculateAbsoluteReturn, calculateYearsBetweenDates } from '../utils';\nimport { BenchmarkData } from '../types';\n\nexport class BenchmarkDataService {\n  private angelOneClient: AngelOneClient;\n\n  constructor(angelOneClient: AngelOneClient) {\n    this.angelOneClient = angelOneClient;\n  }\n\n  /**\n   * Get Nifty 50 historical data\n   */\n  async getNiftyData(startDate: Date, endDate: Date): Promise<BenchmarkData> {\n    try {\n      const historicalData = await this.angelOneClient.getHistoricalData({\n        exchange: 'NSE',\n        symboltoken: BENCHMARK_CONFIG.types.NIFTY.token,\n        interval: 'ONE_DAY',\n        fromdate: startDate.toISOString().slice(0, 16).replace('T', ' '),\n        todate: endDate.toISOString().slice(0, 16).replace('T', ' '),\n      });\n\n      return {\n        type: 'NIFTY',\n        name: BENCHMARK_CONFIG.types.NIFTY.name,\n        returns: historicalData.map(item => ({\n          date: item.date,\n          value: item.close,\n        })),\n      };\n    } catch (error) {\n      console.error('Error fetching Nifty data:', error);\n      throw new Error(`Failed to fetch Nifty data: ${error instanceof Error ? error.message : 'Unknown error'}`);\n    }\n  }\n\n  /**\n   * Get gold price data (simulated - in real implementation, would use gold API)\n   */\n  async getGoldData(startDate: Date, endDate: Date): Promise<BenchmarkData> {\n    try {\n      // Simulate gold price data with realistic growth\n      // In real implementation, this would call a gold price API\n      const returns = this.simulateGoldPrices(startDate, endDate);\n\n      return {\n        type: 'GOLD',\n        name: BENCHMARK_CONFIG.types.GOLD.name,\n        returns,\n      };\n    } catch (error) {\n      console.error('Error fetching gold data:', error);\n      throw new Error(`Failed to fetch gold data: ${error instanceof Error ? error.message : 'Unknown error'}`);\n    }\n  }\n\n  /**\n   * Get Fixed Deposit returns (calculated based on average FD rates)\n   */\n  async getFDData(startDate: Date, endDate: Date): Promise<BenchmarkData> {\n    try {\n      const returns = this.calculateFDReturns(startDate, endDate);\n\n      return {\n        type: 'FD',\n        name: BENCHMARK_CONFIG.types.FD.name,\n        returns,\n      };\n    } catch (error) {\n      console.error('Error calculating FD data:', error);\n      throw new Error(`Failed to calculate FD data: ${error instanceof Error ? error.message : 'Unknown error'}`);\n    }\n  }\n\n  /**\n   * Calculate benchmark returns for a given investment amount and period\n   */\n  async calculateBenchmarkReturns(\n    benchmarkType: 'GOLD' | 'FD' | 'NIFTY',\n    investmentAmount: number,\n    startDate: Date,\n    endDate: Date\n  ): Promise<{\n    initialValue: number;\n    currentValue: number;\n    cagr: number;\n    absoluteReturn: number;\n  }> {\n    let benchmarkData: BenchmarkData;\n\n    switch (benchmarkType) {\n      case 'NIFTY':\n        benchmarkData = await this.getNiftyData(startDate, endDate);\n        break;\n      case 'GOLD':\n        benchmarkData = await this.getGoldData(startDate, endDate);\n        break;\n      case 'FD':\n        benchmarkData = await getFDData(startDate, endDate);\n        break;\n      default:\n        throw new Error(`Unsupported benchmark type: ${benchmarkType}`);\n    }\n\n    if (benchmarkData.returns.length === 0) {\n      throw new Error(`No data available for ${benchmarkType} in the specified period`);\n    }\n\n    const startValue = benchmarkData.returns[0].value;\n    const endValue = benchmarkData.returns[benchmarkData.returns.length - 1].value;\n\n    // Calculate how much of the benchmark could be bought with the investment amount\n    const units = investmentAmount / startValue;\n    const currentValue = units * endValue;\n\n    const years = calculateYearsBetweenDates(startDate, endDate);\n    const cagr = calculateCAGR(investmentAmount, currentValue, years);\n    const absoluteReturn = calculateAbsoluteReturn(investmentAmount, currentValue);\n\n    return {\n      initialValue: investmentAmount,\n      currentValue,\n      cagr,\n      absoluteReturn,\n    };\n  }\n\n  /**\n   * Simulate gold prices with realistic growth patterns\n   * In production, this would be replaced with actual gold price API\n   */\n  private simulateGoldPrices(startDate: Date, endDate: Date): Array<{ date: Date; value: number }> {\n    const returns: Array<{ date: Date; value: number }> = [];\n    const currentDate = new Date(startDate);\n    \n    // Starting gold price (approximate INR per 10 grams)\n    let currentPrice = 50000;\n    \n    // Gold typically grows at 8-10% annually with volatility\n    const annualGrowthRate = 0.09; // 9% annual growth\n    const dailyGrowthRate = Math.pow(1 + annualGrowthRate, 1/365) - 1;\n\n    while (currentDate <= endDate) {\n      // Add some random volatility (-2% to +2% daily)\n      const volatility = (Math.random() - 0.5) * 0.04;\n      const dailyChange = dailyGrowthRate + volatility;\n      \n      currentPrice *= (1 + dailyChange);\n      \n      returns.push({\n        date: new Date(currentDate),\n        value: Math.round(currentPrice * 100) / 100, // Round to 2 decimal places\n      });\n\n      currentDate.setDate(currentDate.getDate() + 1);\n    }\n\n    return returns;\n  }\n\n  /**\n   * Calculate FD returns based on compound interest\n   */\n  private calculateFDReturns(startDate: Date, endDate: Date): Array<{ date: Date; value: number }> {\n    const returns: Array<{ date: Date; value: number }> = [];\n    const currentDate = new Date(startDate);\n    \n    const fdRate = BENCHMARK_CONFIG.types.FD.defaultRate / 100; // Convert percentage to decimal\n    const dailyRate = fdRate / 365; // Daily compound rate\n    \n    let currentValue = 100; // Starting with base value of 100\n\n    while (currentDate <= endDate) {\n      currentValue *= (1 + dailyRate);\n      \n      returns.push({\n        date: new Date(currentDate),\n        value: Math.round(currentValue * 100) / 100,\n      });\n\n      currentDate.setDate(currentDate.getDate() + 1);\n    }\n\n    return returns;\n  }\n\n  /**\n   * Get all benchmark data for comparison\n   */\n  async getAllBenchmarkData(\n    investmentAmount: number,\n    startDate: Date,\n    endDate: Date\n  ): Promise<{\n    [key: string]: {\n      initialValue: number;\n      currentValue: number;\n      cagr: number;\n      absoluteReturn: number;\n    };\n  }> {\n    const benchmarks = ['GOLD', 'FD', 'NIFTY'] as const;\n    const results: Record<string, {\n      initialValue: number;\n      currentValue: number;\n      cagr: number;\n      absoluteReturn: number;\n    }> = {};\n\n    // Process benchmarks sequentially to avoid overwhelming the API\n    for (const benchmark of benchmarks) {\n      try {\n        results[benchmark] = await this.calculateBenchmarkReturns(\n          benchmark,\n          investmentAmount,\n          startDate,\n          endDate\n        );\n      } catch (error) {\n        console.error(`Error calculating ${benchmark} returns:`, error);\n        // Continue with other benchmarks even if one fails\n        results[benchmark] = {\n          initialValue: investmentAmount,\n          currentValue: investmentAmount,\n          cagr: 0,\n          absoluteReturn: 0,\n        };\n      }\n    }\n\n    return results;\n  }\n}\n\n// Helper function to maintain compatibility\nasync function getFDData(startDate: Date, endDate: Date): Promise<BenchmarkData> {\n  // This is a temporary implementation - in a real service, this would be a method\n  const returns: Array<{ date: Date; value: number }> = [];\n  const currentDate = new Date(startDate);\n  \n  const fdRate = BENCHMARK_CONFIG.types.FD.defaultRate / 100;\n  const dailyRate = fdRate / 365;\n  \n  let currentValue = 100;\n\n  while (currentDate <= endDate) {\n    currentValue *= (1 + dailyRate);\n    \n    returns.push({\n      date: new Date(currentDate),\n      value: Math.round(currentValue * 100) / 100,\n    });\n\n    currentDate.setDate(currentDate.getDate() + 1);\n  }\n\n  return {\n    type: 'FD',\n    name: BENCHMARK_CONFIG.types.FD.name,\n    returns,\n  };\n}\n"], "names": [], "mappings": "AAAA,kEAAkE;;;;AAGlE;AACA;;;AAGO,MAAM;IACH,eAA+B;IAEvC,YAAY,cAA8B,CAAE;QAC1C,IAAI,CAAC,cAAc,GAAG;IACxB;IAEA;;GAEC,GACD,MAAM,aAAa,SAAe,EAAE,OAAa,EAA0B;QACzE,IAAI;YACF,MAAM,iBAAiB,MAAM,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC;gBACjE,UAAU;gBACV,aAAa,+HAAA,CAAA,mBAAgB,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK;gBAC/C,UAAU;gBACV,UAAU,UAAU,WAAW,GAAG,KAAK,CAAC,GAAG,IAAI,OAAO,CAAC,KAAK;gBAC5D,QAAQ,QAAQ,WAAW,GAAG,KAAK,CAAC,GAAG,IAAI,OAAO,CAAC,KAAK;YAC1D;YAEA,OAAO;gBACL,MAAM;gBACN,MAAM,+HAAA,CAAA,mBAAgB,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI;gBACvC,SAAS,eAAe,GAAG,CAAC,CAAA,OAAQ,CAAC;wBACnC,MAAM,KAAK,IAAI;wBACf,OAAO,KAAK,KAAK;oBACnB,CAAC;YACH;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,MAAM,IAAI,MAAM,CAAC,4BAA4B,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;QAC3G;IACF;IAEA;;GAEC,GACD,MAAM,YAAY,SAAe,EAAE,OAAa,EAA0B;QACxE,IAAI;YACF,iDAAiD;YACjD,2DAA2D;YAC3D,MAAM,UAAU,IAAI,CAAC,kBAAkB,CAAC,WAAW;YAEnD,OAAO;gBACL,MAAM;gBACN,MAAM,+HAAA,CAAA,mBAAgB,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI;gBACtC;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM,IAAI,MAAM,CAAC,2BAA2B,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;QAC1G;IACF;IAEA;;GAEC,GACD,MAAM,UAAU,SAAe,EAAE,OAAa,EAA0B;QACtE,IAAI;YACF,MAAM,UAAU,IAAI,CAAC,kBAAkB,CAAC,WAAW;YAEnD,OAAO;gBACL,MAAM;gBACN,MAAM,+HAAA,CAAA,mBAAgB,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI;gBACpC;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,MAAM,IAAI,MAAM,CAAC,6BAA6B,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;QAC5G;IACF;IAEA;;GAEC,GACD,MAAM,0BACJ,aAAsC,EACtC,gBAAwB,EACxB,SAAe,EACf,OAAa,EAMZ;QACD,IAAI;QAEJ,OAAQ;YACN,KAAK;gBACH,gBAAgB,MAAM,IAAI,CAAC,YAAY,CAAC,WAAW;gBACnD;YACF,KAAK;gBACH,gBAAgB,MAAM,IAAI,CAAC,WAAW,CAAC,WAAW;gBAClD;YACF,KAAK;gBACH,gBAAgB,MAAM,UAAU,WAAW;gBAC3C;YACF;gBACE,MAAM,IAAI,MAAM,CAAC,4BAA4B,EAAE,eAAe;QAClE;QAEA,IAAI,cAAc,OAAO,CAAC,MAAM,KAAK,GAAG;YACtC,MAAM,IAAI,MAAM,CAAC,sBAAsB,EAAE,cAAc,wBAAwB,CAAC;QAClF;QAEA,MAAM,aAAa,cAAc,OAAO,CAAC,EAAE,CAAC,KAAK;QACjD,MAAM,WAAW,cAAc,OAAO,CAAC,cAAc,OAAO,CAAC,MAAM,GAAG,EAAE,CAAC,KAAK;QAE9E,iFAAiF;QACjF,MAAM,QAAQ,mBAAmB;QACjC,MAAM,eAAe,QAAQ;QAE7B,MAAM,QAAQ,CAAA,GAAA,8HAAA,CAAA,6BAA0B,AAAD,EAAE,WAAW;QACpD,MAAM,OAAO,CAAA,GAAA,8HAAA,CAAA,gBAAa,AAAD,EAAE,kBAAkB,cAAc;QAC3D,MAAM,iBAAiB,CAAA,GAAA,8HAAA,CAAA,0BAAuB,AAAD,EAAE,kBAAkB;QAEjE,OAAO;YACL,cAAc;YACd;YACA;YACA;QACF;IACF;IAEA;;;GAGC,GACD,AAAQ,mBAAmB,SAAe,EAAE,OAAa,EAAwC;QAC/F,MAAM,UAAgD,EAAE;QACxD,MAAM,cAAc,IAAI,KAAK;QAE7B,qDAAqD;QACrD,IAAI,eAAe;QAEnB,yDAAyD;QACzD,MAAM,mBAAmB,MAAM,mBAAmB;QAClD,MAAM,kBAAkB,KAAK,GAAG,CAAC,IAAI,kBAAkB,IAAE,OAAO;QAEhE,MAAO,eAAe,QAAS;YAC7B,gDAAgD;YAChD,MAAM,aAAa,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;YAC3C,MAAM,cAAc,kBAAkB;YAEtC,gBAAiB,IAAI;YAErB,QAAQ,IAAI,CAAC;gBACX,MAAM,IAAI,KAAK;gBACf,OAAO,KAAK,KAAK,CAAC,eAAe,OAAO;YAC1C;YAEA,YAAY,OAAO,CAAC,YAAY,OAAO,KAAK;QAC9C;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,mBAAmB,SAAe,EAAE,OAAa,EAAwC;QAC/F,MAAM,UAAgD,EAAE;QACxD,MAAM,cAAc,IAAI,KAAK;QAE7B,MAAM,SAAS,+HAAA,CAAA,mBAAgB,CAAC,KAAK,CAAC,EAAE,CAAC,WAAW,GAAG,KAAK,gCAAgC;QAC5F,MAAM,YAAY,SAAS,KAAK,sBAAsB;QAEtD,IAAI,eAAe,KAAK,kCAAkC;QAE1D,MAAO,eAAe,QAAS;YAC7B,gBAAiB,IAAI;YAErB,QAAQ,IAAI,CAAC;gBACX,MAAM,IAAI,KAAK;gBACf,OAAO,KAAK,KAAK,CAAC,eAAe,OAAO;YAC1C;YAEA,YAAY,OAAO,CAAC,YAAY,OAAO,KAAK;QAC9C;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,MAAM,oBACJ,gBAAwB,EACxB,SAAe,EACf,OAAa,EAQZ;QACD,MAAM,aAAa;YAAC;YAAQ;YAAM;SAAQ;QAC1C,MAAM,UAKD,CAAC;QAEN,gEAAgE;QAChE,KAAK,MAAM,aAAa,WAAY;YAClC,IAAI;gBACF,OAAO,CAAC,UAAU,GAAG,MAAM,IAAI,CAAC,yBAAyB,CACvD,WACA,kBACA,WACA;YAEJ,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,CAAC,kBAAkB,EAAE,UAAU,SAAS,CAAC,EAAE;gBACzD,mDAAmD;gBACnD,OAAO,CAAC,UAAU,GAAG;oBACnB,cAAc;oBACd,cAAc;oBACd,MAAM;oBACN,gBAAgB;gBAClB;YACF;QACF;QAEA,OAAO;IACT;AACF;AAEA,4CAA4C;AAC5C,eAAe,UAAU,SAAe,EAAE,OAAa;IACrD,iFAAiF;IACjF,MAAM,UAAgD,EAAE;IACxD,MAAM,cAAc,IAAI,KAAK;IAE7B,MAAM,SAAS,+HAAA,CAAA,mBAAgB,CAAC,KAAK,CAAC,EAAE,CAAC,WAAW,GAAG;IACvD,MAAM,YAAY,SAAS;IAE3B,IAAI,eAAe;IAEnB,MAAO,eAAe,QAAS;QAC7B,gBAAiB,IAAI;QAErB,QAAQ,IAAI,CAAC;YACX,MAAM,IAAI,KAAK;YACf,OAAO,KAAK,KAAK,CAAC,eAAe,OAAO;QAC1C;QAEA,YAAY,OAAO,CAAC,YAAY,OAAO,KAAK;IAC9C;IAEA,OAAO;QACL,MAAM;QACN,MAAM,+HAAA,CAAA,mBAAgB,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI;QACpC;IACF;AACF", "debugId": null}}, {"offset": {"line": 1151, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/whatif/whatif/src/lib/services/investmentCalculator.ts"], "sourcesContent": ["// Investment calculation engine for the What If investment analysis tool\n\nimport { StockDataService } from './stockData';\nimport { BenchmarkDataService } from './benchmarkData';\nimport { \n  calculateCAGR, \n  calculateAbsoluteReturn, \n  calculateYearsBetweenDates,\n  validateDateRange,\n  generateId \n} from '../utils';\nimport {\n  InvestmentScenario,\n  InvestmentResult,\n  ComparisonResult,\n  HistoricalPrice,\n} from '../types';\n\nexport class InvestmentCalculator {\n  private stockDataService: StockDataService;\n  private benchmarkDataService: BenchmarkDataService;\n\n  constructor(\n    stockDataService: StockDataService,\n    benchmarkDataService: BenchmarkDataService\n  ) {\n    this.stockDataService = stockDataService;\n    this.benchmarkDataService = benchmarkDataService;\n  }\n\n  /**\n   * Create a new investment scenario\n   */\n  createScenario(\n    stockSymbol: string,\n    investmentAmount: number,\n    startDate: Date,\n    endDate: Date = new Date()\n  ): InvestmentScenario {\n    // Validate inputs\n    if (investmentAmount <= 0) {\n      throw new Error('Investment amount must be greater than 0');\n    }\n\n    const validation = validateDateRange(startDate, endDate);\n    if (!validation.isValid) {\n      throw new Error(validation.error);\n    }\n\n    return {\n      id: generateId(),\n      stockSymbol,\n      investmentAmount,\n      startDate,\n      endDate,\n      createdAt: new Date(),\n    };\n  }\n\n  /**\n   * Calculate investment result for a scenario\n   */\n  async calculateInvestmentResult(scenario: InvestmentScenario): Promise<InvestmentResult> {\n    try {\n      return await this.stockDataService.calculateInvestmentResult(scenario);\n    } catch (error) {\n      console.error('Error calculating investment result:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Calculate investment result with benchmark comparisons\n   */\n  async calculateWithComparisons(scenario: InvestmentScenario): Promise<ComparisonResult> {\n    try {\n      // Calculate the main investment result\n      const investmentResult = await this.calculateInvestmentResult(scenario);\n\n      // Calculate benchmark returns\n      const benchmarkReturns = await this.benchmarkDataService.getAllBenchmarkData(\n        scenario.investmentAmount,\n        scenario.startDate,\n        scenario.endDate\n      );\n\n      return {\n        investment: investmentResult,\n        benchmarks: benchmarkReturns,\n      };\n    } catch (error) {\n      console.error('Error calculating investment with comparisons:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Calculate returns for multiple investment amounts (sensitivity analysis)\n   */\n  async calculateSensitivityAnalysis(\n    stockSymbol: string,\n    baseAmount: number,\n    startDate: Date,\n    endDate: Date,\n    variations: number[] = [0.5, 0.75, 1, 1.25, 1.5, 2] // Multipliers\n  ): Promise<Array<{\n    amount: number;\n    result: InvestmentResult;\n  }>> {\n    const results: Array<{ amount: number; result: InvestmentResult }> = [];\n\n    for (const multiplier of variations) {\n      const amount = baseAmount * multiplier;\n      const scenario = this.createScenario(stockSymbol, amount, startDate, endDate);\n      \n      try {\n        const result = await this.calculateInvestmentResult(scenario);\n        results.push({ amount, result });\n      } catch (error) {\n        console.error(`Error calculating for amount ${amount}:`, error);\n        // Continue with other amounts\n      }\n    }\n\n    return results;\n  }\n\n  /**\n   * Calculate returns for different time periods (time-based analysis)\n   */\n  async calculateTimeBasedAnalysis(\n    stockSymbol: string,\n    investmentAmount: number,\n    baseStartDate: Date,\n    periods: Array<{ label: string; months: number }> = [\n      { label: '6 months', months: 6 },\n      { label: '1 year', months: 12 },\n      { label: '2 years', months: 24 },\n      { label: '3 years', months: 36 },\n      { label: '5 years', months: 60 },\n    ]\n  ): Promise<Array<{\n    period: string;\n    startDate: Date;\n    endDate: Date;\n    result: InvestmentResult;\n  }>> {\n    const results: Array<{\n      period: string;\n      startDate: Date;\n      endDate: Date;\n      result: InvestmentResult;\n    }> = [];\n\n    for (const period of periods) {\n      const startDate = new Date(baseStartDate);\n      const endDate = new Date(baseStartDate);\n      endDate.setMonth(endDate.getMonth() + period.months);\n\n      // Don't calculate for future dates\n      if (endDate > new Date()) {\n        continue;\n      }\n\n      const scenario = this.createScenario(stockSymbol, investmentAmount, startDate, endDate);\n      \n      try {\n        const result = await this.calculateInvestmentResult(scenario);\n        results.push({\n          period: period.label,\n          startDate,\n          endDate,\n          result,\n        });\n      } catch (error) {\n        console.error(`Error calculating for period ${period.label}:`, error);\n        // Continue with other periods\n      }\n    }\n\n    return results;\n  }\n\n  /**\n   * Calculate SIP (Systematic Investment Plan) returns\n   */\n  async calculateSIPReturns(\n    stockSymbol: string,\n    monthlyAmount: number,\n    startDate: Date,\n    endDate: Date\n  ): Promise<{\n    totalInvested: number;\n    currentValue: number;\n    totalReturn: number;\n    cagr: number;\n    absoluteReturn: number;\n    installments: Array<{\n      date: Date;\n      amount: number;\n      price: number;\n      units: number;\n      cumulativeUnits: number;\n      cumulativeInvestment: number;\n    }>;\n  }> {\n    const installments: Array<{\n      date: Date;\n      amount: number;\n      price: number;\n      units: number;\n      cumulativeUnits: number;\n      cumulativeInvestment: number;\n    }> = [];\n\n    let totalInvested = 0;\n    let totalUnits = 0;\n    const currentDate = new Date(startDate);\n\n    // Calculate monthly investments\n    while (currentDate <= endDate) {\n      try {\n        // Get price at this date (or closest available)\n        const priceData = await this.stockDataService.getPriceAtDate(\n          stockSymbol,\n          'NSE', // Default to NSE\n          currentDate\n        );\n\n        if (priceData) {\n          const units = monthlyAmount / priceData.price;\n          totalUnits += units;\n          totalInvested += monthlyAmount;\n\n          installments.push({\n            date: new Date(currentDate),\n            amount: monthlyAmount,\n            price: priceData.price,\n            units,\n            cumulativeUnits: totalUnits,\n            cumulativeInvestment: totalInvested,\n          });\n        }\n      } catch (error) {\n        console.error(`Error calculating SIP for ${currentDate}:`, error);\n        // Continue with next month\n      }\n\n      // Move to next month\n      currentDate.setMonth(currentDate.getMonth() + 1);\n    }\n\n    if (installments.length === 0) {\n      throw new Error('No SIP installments could be calculated');\n    }\n\n    // Get current price to calculate current value\n    try {\n      const currentPriceData = await this.stockDataService.getCurrentPrice(\n        stockSymbol.split('-')[0], // Extract symbol from token\n        stockSymbol,\n        'NSE'\n      );\n\n      const currentValue = totalUnits * currentPriceData.currentPrice;\n      const totalReturn = currentValue - totalInvested;\n      const years = calculateYearsBetweenDates(startDate, endDate);\n      const cagr = calculateCAGR(totalInvested, currentValue, years);\n      const absoluteReturn = calculateAbsoluteReturn(totalInvested, currentValue);\n\n      return {\n        totalInvested,\n        currentValue,\n        totalReturn,\n        cagr,\n        absoluteReturn,\n        installments,\n      };\n    } catch (error) {\n      throw new Error(`Failed to calculate SIP returns: ${error instanceof Error ? error.message : 'Unknown error'}`);\n    }\n  }\n\n  /**\n   * Calculate optimal investment timing (dollar-cost averaging analysis)\n   */\n  async calculateOptimalTiming(\n    stockSymbol: string,\n    totalAmount: number,\n    startDate: Date,\n    endDate: Date,\n    strategies: Array<{\n      name: string;\n      type: 'lump_sum' | 'monthly_sip' | 'quarterly_sip';\n    }> = [\n      { name: 'Lump Sum at Start', type: 'lump_sum' },\n      { name: 'Monthly SIP', type: 'monthly_sip' },\n      { name: 'Quarterly SIP', type: 'quarterly_sip' },\n    ]\n  ): Promise<Array<{\n    strategy: string;\n    totalInvested: number;\n    currentValue: number;\n    cagr: number;\n    absoluteReturn: number;\n  }>> {\n    const results: Array<{\n      strategy: string;\n      totalInvested: number;\n      currentValue: number;\n      cagr: number;\n      absoluteReturn: number;\n    }> = [];\n\n    for (const strategy of strategies) {\n      try {\n        let result;\n\n        switch (strategy.type) {\n          case 'lump_sum':\n            const scenario = this.createScenario(stockSymbol, totalAmount, startDate, endDate);\n            const lumpSumResult = await this.calculateInvestmentResult(scenario);\n            result = {\n              strategy: strategy.name,\n              totalInvested: lumpSumResult.initialValue,\n              currentValue: lumpSumResult.currentValue,\n              cagr: lumpSumResult.cagr,\n              absoluteReturn: lumpSumResult.absoluteReturn,\n            };\n            break;\n\n          case 'monthly_sip':\n            const months = Math.ceil(calculateYearsBetweenDates(startDate, endDate) * 12);\n            const monthlyAmount = totalAmount / months;\n            const monthlySipResult = await this.calculateSIPReturns(\n              stockSymbol,\n              monthlyAmount,\n              startDate,\n              endDate\n            );\n            result = {\n              strategy: strategy.name,\n              totalInvested: monthlySipResult.totalInvested,\n              currentValue: monthlySipResult.currentValue,\n              cagr: monthlySipResult.cagr,\n              absoluteReturn: monthlySipResult.absoluteReturn,\n            };\n            break;\n\n          case 'quarterly_sip':\n            const quarters = Math.ceil(calculateYearsBetweenDates(startDate, endDate) * 4);\n            const quarterlyAmount = totalAmount / quarters;\n            // For quarterly, we'll simulate with 3-month intervals\n            const quarterlySipResult = await this.calculateSIPReturns(\n              stockSymbol,\n              quarterlyAmount,\n              startDate,\n              endDate\n            );\n            result = {\n              strategy: strategy.name,\n              totalInvested: quarterlySipResult.totalInvested,\n              currentValue: quarterlySipResult.currentValue,\n              cagr: quarterlySipResult.cagr,\n              absoluteReturn: quarterlySipResult.absoluteReturn,\n            };\n            break;\n\n          default:\n            continue;\n        }\n\n        results.push(result);\n      } catch (error) {\n        console.error(`Error calculating ${strategy.name}:`, error);\n        // Continue with other strategies\n      }\n    }\n\n    return results;\n  }\n}\n"], "names": [], "mappings": "AAAA,yEAAyE;;;;AAIzE;;AAcO,MAAM;IACH,iBAAmC;IACnC,qBAA2C;IAEnD,YACE,gBAAkC,EAClC,oBAA0C,CAC1C;QACA,IAAI,CAAC,gBAAgB,GAAG;QACxB,IAAI,CAAC,oBAAoB,GAAG;IAC9B;IAEA;;GAEC,GACD,eACE,WAAmB,EACnB,gBAAwB,EACxB,SAAe,EACf,UAAgB,IAAI,MAAM,EACN;QACpB,kBAAkB;QAClB,IAAI,oBAAoB,GAAG;YACzB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,aAAa,CAAA,GAAA,8HAAA,CAAA,oBAAiB,AAAD,EAAE,WAAW;QAChD,IAAI,CAAC,WAAW,OAAO,EAAE;YACvB,MAAM,IAAI,MAAM,WAAW,KAAK;QAClC;QAEA,OAAO;YACL,IAAI,CAAA,GAAA,8HAAA,CAAA,aAAU,AAAD;YACb;YACA;YACA;YACA;YACA,WAAW,IAAI;QACjB;IACF;IAEA;;GAEC,GACD,MAAM,0BAA0B,QAA4B,EAA6B;QACvF,IAAI;YACF,OAAO,MAAM,IAAI,CAAC,gBAAgB,CAAC,yBAAyB,CAAC;QAC/D,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;YACtD,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,yBAAyB,QAA4B,EAA6B;QACtF,IAAI;YACF,uCAAuC;YACvC,MAAM,mBAAmB,MAAM,IAAI,CAAC,yBAAyB,CAAC;YAE9D,8BAA8B;YAC9B,MAAM,mBAAmB,MAAM,IAAI,CAAC,oBAAoB,CAAC,mBAAmB,CAC1E,SAAS,gBAAgB,EACzB,SAAS,SAAS,EAClB,SAAS,OAAO;YAGlB,OAAO;gBACL,YAAY;gBACZ,YAAY;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kDAAkD;YAChE,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,6BACJ,WAAmB,EACnB,UAAkB,EAClB,SAAe,EACf,OAAa,EACb,aAAuB;QAAC;QAAK;QAAM;QAAG;QAAM;QAAK;KAAE,CAAC,cAAc;IAAf,EAIjD;QACF,MAAM,UAA+D,EAAE;QAEvE,KAAK,MAAM,cAAc,WAAY;YACnC,MAAM,SAAS,aAAa;YAC5B,MAAM,WAAW,IAAI,CAAC,cAAc,CAAC,aAAa,QAAQ,WAAW;YAErE,IAAI;gBACF,MAAM,SAAS,MAAM,IAAI,CAAC,yBAAyB,CAAC;gBACpD,QAAQ,IAAI,CAAC;oBAAE;oBAAQ;gBAAO;YAChC,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,CAAC,6BAA6B,EAAE,OAAO,CAAC,CAAC,EAAE;YACzD,8BAA8B;YAChC;QACF;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,MAAM,2BACJ,WAAmB,EACnB,gBAAwB,EACxB,aAAmB,EACnB,UAAoD;QAClD;YAAE,OAAO;YAAY,QAAQ;QAAE;QAC/B;YAAE,OAAO;YAAU,QAAQ;QAAG;QAC9B;YAAE,OAAO;YAAW,QAAQ;QAAG;QAC/B;YAAE,OAAO;YAAW,QAAQ;QAAG;QAC/B;YAAE,OAAO;YAAW,QAAQ;QAAG;KAChC,EAMC;QACF,MAAM,UAKD,EAAE;QAEP,KAAK,MAAM,UAAU,QAAS;YAC5B,MAAM,YAAY,IAAI,KAAK;YAC3B,MAAM,UAAU,IAAI,KAAK;YACzB,QAAQ,QAAQ,CAAC,QAAQ,QAAQ,KAAK,OAAO,MAAM;YAEnD,mCAAmC;YACnC,IAAI,UAAU,IAAI,QAAQ;gBACxB;YACF;YAEA,MAAM,WAAW,IAAI,CAAC,cAAc,CAAC,aAAa,kBAAkB,WAAW;YAE/E,IAAI;gBACF,MAAM,SAAS,MAAM,IAAI,CAAC,yBAAyB,CAAC;gBACpD,QAAQ,IAAI,CAAC;oBACX,QAAQ,OAAO,KAAK;oBACpB;oBACA;oBACA;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,CAAC,6BAA6B,EAAE,OAAO,KAAK,CAAC,CAAC,CAAC,EAAE;YAC/D,8BAA8B;YAChC;QACF;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,MAAM,oBACJ,WAAmB,EACnB,aAAqB,EACrB,SAAe,EACf,OAAa,EAeZ;QACD,MAAM,eAOD,EAAE;QAEP,IAAI,gBAAgB;QACpB,IAAI,aAAa;QACjB,MAAM,cAAc,IAAI,KAAK;QAE7B,gCAAgC;QAChC,MAAO,eAAe,QAAS;YAC7B,IAAI;gBACF,gDAAgD;gBAChD,MAAM,YAAY,MAAM,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAC1D,aACA,OACA;gBAGF,IAAI,WAAW;oBACb,MAAM,QAAQ,gBAAgB,UAAU,KAAK;oBAC7C,cAAc;oBACd,iBAAiB;oBAEjB,aAAa,IAAI,CAAC;wBAChB,MAAM,IAAI,KAAK;wBACf,QAAQ;wBACR,OAAO,UAAU,KAAK;wBACtB;wBACA,iBAAiB;wBACjB,sBAAsB;oBACxB;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,CAAC,0BAA0B,EAAE,YAAY,CAAC,CAAC,EAAE;YAC3D,2BAA2B;YAC7B;YAEA,qBAAqB;YACrB,YAAY,QAAQ,CAAC,YAAY,QAAQ,KAAK;QAChD;QAEA,IAAI,aAAa,MAAM,KAAK,GAAG;YAC7B,MAAM,IAAI,MAAM;QAClB;QAEA,+CAA+C;QAC/C,IAAI;YACF,MAAM,mBAAmB,MAAM,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAClE,YAAY,KAAK,CAAC,IAAI,CAAC,EAAE,EACzB,aACA;YAGF,MAAM,eAAe,aAAa,iBAAiB,YAAY;YAC/D,MAAM,cAAc,eAAe;YACnC,MAAM,QAAQ,CAAA,GAAA,8HAAA,CAAA,6BAA0B,AAAD,EAAE,WAAW;YACpD,MAAM,OAAO,CAAA,GAAA,8HAAA,CAAA,gBAAa,AAAD,EAAE,eAAe,cAAc;YACxD,MAAM,iBAAiB,CAAA,GAAA,8HAAA,CAAA,0BAAuB,AAAD,EAAE,eAAe;YAE9D,OAAO;gBACL;gBACA;gBACA;gBACA;gBACA;gBACA;YACF;QACF,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,MAAM,CAAC,iCAAiC,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;QAChH;IACF;IAEA;;GAEC,GACD,MAAM,uBACJ,WAAmB,EACnB,WAAmB,EACnB,SAAe,EACf,OAAa,EACb,aAGK;QACH;YAAE,MAAM;YAAqB,MAAM;QAAW;QAC9C;YAAE,MAAM;YAAe,MAAM;QAAc;QAC3C;YAAE,MAAM;YAAiB,MAAM;QAAgB;KAChD,EAOC;QACF,MAAM,UAMD,EAAE;QAEP,KAAK,MAAM,YAAY,WAAY;YACjC,IAAI;gBACF,IAAI;gBAEJ,OAAQ,SAAS,IAAI;oBACnB,KAAK;wBACH,MAAM,WAAW,IAAI,CAAC,cAAc,CAAC,aAAa,aAAa,WAAW;wBAC1E,MAAM,gBAAgB,MAAM,IAAI,CAAC,yBAAyB,CAAC;wBAC3D,SAAS;4BACP,UAAU,SAAS,IAAI;4BACvB,eAAe,cAAc,YAAY;4BACzC,cAAc,cAAc,YAAY;4BACxC,MAAM,cAAc,IAAI;4BACxB,gBAAgB,cAAc,cAAc;wBAC9C;wBACA;oBAEF,KAAK;wBACH,MAAM,SAAS,KAAK,IAAI,CAAC,CAAA,GAAA,8HAAA,CAAA,6BAA0B,AAAD,EAAE,WAAW,WAAW;wBAC1E,MAAM,gBAAgB,cAAc;wBACpC,MAAM,mBAAmB,MAAM,IAAI,CAAC,mBAAmB,CACrD,aACA,eACA,WACA;wBAEF,SAAS;4BACP,UAAU,SAAS,IAAI;4BACvB,eAAe,iBAAiB,aAAa;4BAC7C,cAAc,iBAAiB,YAAY;4BAC3C,MAAM,iBAAiB,IAAI;4BAC3B,gBAAgB,iBAAiB,cAAc;wBACjD;wBACA;oBAEF,KAAK;wBACH,MAAM,WAAW,KAAK,IAAI,CAAC,CAAA,GAAA,8HAAA,CAAA,6BAA0B,AAAD,EAAE,WAAW,WAAW;wBAC5E,MAAM,kBAAkB,cAAc;wBACtC,uDAAuD;wBACvD,MAAM,qBAAqB,MAAM,IAAI,CAAC,mBAAmB,CACvD,aACA,iBACA,WACA;wBAEF,SAAS;4BACP,UAAU,SAAS,IAAI;4BACvB,eAAe,mBAAmB,aAAa;4BAC/C,cAAc,mBAAmB,YAAY;4BAC7C,MAAM,mBAAmB,IAAI;4BAC7B,gBAAgB,mBAAmB,cAAc;wBACnD;wBACA;oBAEF;wBACE;gBACJ;gBAEA,QAAQ,IAAI,CAAC;YACf,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,CAAC,kBAAkB,EAAE,SAAS,IAAI,CAAC,CAAC,CAAC,EAAE;YACrD,iCAAiC;YACnC;QACF;QAEA,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 1418, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/whatif/whatif/src/lib/services/comparisonService.ts"], "sourcesContent": ["// Comparison service for the What If investment analysis tool\n\nimport { InvestmentCalculator } from './investmentCalculator';\nimport { BenchmarkDataService } from './benchmarkData';\nimport { formatCurrency, formatPercentage, calculateYearsBetweenDates } from '../utils';\nimport {\n  InvestmentScenario,\n  InvestmentResult,\n  ComparisonResult,\n  ChartDataPoint,\n} from '../types';\n\nexport interface ComparisonSummary {\n  investment: {\n    name: string;\n    initialValue: number;\n    currentValue: number;\n    totalReturn: number;\n    cagr: number;\n    absoluteReturn: number;\n    rank: number;\n  };\n  benchmarks: Array<{\n    name: string;\n    type: string;\n    initialValue: number;\n    currentValue: number;\n    totalReturn: number;\n    cagr: number;\n    absoluteReturn: number;\n    rank: number;\n    outperformance: number; // How much better/worse than investment\n  }>;\n  insights: string[];\n}\n\nexport interface PerformanceMetrics {\n  bestPerformer: {\n    name: string;\n    cagr: number;\n    absoluteReturn: number;\n  };\n  worstPerformer: {\n    name: string;\n    cagr: number;\n    absoluteReturn: number;\n  };\n  averageCagr: number;\n  volatilityRanking: Array<{\n    name: string;\n    volatility: number;\n  }>;\n}\n\nexport class ComparisonService {\n  private investmentCalculator: InvestmentCalculator;\n  private benchmarkDataService: BenchmarkDataService;\n\n  constructor(\n    investmentCalculator: InvestmentCalculator,\n    benchmarkDataService: BenchmarkDataService\n  ) {\n    this.investmentCalculator = investmentCalculator;\n    this.benchmarkDataService = benchmarkDataService;\n  }\n\n  /**\n   * Generate comprehensive comparison summary\n   */\n  async generateComparisonSummary(scenario: InvestmentScenario): Promise<ComparisonSummary> {\n    try {\n      const comparisonResult = await this.investmentCalculator.calculateWithComparisons(scenario);\n      \n      // Prepare investment data\n      const investment = {\n        name: `${scenario.stockSymbol} Investment`,\n        initialValue: comparisonResult.investment.initialValue,\n        currentValue: comparisonResult.investment.currentValue,\n        totalReturn: comparisonResult.investment.totalReturn,\n        cagr: comparisonResult.investment.cagr,\n        absoluteReturn: comparisonResult.investment.absoluteReturn,\n        rank: 0, // Will be calculated below\n      };\n\n      // Prepare benchmark data\n      const benchmarks = Object.entries(comparisonResult.benchmarks).map(([type, data]) => ({\n        name: this.getBenchmarkDisplayName(type),\n        type,\n        initialValue: data.initialValue,\n        currentValue: data.currentValue,\n        totalReturn: data.currentValue - data.initialValue,\n        cagr: data.cagr,\n        absoluteReturn: data.absoluteReturn,\n        rank: 0, // Will be calculated below\n        outperformance: investment.cagr - data.cagr,\n      }));\n\n      // Calculate rankings based on CAGR\n      const allInvestments = [investment, ...benchmarks];\n      allInvestments.sort((a, b) => b.cagr - a.cagr);\n      allInvestments.forEach((item, index) => {\n        item.rank = index + 1;\n      });\n\n      // Generate insights\n      const insights = this.generateInsights(investment, benchmarks, scenario);\n\n      return {\n        investment,\n        benchmarks,\n        insights,\n      };\n    } catch (error) {\n      console.error('Error generating comparison summary:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Calculate performance metrics across all investments\n   */\n  calculatePerformanceMetrics(comparisonSummary: ComparisonSummary): PerformanceMetrics {\n    const allInvestments = [comparisonSummary.investment, ...comparisonSummary.benchmarks];\n    \n    // Find best and worst performers\n    const sortedByCagr = [...allInvestments].sort((a, b) => b.cagr - a.cagr);\n    const bestPerformer = {\n      name: sortedByCagr[0].name,\n      cagr: sortedByCagr[0].cagr,\n      absoluteReturn: sortedByCagr[0].absoluteReturn,\n    };\n    const worstPerformer = {\n      name: sortedByCagr[sortedByCagr.length - 1].name,\n      cagr: sortedByCagr[sortedByCagr.length - 1].cagr,\n      absoluteReturn: sortedByCagr[sortedByCagr.length - 1].absoluteReturn,\n    };\n\n    // Calculate average CAGR\n    const averageCagr = allInvestments.reduce((sum, inv) => sum + inv.cagr, 0) / allInvestments.length;\n\n    // Calculate volatility ranking (simplified - based on absolute return variance from CAGR)\n    const volatilityRanking = allInvestments.map(inv => ({\n      name: inv.name,\n      volatility: Math.abs(inv.absoluteReturn - inv.cagr), // Simplified volatility measure\n    })).sort((a, b) => a.volatility - b.volatility);\n\n    return {\n      bestPerformer,\n      worstPerformer,\n      averageCagr: Number(averageCagr.toFixed(2)),\n      volatilityRanking,\n    };\n  }\n\n  /**\n   * Generate chart data for comparison visualization\n   */\n  async generateComparisonChartData(scenario: InvestmentScenario): Promise<{\n    timeSeriesData: Array<{\n      date: string;\n      investment: number;\n      gold: number;\n      fd: number;\n      nifty: number;\n    }>;\n    barChartData: Array<{\n      name: string;\n      cagr: number;\n      absoluteReturn: number;\n      currentValue: number;\n    }>;\n  }> {\n    try {\n      const comparisonResult = await this.investmentCalculator.calculateWithComparisons(scenario);\n      \n      // For time series, we'll need to get historical data for all benchmarks\n      // This is a simplified version - in production, you'd want actual time series data\n      const timeSeriesData = await this.generateTimeSeriesData(scenario, comparisonResult);\n      \n      // Bar chart data for final comparison\n      const barChartData = [\n        {\n          name: scenario.stockSymbol,\n          cagr: comparisonResult.investment.cagr,\n          absoluteReturn: comparisonResult.investment.absoluteReturn,\n          currentValue: comparisonResult.investment.currentValue,\n        },\n        ...Object.entries(comparisonResult.benchmarks).map(([type, data]) => ({\n          name: this.getBenchmarkDisplayName(type),\n          cagr: data.cagr,\n          absoluteReturn: data.absoluteReturn,\n          currentValue: data.currentValue,\n        })),\n      ];\n\n      return {\n        timeSeriesData,\n        barChartData,\n      };\n    } catch (error) {\n      console.error('Error generating chart data:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Compare multiple stocks against benchmarks\n   */\n  async compareMultipleStocks(\n    scenarios: InvestmentScenario[]\n  ): Promise<Array<{\n    scenario: InvestmentScenario;\n    summary: ComparisonSummary;\n  }>> {\n    const results: Array<{\n      scenario: InvestmentScenario;\n      summary: ComparisonSummary;\n    }> = [];\n\n    for (const scenario of scenarios) {\n      try {\n        const summary = await this.generateComparisonSummary(scenario);\n        results.push({ scenario, summary });\n      } catch (error) {\n        console.error(`Error comparing scenario ${scenario.id}:`, error);\n        // Continue with other scenarios\n      }\n    }\n\n    return results;\n  }\n\n  /**\n   * Generate insights based on comparison results\n   */\n  private generateInsights(\n    investment: any,\n    benchmarks: any[],\n    scenario: InvestmentScenario\n  ): string[] {\n    const insights: string[] = [];\n    const years = calculateYearsBetweenDates(scenario.startDate, scenario.endDate);\n\n    // Performance insights\n    if (investment.rank === 1) {\n      insights.push(`🎉 Your ${scenario.stockSymbol} investment outperformed all benchmarks with a CAGR of ${formatPercentage(investment.cagr)}.`);\n    } else {\n      const betterBenchmarks = benchmarks.filter(b => b.rank < investment.rank);\n      if (betterBenchmarks.length > 0) {\n        const bestBenchmark = betterBenchmarks[0];\n        insights.push(`📊 Your investment ranked #${investment.rank}. ${bestBenchmark.name} performed better with ${formatPercentage(bestBenchmark.cagr)} CAGR.`);\n      }\n    }\n\n    // Risk-adjusted insights\n    const goldBenchmark = benchmarks.find(b => b.type === 'GOLD');\n    const fdBenchmark = benchmarks.find(b => b.type === 'FD');\n    const niftyBenchmark = benchmarks.find(b => b.type === 'NIFTY');\n\n    if (goldBenchmark && investment.cagr > goldBenchmark.cagr) {\n      insights.push(`🥇 Your investment beat gold by ${formatPercentage(investment.cagr - goldBenchmark.cagr)} annually.`);\n    }\n\n    if (fdBenchmark && investment.cagr > fdBenchmark.cagr) {\n      insights.push(`🏦 Your investment outperformed Fixed Deposits by ${formatPercentage(investment.cagr - fdBenchmark.cagr)} annually.`);\n    }\n\n    if (niftyBenchmark) {\n      if (investment.cagr > niftyBenchmark.cagr) {\n        insights.push(`📈 Your stock selection beat the market (Nifty 50) by ${formatPercentage(investment.cagr - niftyBenchmark.cagr)} annually.`);\n      } else {\n        insights.push(`📉 The market (Nifty 50) outperformed your stock by ${formatPercentage(niftyBenchmark.cagr - investment.cagr)} annually.`);\n      }\n    }\n\n    // Time-based insights\n    if (years >= 5) {\n      insights.push(`⏰ Over ${Math.round(years)} years, your ${formatCurrency(scenario.investmentAmount)} investment grew to ${formatCurrency(investment.currentValue)}.`);\n    } else if (years >= 1) {\n      insights.push(`📅 In ${Math.round(years * 12)} months, your investment generated ${formatCurrency(investment.totalReturn)} in returns.`);\n    }\n\n    // Value insights\n    if (investment.absoluteReturn > 100) {\n      insights.push(`💰 Your investment more than doubled your money with ${formatPercentage(investment.absoluteReturn)} total returns.`);\n    } else if (investment.absoluteReturn > 50) {\n      insights.push(`💵 Your investment generated strong returns of ${formatPercentage(investment.absoluteReturn)}.`);\n    } else if (investment.absoluteReturn < 0) {\n      insights.push(`⚠️ Your investment resulted in a loss of ${formatPercentage(Math.abs(investment.absoluteReturn))}.`);\n    }\n\n    return insights;\n  }\n\n  /**\n   * Get display name for benchmark type\n   */\n  private getBenchmarkDisplayName(type: string): string {\n    const names: { [key: string]: string } = {\n      'GOLD': 'Gold',\n      'FD': 'Fixed Deposit',\n      'NIFTY': 'Nifty 50',\n    };\n    return names[type] || type;\n  }\n\n  /**\n   * Generate time series data for visualization\n   */\n  private async generateTimeSeriesData(\n    scenario: InvestmentScenario,\n    comparisonResult: ComparisonResult\n  ): Promise<Array<{\n    date: string;\n    investment: number;\n    gold: number;\n    fd: number;\n    nifty: number;\n  }>> {\n    // This is a simplified implementation\n    // In production, you'd fetch actual historical data for all assets\n    const data: Array<{\n      date: string;\n      investment: number;\n      gold: number;\n      fd: number;\n      nifty: number;\n    }> = [];\n\n    const startDate = new Date(scenario.startDate);\n    const endDate = new Date(scenario.endDate);\n    const currentDate = new Date(startDate);\n\n    // Calculate daily growth rates\n    const years = calculateYearsBetweenDates(startDate, endDate);\n    const days = years * 365;\n\n    const investmentDailyGrowth = Math.pow(comparisonResult.investment.currentValue / comparisonResult.investment.initialValue, 1 / days);\n    const goldDailyGrowth = Math.pow(comparisonResult.benchmarks.GOLD.currentValue / comparisonResult.benchmarks.GOLD.initialValue, 1 / days);\n    const fdDailyGrowth = Math.pow(comparisonResult.benchmarks.FD.currentValue / comparisonResult.benchmarks.FD.initialValue, 1 / days);\n    const niftyDailyGrowth = Math.pow(comparisonResult.benchmarks.NIFTY.currentValue / comparisonResult.benchmarks.NIFTY.initialValue, 1 / days);\n\n    let dayCount = 0;\n    while (currentDate <= endDate) {\n      const investmentValue = scenario.investmentAmount * Math.pow(investmentDailyGrowth, dayCount);\n      const goldValue = scenario.investmentAmount * Math.pow(goldDailyGrowth, dayCount);\n      const fdValue = scenario.investmentAmount * Math.pow(fdDailyGrowth, dayCount);\n      const niftyValue = scenario.investmentAmount * Math.pow(niftyDailyGrowth, dayCount);\n\n      data.push({\n        date: currentDate.toISOString().split('T')[0],\n        investment: Math.round(investmentValue),\n        gold: Math.round(goldValue),\n        fd: Math.round(fdValue),\n        nifty: Math.round(niftyValue),\n      });\n\n      currentDate.setDate(currentDate.getDate() + 7); // Weekly data points\n      dayCount += 7;\n    }\n\n    return data;\n  }\n}\n"], "names": [], "mappings": "AAAA,8DAA8D;;;;AAI9D;;AAkDO,MAAM;IACH,qBAA2C;IAC3C,qBAA2C;IAEnD,YACE,oBAA0C,EAC1C,oBAA0C,CAC1C;QACA,IAAI,CAAC,oBAAoB,GAAG;QAC5B,IAAI,CAAC,oBAAoB,GAAG;IAC9B;IAEA;;GAEC,GACD,MAAM,0BAA0B,QAA4B,EAA8B;QACxF,IAAI;YACF,MAAM,mBAAmB,MAAM,IAAI,CAAC,oBAAoB,CAAC,wBAAwB,CAAC;YAElF,0BAA0B;YAC1B,MAAM,aAAa;gBACjB,MAAM,GAAG,SAAS,WAAW,CAAC,WAAW,CAAC;gBAC1C,cAAc,iBAAiB,UAAU,CAAC,YAAY;gBACtD,cAAc,iBAAiB,UAAU,CAAC,YAAY;gBACtD,aAAa,iBAAiB,UAAU,CAAC,WAAW;gBACpD,MAAM,iBAAiB,UAAU,CAAC,IAAI;gBACtC,gBAAgB,iBAAiB,UAAU,CAAC,cAAc;gBAC1D,MAAM;YACR;YAEA,yBAAyB;YACzB,MAAM,aAAa,OAAO,OAAO,CAAC,iBAAiB,UAAU,EAAE,GAAG,CAAC,CAAC,CAAC,MAAM,KAAK,GAAK,CAAC;oBACpF,MAAM,IAAI,CAAC,uBAAuB,CAAC;oBACnC;oBACA,cAAc,KAAK,YAAY;oBAC/B,cAAc,KAAK,YAAY;oBAC/B,aAAa,KAAK,YAAY,GAAG,KAAK,YAAY;oBAClD,MAAM,KAAK,IAAI;oBACf,gBAAgB,KAAK,cAAc;oBACnC,MAAM;oBACN,gBAAgB,WAAW,IAAI,GAAG,KAAK,IAAI;gBAC7C,CAAC;YAED,mCAAmC;YACnC,MAAM,iBAAiB;gBAAC;mBAAe;aAAW;YAClD,eAAe,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,IAAI,GAAG,EAAE,IAAI;YAC7C,eAAe,OAAO,CAAC,CAAC,MAAM;gBAC5B,KAAK,IAAI,GAAG,QAAQ;YACtB;YAEA,oBAAoB;YACpB,MAAM,WAAW,IAAI,CAAC,gBAAgB,CAAC,YAAY,YAAY;YAE/D,OAAO;gBACL;gBACA;gBACA;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;YACtD,MAAM;QACR;IACF;IAEA;;GAEC,GACD,4BAA4B,iBAAoC,EAAsB;QACpF,MAAM,iBAAiB;YAAC,kBAAkB,UAAU;eAAK,kBAAkB,UAAU;SAAC;QAEtF,iCAAiC;QACjC,MAAM,eAAe;eAAI;SAAe,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,IAAI,GAAG,EAAE,IAAI;QACvE,MAAM,gBAAgB;YACpB,MAAM,YAAY,CAAC,EAAE,CAAC,IAAI;YAC1B,MAAM,YAAY,CAAC,EAAE,CAAC,IAAI;YAC1B,gBAAgB,YAAY,CAAC,EAAE,CAAC,cAAc;QAChD;QACA,MAAM,iBAAiB;YACrB,MAAM,YAAY,CAAC,aAAa,MAAM,GAAG,EAAE,CAAC,IAAI;YAChD,MAAM,YAAY,CAAC,aAAa,MAAM,GAAG,EAAE,CAAC,IAAI;YAChD,gBAAgB,YAAY,CAAC,aAAa,MAAM,GAAG,EAAE,CAAC,cAAc;QACtE;QAEA,yBAAyB;QACzB,MAAM,cAAc,eAAe,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,IAAI,IAAI,EAAE,KAAK,eAAe,MAAM;QAElG,0FAA0F;QAC1F,MAAM,oBAAoB,eAAe,GAAG,CAAC,CAAA,MAAO,CAAC;gBACnD,MAAM,IAAI,IAAI;gBACd,YAAY,KAAK,GAAG,CAAC,IAAI,cAAc,GAAG,IAAI,IAAI;YACpD,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,UAAU,GAAG,EAAE,UAAU;QAE9C,OAAO;YACL;YACA;YACA,aAAa,OAAO,YAAY,OAAO,CAAC;YACxC;QACF;IACF;IAEA;;GAEC,GACD,MAAM,4BAA4B,QAA4B,EAc3D;QACD,IAAI;YACF,MAAM,mBAAmB,MAAM,IAAI,CAAC,oBAAoB,CAAC,wBAAwB,CAAC;YAElF,wEAAwE;YACxE,mFAAmF;YACnF,MAAM,iBAAiB,MAAM,IAAI,CAAC,sBAAsB,CAAC,UAAU;YAEnE,sCAAsC;YACtC,MAAM,eAAe;gBACnB;oBACE,MAAM,SAAS,WAAW;oBAC1B,MAAM,iBAAiB,UAAU,CAAC,IAAI;oBACtC,gBAAgB,iBAAiB,UAAU,CAAC,cAAc;oBAC1D,cAAc,iBAAiB,UAAU,CAAC,YAAY;gBACxD;mBACG,OAAO,OAAO,CAAC,iBAAiB,UAAU,EAAE,GAAG,CAAC,CAAC,CAAC,MAAM,KAAK,GAAK,CAAC;wBACpE,MAAM,IAAI,CAAC,uBAAuB,CAAC;wBACnC,MAAM,KAAK,IAAI;wBACf,gBAAgB,KAAK,cAAc;wBACnC,cAAc,KAAK,YAAY;oBACjC,CAAC;aACF;YAED,OAAO;gBACL;gBACA;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,sBACJ,SAA+B,EAI7B;QACF,MAAM,UAGD,EAAE;QAEP,KAAK,MAAM,YAAY,UAAW;YAChC,IAAI;gBACF,MAAM,UAAU,MAAM,IAAI,CAAC,yBAAyB,CAAC;gBACrD,QAAQ,IAAI,CAAC;oBAAE;oBAAU;gBAAQ;YACnC,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,CAAC,yBAAyB,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC,EAAE;YAC1D,gCAAgC;YAClC;QACF;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,iBACN,UAAe,EACf,UAAiB,EACjB,QAA4B,EAClB;QACV,MAAM,WAAqB,EAAE;QAC7B,MAAM,QAAQ,CAAA,GAAA,8HAAA,CAAA,6BAA0B,AAAD,EAAE,SAAS,SAAS,EAAE,SAAS,OAAO;QAE7E,uBAAuB;QACvB,IAAI,WAAW,IAAI,KAAK,GAAG;YACzB,SAAS,IAAI,CAAC,CAAC,QAAQ,EAAE,SAAS,WAAW,CAAC,uDAAuD,EAAE,CAAA,GAAA,8HAAA,CAAA,mBAAgB,AAAD,EAAE,WAAW,IAAI,EAAE,CAAC,CAAC;QAC7I,OAAO;YACL,MAAM,mBAAmB,WAAW,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,GAAG,WAAW,IAAI;YACxE,IAAI,iBAAiB,MAAM,GAAG,GAAG;gBAC/B,MAAM,gBAAgB,gBAAgB,CAAC,EAAE;gBACzC,SAAS,IAAI,CAAC,CAAC,2BAA2B,EAAE,WAAW,IAAI,CAAC,EAAE,EAAE,cAAc,IAAI,CAAC,uBAAuB,EAAE,CAAA,GAAA,8HAAA,CAAA,mBAAgB,AAAD,EAAE,cAAc,IAAI,EAAE,MAAM,CAAC;YAC1J;QACF;QAEA,yBAAyB;QACzB,MAAM,gBAAgB,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK;QACtD,MAAM,cAAc,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK;QACpD,MAAM,iBAAiB,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK;QAEvD,IAAI,iBAAiB,WAAW,IAAI,GAAG,cAAc,IAAI,EAAE;YACzD,SAAS,IAAI,CAAC,CAAC,gCAAgC,EAAE,CAAA,GAAA,8HAAA,CAAA,mBAAgB,AAAD,EAAE,WAAW,IAAI,GAAG,cAAc,IAAI,EAAE,UAAU,CAAC;QACrH;QAEA,IAAI,eAAe,WAAW,IAAI,GAAG,YAAY,IAAI,EAAE;YACrD,SAAS,IAAI,CAAC,CAAC,kDAAkD,EAAE,CAAA,GAAA,8HAAA,CAAA,mBAAgB,AAAD,EAAE,WAAW,IAAI,GAAG,YAAY,IAAI,EAAE,UAAU,CAAC;QACrI;QAEA,IAAI,gBAAgB;YAClB,IAAI,WAAW,IAAI,GAAG,eAAe,IAAI,EAAE;gBACzC,SAAS,IAAI,CAAC,CAAC,sDAAsD,EAAE,CAAA,GAAA,8HAAA,CAAA,mBAAgB,AAAD,EAAE,WAAW,IAAI,GAAG,eAAe,IAAI,EAAE,UAAU,CAAC;YAC5I,OAAO;gBACL,SAAS,IAAI,CAAC,CAAC,oDAAoD,EAAE,CAAA,GAAA,8HAAA,CAAA,mBAAgB,AAAD,EAAE,eAAe,IAAI,GAAG,WAAW,IAAI,EAAE,UAAU,CAAC;YAC1I;QACF;QAEA,sBAAsB;QACtB,IAAI,SAAS,GAAG;YACd,SAAS,IAAI,CAAC,CAAC,OAAO,EAAE,KAAK,KAAK,CAAC,OAAO,aAAa,EAAE,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD,EAAE,SAAS,gBAAgB,EAAE,oBAAoB,EAAE,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD,EAAE,WAAW,YAAY,EAAE,CAAC,CAAC;QACrK,OAAO,IAAI,SAAS,GAAG;YACrB,SAAS,IAAI,CAAC,CAAC,MAAM,EAAE,KAAK,KAAK,CAAC,QAAQ,IAAI,mCAAmC,EAAE,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD,EAAE,WAAW,WAAW,EAAE,YAAY,CAAC;QACzI;QAEA,iBAAiB;QACjB,IAAI,WAAW,cAAc,GAAG,KAAK;YACnC,SAAS,IAAI,CAAC,CAAC,qDAAqD,EAAE,CAAA,GAAA,8HAAA,CAAA,mBAAgB,AAAD,EAAE,WAAW,cAAc,EAAE,eAAe,CAAC;QACpI,OAAO,IAAI,WAAW,cAAc,GAAG,IAAI;YACzC,SAAS,IAAI,CAAC,CAAC,+CAA+C,EAAE,CAAA,GAAA,8HAAA,CAAA,mBAAgB,AAAD,EAAE,WAAW,cAAc,EAAE,CAAC,CAAC;QAChH,OAAO,IAAI,WAAW,cAAc,GAAG,GAAG;YACxC,SAAS,IAAI,CAAC,CAAC,yCAAyC,EAAE,CAAA,GAAA,8HAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,GAAG,CAAC,WAAW,cAAc,GAAG,CAAC,CAAC;QACpH;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,wBAAwB,IAAY,EAAU;QACpD,MAAM,QAAmC;YACvC,QAAQ;YACR,MAAM;YACN,SAAS;QACX;QACA,OAAO,KAAK,CAAC,KAAK,IAAI;IACxB;IAEA;;GAEC,GACD,MAAc,uBACZ,QAA4B,EAC5B,gBAAkC,EAOhC;QACF,sCAAsC;QACtC,mEAAmE;QACnE,MAAM,OAMD,EAAE;QAEP,MAAM,YAAY,IAAI,KAAK,SAAS,SAAS;QAC7C,MAAM,UAAU,IAAI,KAAK,SAAS,OAAO;QACzC,MAAM,cAAc,IAAI,KAAK;QAE7B,+BAA+B;QAC/B,MAAM,QAAQ,CAAA,GAAA,8HAAA,CAAA,6BAA0B,AAAD,EAAE,WAAW;QACpD,MAAM,OAAO,QAAQ;QAErB,MAAM,wBAAwB,KAAK,GAAG,CAAC,iBAAiB,UAAU,CAAC,YAAY,GAAG,iBAAiB,UAAU,CAAC,YAAY,EAAE,IAAI;QAChI,MAAM,kBAAkB,KAAK,GAAG,CAAC,iBAAiB,UAAU,CAAC,IAAI,CAAC,YAAY,GAAG,iBAAiB,UAAU,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI;QACpI,MAAM,gBAAgB,KAAK,GAAG,CAAC,iBAAiB,UAAU,CAAC,EAAE,CAAC,YAAY,GAAG,iBAAiB,UAAU,CAAC,EAAE,CAAC,YAAY,EAAE,IAAI;QAC9H,MAAM,mBAAmB,KAAK,GAAG,CAAC,iBAAiB,UAAU,CAAC,KAAK,CAAC,YAAY,GAAG,iBAAiB,UAAU,CAAC,KAAK,CAAC,YAAY,EAAE,IAAI;QAEvI,IAAI,WAAW;QACf,MAAO,eAAe,QAAS;YAC7B,MAAM,kBAAkB,SAAS,gBAAgB,GAAG,KAAK,GAAG,CAAC,uBAAuB;YACpF,MAAM,YAAY,SAAS,gBAAgB,GAAG,KAAK,GAAG,CAAC,iBAAiB;YACxE,MAAM,UAAU,SAAS,gBAAgB,GAAG,KAAK,GAAG,CAAC,eAAe;YACpE,MAAM,aAAa,SAAS,gBAAgB,GAAG,KAAK,GAAG,CAAC,kBAAkB;YAE1E,KAAK,IAAI,CAAC;gBACR,MAAM,YAAY,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;gBAC7C,YAAY,KAAK,KAAK,CAAC;gBACvB,MAAM,KAAK,KAAK,CAAC;gBACjB,IAAI,KAAK,KAAK,CAAC;gBACf,OAAO,KAAK,KAAK,CAAC;YACpB;YAEA,YAAY,OAAO,CAAC,YAAY,OAAO,KAAK,IAAI,qBAAqB;YACrE,YAAY;QACd;QAEA,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 1663, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/whatif/whatif/src/app/api/analyze/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { AngelOneClient } from '@/lib/api/angelone';\nimport { StockDataService } from '@/lib/services/stockData';\nimport { BenchmarkDataService } from '@/lib/services/benchmarkData';\nimport { InvestmentCalculator } from '@/lib/services/investmentCalculator';\nimport { ComparisonService } from '@/lib/services/comparisonService';\nimport { InvestmentScenario } from '@/lib/types';\nimport { generateId } from '@/lib/utils';\n\n// Initialize services\nlet angelOneClient: AngelOneClient;\nlet stockDataService: StockDataService;\nlet benchmarkDataService: BenchmarkDataService;\nlet investmentCalculator: InvestmentCalculator;\nlet comparisonService: ComparisonService;\n\nasync function initializeServices() {\n  if (!angelOneClient) {\n    // Initialize Angel One client with environment variables\n    angelOneClient = new AngelOneClient({\n      apiKey: process.env.ANGEL_ONE_API_KEY!,\n      clientId: process.env.ANGEL_ONE_CLIENT_ID!,\n      password: process.env.ANGEL_ONE_PASSWORD!,\n      totpSecret: process.env.ANGEL_ONE_TOTP_SECRET!,\n    });\n\n    // Authenticate\n    await angelOneClient.login();\n\n    // Initialize services\n    stockDataService = new StockDataService(angelOneClient);\n    benchmarkDataService = new BenchmarkDataService(angelOneClient);\n    investmentCalculator = new InvestmentCalculator(stockDataService, benchmarkDataService);\n    comparisonService = new ComparisonService(investmentCalculator, benchmarkDataService);\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    console.log('🚀 Starting investment analysis...');\n\n    // Initialize services if not already done\n    console.log('🔧 Initializing services...');\n    await initializeServices();\n    console.log('✅ Services initialized successfully');\n\n    // Parse request body\n    console.log('📥 Parsing request body...');\n    const body = await request.json();\n    const { stockSymbol, investmentAmount, startDate, endDate } = body;\n\n    console.log('📊 Request data:', {\n      stockSymbol,\n      investmentAmount,\n      startDate,\n      endDate\n    });\n\n    // Validate input\n    if (!stockSymbol || !investmentAmount || !startDate || !endDate) {\n      console.log('❌ Validation failed: Missing required fields');\n      return NextResponse.json(\n        { error: 'Missing required fields' },\n        { status: 400 }\n      );\n    }\n\n    // Create investment scenario\n    const scenario: InvestmentScenario = {\n      id: generateId(),\n      stockSymbol,\n      investmentAmount: parseFloat(investmentAmount),\n      startDate: new Date(startDate),\n      endDate: new Date(endDate),\n      createdAt: new Date(),\n    };\n\n    // Validate date range\n    if (scenario.startDate >= scenario.endDate) {\n      return NextResponse.json(\n        { error: 'Start date must be before end date' },\n        { status: 400 }\n      );\n    }\n\n    if (scenario.startDate > new Date()) {\n      return NextResponse.json(\n        { error: 'Start date cannot be in the future' },\n        { status: 400 }\n      );\n    }\n\n    // Calculate investment result\n    const investmentResult = await investmentCalculator.calculateInvestmentResult(scenario);\n\n    // Calculate comparison with benchmarks\n    const comparisonResult = await investmentCalculator.calculateWithComparisons(scenario);\n\n    // Generate comparison summary\n    const comparisonSummary = await comparisonService.generateComparisonSummary(scenario);\n\n    // Generate chart data\n    const chartData = await comparisonService.generateComparisonChartData(scenario);\n\n    // Return results\n    return NextResponse.json({\n      investmentResult,\n      comparisonResult,\n      comparisonSummary,\n      chartData,\n    });\n\n  } catch (error) {\n    console.error('💥 Analysis API error:', error);\n    console.error('Error type:', error?.constructor?.name);\n    console.error('Error message:', error?.message);\n    console.error('Error stack:', error?.stack);\n\n    // Handle specific error types\n    if (error instanceof Error) {\n      console.log('🔍 Checking error type:', error.message);\n\n      if (error.message.includes('Stock not found') || error.message.includes('Invalid stock symbol')) {\n        console.log('❌ Stock not found error');\n        return NextResponse.json(\n          { error: 'Stock symbol not found. Please check the symbol and try again.' },\n          { status: 404 }\n        );\n      }\n\n      if (error.message.includes('Rate limit')) {\n        console.log('❌ Rate limit error');\n        return NextResponse.json(\n          { error: 'Too many requests. Please wait a moment and try again.' },\n          { status: 429 }\n        );\n      }\n\n      if (error.message.includes('Authentication')) {\n        console.log('❌ Authentication error');\n        return NextResponse.json(\n          { error: 'Authentication failed. Please try again later.' },\n          { status: 401 }\n        );\n      }\n    }\n\n    console.log('❌ Generic error, returning 500');\n    return NextResponse.json(\n      { error: 'An error occurred while analyzing the investment. Please try again.', details: error?.message },\n      { status: 500 }\n    );\n  }\n}\n\n// Health check endpoint\nexport async function GET() {\n  try {\n    await initializeServices();\n    return NextResponse.json({ status: 'OK', message: 'Analysis API is running' });\n  } catch (error) {\n    console.error('Health check failed:', error);\n    return NextResponse.json(\n      { status: 'ERROR', message: 'Service unavailable' },\n      { status: 503 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AAEA;;;;;;;;AAEA,sBAAsB;AACtB,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,eAAe;IACb,IAAI,CAAC,gBAAgB;QACnB,yDAAyD;QACzD,iBAAiB,IAAI,+HAAA,CAAA,iBAAc,CAAC;YAClC,QAAQ,QAAQ,GAAG,CAAC,iBAAiB;YACrC,UAAU,QAAQ,GAAG,CAAC,mBAAmB;YACzC,UAAU,QAAQ,GAAG,CAAC,kBAAkB;YACxC,YAAY,QAAQ,GAAG,CAAC,qBAAqB;QAC/C;QAEA,eAAe;QACf,MAAM,eAAe,KAAK;QAE1B,sBAAsB;QACtB,mBAAmB,IAAI,qIAAA,CAAA,mBAAgB,CAAC;QACxC,uBAAuB,IAAI,yIAAA,CAAA,uBAAoB,CAAC;QAChD,uBAAuB,IAAI,gJAAA,CAAA,uBAAoB,CAAC,kBAAkB;QAClE,oBAAoB,IAAI,6IAAA,CAAA,oBAAiB,CAAC,sBAAsB;IAClE;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,0CAA0C;QAC1C,QAAQ,GAAG,CAAC;QACZ,MAAM;QACN,QAAQ,GAAG,CAAC;QAEZ,qBAAqB;QACrB,QAAQ,GAAG,CAAC;QACZ,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,WAAW,EAAE,gBAAgB,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG;QAE9D,QAAQ,GAAG,CAAC,oBAAoB;YAC9B;YACA;YACA;YACA;QACF;QAEA,iBAAiB;QACjB,IAAI,CAAC,eAAe,CAAC,oBAAoB,CAAC,aAAa,CAAC,SAAS;YAC/D,QAAQ,GAAG,CAAC;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA0B,GACnC;gBAAE,QAAQ;YAAI;QAElB;QAEA,6BAA6B;QAC7B,MAAM,WAA+B;YACnC,IAAI,CAAA,GAAA,8HAAA,CAAA,aAAU,AAAD;YACb;YACA,kBAAkB,WAAW;YAC7B,WAAW,IAAI,KAAK;YACpB,SAAS,IAAI,KAAK;YAClB,WAAW,IAAI;QACjB;QAEA,sBAAsB;QACtB,IAAI,SAAS,SAAS,IAAI,SAAS,OAAO,EAAE;YAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAqC,GAC9C;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI,SAAS,SAAS,GAAG,IAAI,QAAQ;YACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAqC,GAC9C;gBAAE,QAAQ;YAAI;QAElB;QAEA,8BAA8B;QAC9B,MAAM,mBAAmB,MAAM,qBAAqB,yBAAyB,CAAC;QAE9E,uCAAuC;QACvC,MAAM,mBAAmB,MAAM,qBAAqB,wBAAwB,CAAC;QAE7E,8BAA8B;QAC9B,MAAM,oBAAoB,MAAM,kBAAkB,yBAAyB,CAAC;QAE5E,sBAAsB;QACtB,MAAM,YAAY,MAAM,kBAAkB,2BAA2B,CAAC;QAEtE,iBAAiB;QACjB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB;YACA;YACA;YACA;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,QAAQ,KAAK,CAAC,eAAe,OAAO,aAAa;QACjD,QAAQ,KAAK,CAAC,kBAAkB,OAAO;QACvC,QAAQ,KAAK,CAAC,gBAAgB,OAAO;QAErC,8BAA8B;QAC9B,IAAI,iBAAiB,OAAO;YAC1B,QAAQ,GAAG,CAAC,2BAA2B,MAAM,OAAO;YAEpD,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,sBAAsB,MAAM,OAAO,CAAC,QAAQ,CAAC,yBAAyB;gBAC/F,QAAQ,GAAG,CAAC;gBACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAAiE,GAC1E;oBAAE,QAAQ;gBAAI;YAElB;YAEA,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,eAAe;gBACxC,QAAQ,GAAG,CAAC;gBACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAAyD,GAClE;oBAAE,QAAQ;gBAAI;YAElB;YAEA,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,mBAAmB;gBAC5C,QAAQ,GAAG,CAAC;gBACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAAiD,GAC1D;oBAAE,QAAQ;gBAAI;YAElB;QACF;QAEA,QAAQ,GAAG,CAAC;QACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;YAAuE,SAAS,OAAO;QAAQ,GACxG;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe;IACpB,IAAI;QACF,MAAM;QACN,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,QAAQ;YAAM,SAAS;QAA0B;IAC9E,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,QAAQ;YAAS,SAAS;QAAsB,GAClD;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}