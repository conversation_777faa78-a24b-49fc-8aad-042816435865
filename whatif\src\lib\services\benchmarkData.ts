// Benchmark data service for the What If investment analysis tool
// Now uses real market indices instead of simulated FD/Gold data

import { AngelOneClient } from '../api/angelone';
import { BENCHMARK_CONFIG } from '../config';
import { calculateCAGR, calculateAbsoluteReturn, calculateYearsBetweenDates } from '../utils';
import { resolveIndexSymbol, getSupportedIndices } from '../utils/symbolMapping';
import { BenchmarkData, AngelOneHistoricalRequest, HistoricalPrice } from '../types';
import { format } from 'date-fns';

export class BenchmarkDataService {
  private angelOneClient: AngelOneClient;

  constructor(angelOneClient: AngelOneClient) {
    this.angelOneClient = angelOneClient;
  }

  /**
   * Get real market index data (replaces simulated FD/Gold)
   */
  async getIndexData(indexSymbol: string, startDate: Date, endDate: Date): Promise<BenchmarkData> {
    try {
      const indexMapping = resolveIndexSymbol(indexSymbol);

      const request: AngelOneHistoricalRequest = {
        exchange: indexMapping.exchange,
        symboltoken: indexMapping.token,
        interval: 'ONE_DAY',
        fromdate: format(startDate, 'yyyy-MM-dd HH:mm'),
        todate: format(endDate, 'yyyy-MM-dd HH:mm'),
      };

      console.log(`📊 Fetching ${indexMapping.name} data from ${format(startDate, 'yyyy-MM-dd')} to ${format(endDate, 'yyyy-MM-dd')}`);

      const historicalData = await this.angelOneClient.getHistoricalData(request);

      if (historicalData.length === 0) {
        throw new Error(`No historical data available for ${indexMapping.name}`);
      }

      const startPrice = historicalData[0].close;
      const endPrice = historicalData[historicalData.length - 1].close;
      const years = calculateYearsBetweenDates(startDate, endDate);

      return {
        type: indexSymbol as any,
        name: indexMapping.name,
        startDate,
        endDate,
        startValue: startPrice,
        endValue: endPrice,
        cagr: calculateCAGR(startPrice, endPrice, years),
        absoluteReturn: calculateAbsoluteReturn(startPrice, endPrice),
        historicalData: historicalData.map(item => ({
          date: item.date,
          value: item.close,
        })),
      };
    } catch (error) {
      console.error(`Error fetching ${indexSymbol} data:`, error);
      throw new Error(`Failed to fetch ${indexSymbol} data: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get Nifty 50 historical data (updated to use new structure)
   */
  async getNiftyData(startDate: Date, endDate: Date): Promise<BenchmarkData> {
    return this.getIndexData('NIFTY', startDate, endDate);
  }

  /**
   * Get Bank Nifty historical data (replaces Gold)
   */
  async getBankNiftyData(startDate: Date, endDate: Date): Promise<BenchmarkData> {
    return this.getIndexData('BANKNIFTY', startDate, endDate);
  }

  /**
   * Get Nifty IT historical data (replaces Fixed Deposit)
   */
  async getNiftyITData(startDate: Date, endDate: Date): Promise<BenchmarkData> {
    return this.getIndexData('NIFTYIT', startDate, endDate);
  }

  /**
   * Get BSE Sensex historical data (additional benchmark)
   */
  async getSensexData(startDate: Date, endDate: Date): Promise<BenchmarkData> {
    return this.getIndexData('SENSEX', startDate, endDate);
  }

  /**
   * Get all benchmark data for comparison (now uses real market indices)
   */
  async getAllBenchmarks(startDate: Date, endDate: Date): Promise<{
    nifty: BenchmarkData;
    bankNifty: BenchmarkData;
    niftyIT: BenchmarkData;
  }> {
    try {
      console.log('📊 Fetching all benchmark data with real market indices...');

      const [nifty, bankNifty, niftyIT] = await Promise.all([
        this.getNiftyData(startDate, endDate),
        this.getBankNiftyData(startDate, endDate),
        this.getNiftyITData(startDate, endDate),
      ]);

      return { nifty, bankNifty, niftyIT };
    } catch (error) {
      console.error('Error fetching benchmark data:', error);
      throw new Error(`Failed to fetch benchmark data: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Calculate benchmark returns for a given investment amount and period
   * Now supports real market indices instead of simulated data
   */
  async calculateBenchmarkReturns(
    benchmarkType: 'NIFTY' | 'BANKNIFTY' | 'NIFTYIT' | 'SENSEX',
    investmentAmount: number,
    startDate: Date,
    endDate: Date
  ): Promise<{
    initialValue: number;
    currentValue: number;
    cagr: number;
    absoluteReturn: number;
  }> {
    let benchmarkData: BenchmarkData;

    switch (benchmarkType) {
      case 'NIFTY':
        benchmarkData = await this.getNiftyData(startDate, endDate);
        break;
      case 'BANKNIFTY':
        benchmarkData = await this.getBankNiftyData(startDate, endDate);
        break;
      case 'NIFTYIT':
        benchmarkData = await this.getNiftyITData(startDate, endDate);
        break;
      case 'SENSEX':
        benchmarkData = await this.getSensexData(startDate, endDate);
        break;
      default:
        throw new Error(`Unsupported benchmark type: ${benchmarkType}`);
    }

    if (benchmarkData.historicalData.length === 0) {
      throw new Error(`No data available for ${benchmarkType} in the specified period`);
    }

    const startValue = benchmarkData.startValue;
    const endValue = benchmarkData.endValue;

    // Calculate how much of the benchmark could be bought with the investment amount
    const units = investmentAmount / startValue;
    const currentValue = units * endValue;

    return {
      initialValue: investmentAmount,
      currentValue,
      cagr: benchmarkData.cagr,
      absoluteReturn: benchmarkData.absoluteReturn,
    };
  }

}
