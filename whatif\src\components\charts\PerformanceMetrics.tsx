'use client';

import React from 'react';
import { formatCurrency, formatPercentage } from '@/lib/utils';

export interface PerformanceMetricsData {
  bestPerformer: {
    name: string;
    cagr: number;
    absoluteReturn: number;
  };
  worstPerformer: {
    name: string;
    cagr: number;
    absoluteReturn: number;
  };
  averageCagr: number;
  volatilityRanking: Array<{
    name: string;
    volatility: number;
  }>;
}

interface PerformanceMetricsProps {
  data: PerformanceMetricsData;
  investmentAmount: number;
  investmentPeriod: string;
  title?: string;
}

const PerformanceMetrics: React.FC<PerformanceMetricsProps> = ({
  data,
  investmentAmount,
  investmentPeriod,
  title = 'Performance Metrics',
}) => {
  const MetricCard: React.FC<{
    title: string;
    value: string;
    subtitle?: string;
    color: 'green' | 'red' | 'blue' | 'purple' | 'yellow';
    icon?: React.ReactNode;
  }> = ({ title, value, subtitle, color, icon }) => {
    const colorClasses = {
      green: 'bg-green-50 border-green-200 text-green-800',
      red: 'bg-red-50 border-red-200 text-red-800',
      blue: 'bg-blue-50 border-blue-200 text-blue-800',
      purple: 'bg-purple-50 border-purple-200 text-purple-800',
      yellow: 'bg-yellow-50 border-yellow-200 text-yellow-800',
    };

    return (
      <div className={`p-4 rounded-lg border ${colorClasses[color]}`}>
        <div className="flex items-center justify-between">
          <div>
            <h4 className="text-sm font-medium opacity-75">{title}</h4>
            <p className="text-xl font-bold mt-1">{value}</p>
            {subtitle && (
              <p className="text-sm opacity-75 mt-1">{subtitle}</p>
            )}
          </div>
          {icon && (
            <div className="text-2xl opacity-50">
              {icon}
            </div>
          )}
        </div>
      </div>
    );
  };

  const RankingList: React.FC<{
    title: string;
    items: Array<{ name: string; volatility: number }>;
  }> = ({ title, items }) => (
    <div className="bg-white p-4 rounded-lg border border-gray-200">
      <h4 className="text-md font-semibold text-gray-800 mb-3">{title}</h4>
      <div className="space-y-2">
        {items.map((item, index) => (
          <div key={item.name} className="flex justify-between items-center">
            <div className="flex items-center">
              <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-gray-100 text-xs font-medium text-gray-600 mr-3">
                {index + 1}
              </span>
              <span className="text-sm font-medium">{item.name}</span>
            </div>
            <span className="text-sm text-gray-600">
              {formatPercentage(item.volatility)}
            </span>
          </div>
        ))}
      </div>
    </div>
  );

  return (
    <div className="w-full space-y-6">
      {title && (
        <h3 className="text-lg font-semibold text-gray-800 text-center">
          {title}
        </h3>
      )}

      {/* Investment Summary */}
      <div className="bg-gradient-to-r from-blue-50 to-purple-50 p-4 rounded-lg border border-blue-200">
        <h4 className="text-md font-semibold text-gray-800 mb-2">Investment Summary</h4>
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-600">Investment Amount:</span>
            <span className="font-semibold ml-2">{formatCurrency(investmentAmount)}</span>
          </div>
          <div>
            <span className="text-gray-600">Investment Period:</span>
            <span className="font-semibold ml-2">{investmentPeriod}</span>
          </div>
        </div>
      </div>

      {/* Key Performance Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <MetricCard
          title="Best Performer"
          value={data.bestPerformer.name}
          subtitle={`${formatPercentage(data.bestPerformer.cagr)} CAGR`}
          color="green"
          icon="🏆"
        />
        
        <MetricCard
          title="Worst Performer"
          value={data.worstPerformer.name}
          subtitle={`${formatPercentage(data.worstPerformer.cagr)} CAGR`}
          color="red"
          icon="📉"
        />
        
        <MetricCard
          title="Average CAGR"
          value={formatPercentage(data.averageCagr)}
          subtitle="Across all options"
          color="blue"
          icon="📊"
        />
        
        <MetricCard
          title="Most Stable"
          value={data.volatilityRanking[0]?.name || 'N/A'}
          subtitle="Lowest volatility"
          color="purple"
          icon="🛡️"
        />
      </div>

      {/* Detailed Performance Comparison */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Best vs Worst Comparison */}
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <h4 className="text-md font-semibold text-gray-800 mb-3">
            Best vs Worst Performance
          </h4>
          
          <div className="space-y-4">
            <div className="flex justify-between items-center p-3 bg-green-50 rounded">
              <div>
                <div className="font-semibold text-green-800">
                  🏆 {data.bestPerformer.name}
                </div>
                <div className="text-sm text-green-600">
                  CAGR: {formatPercentage(data.bestPerformer.cagr)}
                </div>
              </div>
              <div className="text-right text-green-700">
                <div className="font-bold">
                  {formatPercentage(data.bestPerformer.absoluteReturn)}
                </div>
                <div className="text-xs">Total Return</div>
              </div>
            </div>
            
            <div className="flex justify-between items-center p-3 bg-red-50 rounded">
              <div>
                <div className="font-semibold text-red-800">
                  📉 {data.worstPerformer.name}
                </div>
                <div className="text-sm text-red-600">
                  CAGR: {formatPercentage(data.worstPerformer.cagr)}
                </div>
              </div>
              <div className="text-right text-red-700">
                <div className="font-bold">
                  {formatPercentage(data.worstPerformer.absoluteReturn)}
                </div>
                <div className="text-xs">Total Return</div>
              </div>
            </div>
            
            <div className="p-3 bg-blue-50 rounded">
              <div className="text-center text-blue-800">
                <div className="font-semibold">Performance Gap</div>
                <div className="text-lg font-bold">
                  {formatPercentage(data.bestPerformer.cagr - data.worstPerformer.cagr)}
                </div>
                <div className="text-xs">CAGR Difference</div>
              </div>
            </div>
          </div>
        </div>

        {/* Volatility Ranking */}
        <RankingList
          title="Stability Ranking (Low to High Volatility)"
          items={data.volatilityRanking}
        />
      </div>

      {/* Investment Insights */}
      <div className="bg-gradient-to-r from-yellow-50 to-orange-50 p-4 rounded-lg border border-yellow-200">
        <h4 className="text-md font-semibold text-gray-800 mb-3 flex items-center">
          💡 Key Insights
        </h4>
        <div className="space-y-2 text-sm text-gray-700">
          <p>
            • The best performing option ({data.bestPerformer.name}) delivered{' '}
            <span className="font-semibold">{formatPercentage(data.bestPerformer.cagr)}</span> CAGR
          </p>
          <p>
            • Average performance across all options was{' '}
            <span className="font-semibold">{formatPercentage(data.averageCagr)}</span> CAGR
          </p>
          <p>
            • Most stable investment was{' '}
            <span className="font-semibold">{data.volatilityRanking[0]?.name}</span> with lowest volatility
          </p>
          <p>
            • Performance gap between best and worst was{' '}
            <span className="font-semibold">
              {formatPercentage(data.bestPerformer.cagr - data.worstPerformer.cagr)}
            </span>
          </p>
        </div>
      </div>
    </div>
  );
};

export default PerformanceMetrics;
