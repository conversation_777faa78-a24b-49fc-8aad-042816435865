@import "tailwindcss";

/* Killer UI - Modern Gradients & Glassmorphism */
:root {
  --background: #ffffff;
  --foreground: #171717;

  /* Modern Color Palette */
  --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --gradient-warning: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  --gradient-dark: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);

  /* Glassmorphism */
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  background-attachment: fixed;
  min-height: 100vh;
  color: var(--foreground);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* Dark mode gradients */
@media (prefers-color-scheme: dark) {
  body {
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
    background-attachment: fixed;
  }
}

/* Glassmorphism Card */
.glass-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

@media (prefers-color-scheme: dark) {
  .glass-card {
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
}

/* Gradient Button */
.btn-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  color: white;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px 0 rgba(102, 126, 234, 0.4);
}

.btn-gradient:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px 0 rgba(102, 126, 234, 0.6);
}

/* Success Gradient */
.btn-success {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  box-shadow: 0 4px 15px 0 rgba(79, 172, 254, 0.4);
}

.btn-success:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px 0 rgba(79, 172, 254, 0.6);
}

/* Modern Input Styling */
.modern-input {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  color: white;
  transition: all 0.3s ease;
}

.modern-input:focus {
  border-color: rgba(102, 126, 234, 0.8);
  box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
  outline: none;
}

.modern-input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

@media (prefers-color-scheme: dark) {
  .modern-input {
    background: rgba(0, 0, 0, 0.2);
    color: white;
  }
}

/* Floating Animation */
.float-animation {
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

/* Pulse Animation */
.pulse-glow {
  animation: pulseGlow 2s ease-in-out infinite alternate;
}

@keyframes pulseGlow {
  from { box-shadow: 0 0 20px rgba(102, 126, 234, 0.4); }
  to { box-shadow: 0 0 30px rgba(102, 126, 234, 0.8); }
}

/* Animated Background */
.animated-bg {
  background: linear-gradient(-45deg, #667eea, #764ba2, #f093fb, #f5576c);
  background-size: 400% 400%;
  animation: gradientShift 15s ease infinite;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}
