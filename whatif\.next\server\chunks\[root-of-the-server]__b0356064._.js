module.exports = {

"[project]/.next-internal/server/app/api/test-auth/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/path [external] (path, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/fs [external] (fs, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/assert [external] (assert, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}}),
"[externals]/tty [external] (tty, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tty", () => require("tty"));

module.exports = mod;
}}),
"[externals]/os [external] (os, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("os", () => require("os"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[project]/src/lib/config/index.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Configuration for the What If investment analysis tool
__turbopack_context__.s({
    "API_CONFIG": (()=>API_CONFIG),
    "APP_CONFIG": (()=>APP_CONFIG),
    "BENCHMARK_CONFIG": (()=>BENCHMARK_CONFIG),
    "CALCULATION_CONFIG": (()=>CALCULATION_CONFIG),
    "MARKET_CONFIG": (()=>MARKET_CONFIG),
    "STORAGE_CONFIG": (()=>STORAGE_CONFIG),
    "UI_CONFIG": (()=>UI_CONFIG),
    "getEnvironmentConfig": (()=>getEnvironmentConfig)
});
const APP_CONFIG = {
    name: 'What If',
    description: 'Investment Analysis Tool for Indian Equities',
    version: '1.0.0',
    author: 'What If Team'
};
const API_CONFIG = {
    angelOne: {
        baseUrl: 'https://apiconnect.angelone.in',
        version: 'v1',
        endpoints: {
            login: '/rest/auth/angelbroking/user/v1/loginByPassword',
            profile: '/rest/secure/angelbroking/user/v1/getProfile',
            historicalData: '/rest/secure/angelbroking/historical/v1/getCandleData',
            ltp: '/rest/secure/angelbroking/order/v1/getLtpData',
            logout: '/rest/secure/angelbroking/user/v1/logout'
        }
    },
    rateLimit: {
        requestsPerSecond: 10,
        requestsPerMinute: 100
    }
};
const MARKET_CONFIG = {
    exchanges: [
        'NSE',
        'BSE'
    ],
    segments: [
        'EQUITY'
    ],
    tradingHours: {
        start: '09:15',
        end: '15:30',
        timezone: 'Asia/Kolkata'
    },
    holidays: []
};
const BENCHMARK_CONFIG = {
    types: {
        GOLD: {
            name: 'Gold',
            symbol: 'GOLD',
            description: 'Gold prices in INR per 10 grams'
        },
        FD: {
            name: 'Fixed Deposit',
            symbol: 'FD',
            description: 'Average FD rates from major banks',
            defaultRate: 6.5
        },
        NIFTY: {
            name: 'Nifty 50',
            symbol: 'NIFTY',
            description: 'NSE Nifty 50 Index',
            token: '********'
        }
    }
};
const CALCULATION_CONFIG = {
    precision: {
        currency: 2,
        percentage: 2,
        cagr: 2
    },
    defaults: {
        fdRate: 6.5,
        inflationRate: 6.0
    }
};
const UI_CONFIG = {
    theme: {
        primary: '#1f2937',
        secondary: '#374151',
        accent: '#3b82f6',
        success: '#10b981',
        warning: '#f59e0b',
        error: '#ef4444'
    },
    charts: {
        defaultHeight: 400,
        colors: {
            investment: '#3b82f6',
            gold: '#f59e0b',
            fd: '#10b981',
            nifty: '#8b5cf6'
        }
    }
};
const STORAGE_CONFIG = {
    keys: {
        userPreferences: 'whatif_user_preferences',
        savedScenarios: 'whatif_saved_scenarios',
        apiCredentials: 'whatif_api_credentials'
    },
    encryption: {
        enabled: true,
        algorithm: 'AES-256-GCM'
    }
};
const getEnvironmentConfig = ()=>{
    const isDevelopment = ("TURBOPACK compile-time value", "development") === 'development';
    const isProduction = ("TURBOPACK compile-time value", "development") === 'production';
    return {
        isDevelopment,
        isProduction,
        apiUrl: process.env.NEXT_PUBLIC_API_URL || API_CONFIG.angelOne.baseUrl,
        enableLogging: isDevelopment,
        enableAnalytics: isProduction
    };
};
}}),
"[project]/src/lib/security/index.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Security utilities for the What If investment analysis tool
__turbopack_context__.s({
    "RateLimiter": (()=>RateLimiter),
    "sanitizeForLogging": (()=>sanitizeForLogging),
    "simpleDecrypt": (()=>simpleDecrypt),
    "simpleEncrypt": (()=>simpleEncrypt),
    "validateApiKey": (()=>validateApiKey),
    "validateEnvironment": (()=>validateEnvironment)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/config/index.ts [app-route] (ecmascript)");
;
function validateEnvironment() {
    const requiredVars = [
        'NEXT_PUBLIC_ANGEL_ONE_API_URL',
        'NEXT_PUBLIC_APP_NAME',
        'NEXT_PUBLIC_APP_VERSION'
    ];
    const optionalVars = [
        'ANGEL_ONE_API_KEY',
        'ANGEL_ONE_CLIENT_ID',
        'ANGEL_ONE_PASSWORD',
        'ANGEL_ONE_TOTP_SECRET'
    ];
    const missingVars = [];
    const warnings = [];
    // Check required variables
    requiredVars.forEach((varName)=>{
        if (!process.env[varName]) {
            missingVars.push(varName);
        }
    });
    // Check optional but important variables
    optionalVars.forEach((varName)=>{
        if (!process.env[varName]) {
            warnings.push(`${varName} is not set - API functionality will be limited`);
        }
    });
    // Check for development secrets in production
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    return {
        isValid: missingVars.length === 0,
        missingVars,
        warnings
    };
}
function sanitizeForLogging(data) {
    const sensitiveKeys = [
        'password',
        'token',
        'secret',
        'key',
        'auth',
        'credential',
        'totp'
    ];
    const sanitized = {};
    Object.keys(data).forEach((key)=>{
        const lowerKey = key.toLowerCase();
        const isSensitive = sensitiveKeys.some((sensitiveKey)=>lowerKey.includes(sensitiveKey));
        if (isSensitive) {
            sanitized[key] = '[REDACTED]';
        } else if (typeof data[key] === 'object' && data[key] !== null) {
            sanitized[key] = sanitizeForLogging(data[key]);
        } else {
            sanitized[key] = data[key];
        }
    });
    return sanitized;
}
function simpleEncrypt(text) {
    if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["STORAGE_CONFIG"].encryption.enabled) {
        return text;
    }
    // Simple base64 encoding with character shifting (not secure, just obfuscation)
    const shifted = text.split('').map((char)=>String.fromCharCode(char.charCodeAt(0) + 3)).join('');
    return btoa(shifted);
}
function simpleDecrypt(encryptedText) {
    if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["STORAGE_CONFIG"].encryption.enabled) {
        return encryptedText;
    }
    try {
        const decoded = atob(encryptedText);
        return decoded.split('').map((char)=>String.fromCharCode(char.charCodeAt(0) - 3)).join('');
    } catch  {
        return encryptedText; // Return as-is if decryption fails
    }
}
function validateApiKey(apiKey) {
    if (!apiKey || typeof apiKey !== 'string') {
        return {
            isValid: false,
            error: 'API key is required'
        };
    }
    if (apiKey.length < 10) {
        return {
            isValid: false,
            error: 'API key is too short'
        };
    }
    if (apiKey.includes(' ')) {
        return {
            isValid: false,
            error: 'API key should not contain spaces'
        };
    }
    return {
        isValid: true
    };
}
class RateLimiter {
    requests = [];
    maxRequests;
    timeWindow;
    constructor(maxRequests, timeWindowSeconds){
        this.maxRequests = maxRequests;
        this.timeWindow = timeWindowSeconds * 1000;
    }
    /**
   * Check if request is allowed
   * @returns Whether request is allowed
   */ isAllowed() {
        const now = Date.now();
        // Remove old requests outside the time window
        this.requests = this.requests.filter((time)=>now - time < this.timeWindow);
        // Check if we're under the limit
        if (this.requests.length < this.maxRequests) {
            this.requests.push(now);
            return true;
        }
        return false;
    }
    /**
   * Get time until next request is allowed
   * @returns Milliseconds until next request
   */ getTimeUntilReset() {
        if (this.requests.length === 0) return 0;
        const oldestRequest = Math.min(...this.requests);
        const timeUntilReset = this.timeWindow - (Date.now() - oldestRequest);
        return Math.max(0, timeUntilReset);
    }
}
}}),
"[project]/src/lib/api/angelone.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Angel One API client for the What If investment analysis tool
__turbopack_context__.s({
    "AngelOneClient": (()=>AngelOneClient)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$otplib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/otplib/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/config/index.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/security/index.ts [app-route] (ecmascript)");
;
;
;
;
class AngelOneClient {
    axiosInstance;
    jwtToken = null;
    refreshToken = null;
    feedToken = null;
    rateLimiter;
    apiKey;
    clientId;
    password;
    totpSecret;
    constructor(config){
        this.apiKey = config.apiKey;
        this.clientId = config.clientId;
        this.password = config.password;
        this.totpSecret = config.totpSecret;
        // Initialize rate limiter
        this.rateLimiter = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["RateLimiter"](__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["API_CONFIG"].rateLimit.requestsPerSecond, 1);
        // Create axios instance
        this.axiosInstance = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].create({
            baseURL: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["API_CONFIG"].angelOne.baseUrl,
            timeout: 30000,
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'X-UserType': 'USER',
                'X-SourceID': 'WEB',
                'X-ClientLocalIP': '127.0.0.1',
                'X-ClientPublicIP': '127.0.0.1',
                'X-MACAddress': '00:00:00:00:00:00',
                'X-PrivateKey': this.apiKey
            }
        });
        // Add request interceptor for rate limiting
        this.axiosInstance.interceptors.request.use(async (config)=>{
            // Wait if rate limit exceeded
            while(!this.rateLimiter.isAllowed()){
                const waitTime = this.rateLimiter.getTimeUntilReset();
                await new Promise((resolve)=>setTimeout(resolve, waitTime));
            }
            // Add JWT token if available
            if (this.jwtToken) {
                config.headers.Authorization = `Bearer ${this.jwtToken}`;
            }
            return config;
        }, (error)=>Promise.reject(error));
        // Add response interceptor for error handling
        this.axiosInstance.interceptors.response.use((response)=>response, async (error)=>{
            if (error.response?.status === 401 && this.refreshToken) {
                // Try to refresh token
                try {
                    await this.refreshAuthToken();
                    // Retry the original request
                    return this.axiosInstance.request(error.config);
                } catch  {
                    // Refresh failed, need to re-login
                    this.clearTokens();
                    throw new Error('Authentication failed. Please login again.');
                }
            }
            return Promise.reject(error);
        });
    }
    /**
   * Generate TOTP for authentication
   */ generateTOTP() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$otplib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["authenticator"].generate(this.totpSecret);
    }
    /**
   * Clear stored tokens
   */ clearTokens() {
        this.jwtToken = null;
        this.refreshToken = null;
        this.feedToken = null;
    }
    /**
   * Login to Angel One API
   */ async login() {
        try {
            const totp = this.generateTOTP();
            const loginRequest = {
                clientcode: this.clientId,
                password: this.password,
                totp: totp
            };
            console.log('Attempting login with:', (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["sanitizeForLogging"])(loginRequest));
            const response = await this.axiosInstance.post(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["API_CONFIG"].angelOne.endpoints.login, loginRequest);
            if (response.data.status && response.data.data) {
                this.jwtToken = response.data.data.jwtToken;
                this.refreshToken = response.data.data.refreshToken;
                this.feedToken = response.data.data.feedToken;
                return {
                    success: true,
                    message: 'Login successful'
                };
            } else {
                return {
                    success: false,
                    message: response.data.message || 'Login failed'
                };
            }
        } catch (error) {
            console.error('Login error:', (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["sanitizeForLogging"])({
                error
            }));
            return {
                success: false,
                message: error instanceof Error ? error.message : 'Login failed'
            };
        }
    }
    /**
   * Refresh authentication token
   */ async refreshAuthToken() {
        if (!this.refreshToken) {
            throw new Error('No refresh token available');
        }
        const response = await this.axiosInstance.post('/rest/auth/angelbroking/jwt/v1/generateTokens', {
            refreshToken: this.refreshToken
        });
        if (response.data.status && response.data.data) {
            this.jwtToken = response.data.data.jwtToken;
            this.refreshToken = response.data.data.refreshToken;
        } else {
            throw new Error('Token refresh failed');
        }
    }
    /**
   * Check if client is authenticated
   */ isAuthenticated() {
        return this.jwtToken !== null;
    }
    /**
   * Get historical data for a stock
   */ async getHistoricalData(request) {
        if (!this.isAuthenticated()) {
            throw new Error('Not authenticated. Please login first.');
        }
        try {
            const response = await this.axiosInstance.post(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["API_CONFIG"].angelOne.endpoints.historicalData, request);
            if (response.data.status && response.data.data) {
                return response.data.data.map((item)=>({
                        date: new Date(item[0]),
                        open: item[1],
                        high: item[2],
                        low: item[3],
                        close: item[4],
                        volume: item[5]
                    }));
            } else {
                throw new Error(response.data.message || 'Failed to fetch historical data');
            }
        } catch (error) {
            console.error('Historical data error:', (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["sanitizeForLogging"])({
                error,
                request
            }));
            throw error;
        }
    }
    /**
   * Get current price (LTP) for a stock
   */ async getCurrentPrice(request) {
        if (!this.isAuthenticated()) {
            throw new Error('Not authenticated. Please login first.');
        }
        try {
            const response = await this.axiosInstance.post(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["API_CONFIG"].angelOne.endpoints.ltp, request);
            if (response.data.status && response.data.data) {
                const data = response.data.data;
                return {
                    symbol: data.tradingsymbol,
                    name: data.tradingsymbol,
                    exchange: data.exchange,
                    token: data.symboltoken,
                    currentPrice: data.ltp,
                    lastUpdated: new Date()
                };
            } else {
                throw new Error(response.data.message || 'Failed to fetch current price');
            }
        } catch (error) {
            console.error('Current price error:', (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["sanitizeForLogging"])({
                error,
                request
            }));
            throw error;
        }
    }
    /**
   * Logout from Angel One API
   */ async logout() {
        if (!this.isAuthenticated()) {
            return {
                success: true,
                message: 'Already logged out'
            };
        }
        try {
            await this.axiosInstance.post(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["API_CONFIG"].angelOne.endpoints.logout, {
                clientcode: this.clientId
            });
            this.clearTokens();
            return {
                success: true,
                message: 'Logout successful'
            };
        } catch (error) {
            console.error('Logout error:', (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["sanitizeForLogging"])({
                error
            }));
            this.clearTokens(); // Clear tokens anyway
            return {
                success: false,
                message: error instanceof Error ? error.message : 'Logout failed'
            };
        }
    }
}
}}),
"[project]/src/app/api/test-auth/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET),
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$angelone$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/angelone.ts [app-route] (ecmascript)");
;
;
async function POST(request) {
    try {
        console.log('🧪 Testing Angel One authentication...');
        // Initialize Angel One client with environment variables
        const angelOneClient = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$angelone$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["AngelOneClient"]({
            apiKey: process.env.ANGEL_ONE_API_KEY,
            clientId: process.env.ANGEL_ONE_CLIENT_ID,
            password: process.env.ANGEL_ONE_PASSWORD,
            totpSecret: process.env.ANGEL_ONE_TOTP_SECRET
        });
        console.log('🔐 Attempting login...');
        await angelOneClient.login();
        console.log('✅ Login successful!');
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            status: 'success',
            message: 'Angel One authentication successful'
        });
    } catch (error) {
        console.error('💥 Auth test error:', error);
        console.error('Error type:', error?.constructor?.name);
        console.error('Error message:', error?.message);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Authentication test failed',
            details: error?.message,
            type: error?.constructor?.name
        }, {
            status: 500
        });
    }
}
async function GET() {
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
        message: 'Auth test endpoint ready'
    });
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__b0356064._.js.map