// Core types for the What If investment analysis tool

export interface StockData {
  symbol: string;
  name: string;
  exchange: 'NSE' | 'BSE';
  token: string;
  currentPrice: number;
  lastUpdated: Date;
}

export interface HistoricalPrice {
  date: Date;
  close: number; // Only closing price needed for investment calculations
}

// Full OHLC data interface (if needed for advanced analysis)
export interface FullHistoricalPrice {
  date: Date;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

export interface InvestmentScenario {
  id: string;
  stockSymbol: string;
  investmentAmount: number;
  startDate: Date;
  endDate: Date;
  createdAt: Date;
}

export interface InvestmentResult {
  scenario: InvestmentScenario;
  initialValue: number;
  currentValue: number;
  absoluteReturn: number;
  cagr: number;
  totalReturn: number;
  annualizedReturn: number;
}

export interface BenchmarkData {
  type: 'GOLD' | 'FD' | 'NIFTY';
  name: string;
  returns: {
    date: Date;
    value: number;
  }[];
}

export interface ComparisonResult {
  investment: InvestmentResult;
  benchmarks: {
    [key: string]: {
      initialValue: number;
      currentValue: number;
      cagr: number;
      absoluteReturn: number;
    };
  };
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface AngelOneConfig {
  apiKey: string;
  clientId: string;
  password: string;
  totpSecret: string;
}

export interface ChartDataPoint {
  date: string;
  value: number;
  label?: string;
}

export interface UserPreferences {
  defaultBenchmarks: string[];
  defaultTimeframe: string;
  currency: 'INR';
  theme: 'light' | 'dark';
}

// Angel One API specific types
export interface AngelOneLoginRequest {
  clientcode: string;
  password: string;
  totp: string;
}

export interface AngelOneLoginResponse {
  status: boolean;
  message: string;
  errorcode?: string;
  data?: {
    jwtToken: string;
    refreshToken: string;
    feedToken: string;
  };
}

export interface AngelOneHistoricalRequest {
  exchange: 'NSE' | 'BSE';
  symboltoken: string;
  interval: 'ONE_MINUTE' | 'THREE_MINUTE' | 'FIVE_MINUTE' | 'TEN_MINUTE' | 'FIFTEEN_MINUTE' | 'THIRTY_MINUTE' | 'ONE_HOUR' | 'ONE_DAY';
  fromdate: string; // YYYY-MM-DD HH:mm format
  todate: string;   // YYYY-MM-DD HH:mm format
}

export interface AngelOneHistoricalResponse {
  status: boolean;
  message: string;
  errorcode?: string;
  data?: Array<[string, number, number, number, number, number]>; // [timestamp, open, high, low, close, volume]
}

export interface AngelOneLTPRequest {
  exchange: 'NSE' | 'BSE';
  tradingsymbol: string;
  symboltoken: string;
}

export interface AngelOneLTPResponse {
  status: boolean;
  message: string;
  errorcode?: string;
  data?: {
    exchange: string;
    tradingsymbol: string;
    symboltoken: string;
    open: number;
    high: number;
    low: number;
    close: number;
    ltp: number;
  };
}
