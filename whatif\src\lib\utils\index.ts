// Utility functions for the What If investment analysis tool

import { CALCULATION_CONFIG } from '../config';

/**
 * Calculate Compound Annual Growth Rate (CAGR)
 * @param initialValue - Initial investment value
 * @param finalValue - Final investment value
 * @param years - Number of years
 * @returns CAGR as a percentage
 */
export function calculateCAGR(
  initialValue: number,
  finalValue: number,
  years: number
): number {
  if (initialValue <= 0 || finalValue <= 0 || years <= 0) {
    return 0;
  }
  
  const cagr = (Math.pow(finalValue / initialValue, 1 / years) - 1) * 100;
  return Number(cagr.toFixed(CALCULATION_CONFIG.precision.cagr));
}

/**
 * Calculate absolute return
 * @param initialValue - Initial investment value
 * @param finalValue - Final investment value
 * @returns Absolute return as a percentage
 */
export function calculateAbsoluteReturn(
  initialValue: number,
  finalValue: number
): number {
  if (initialValue <= 0) return 0;
  
  const absoluteReturn = ((finalValue - initialValue) / initialValue) * 100;
  return Number(absoluteReturn.toFixed(CALCULATION_CONFIG.precision.percentage));
}

/**
 * Calculate the number of years between two dates
 * @param startDate - Start date
 * @param endDate - End date
 * @returns Number of years (with decimals)
 */
export function calculateYearsBetweenDates(
  startDate: Date,
  endDate: Date
): number {
  const timeDiff = endDate.getTime() - startDate.getTime();
  const daysDiff = timeDiff / (1000 * 3600 * 24);
  return daysDiff / 365.25; // Account for leap years
}

/**
 * Format currency value
 * @param value - Numeric value
 * @param currency - Currency code (default: INR)
 * @returns Formatted currency string
 */
export function formatCurrency(value: number, currency: string = 'INR'): string {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: CALCULATION_CONFIG.precision.currency,
    maximumFractionDigits: CALCULATION_CONFIG.precision.currency,
  }).format(value);
}

/**
 * Format percentage value
 * @param value - Numeric value
 * @returns Formatted percentage string
 */
export function formatPercentage(value: number): string {
  return `${value.toFixed(CALCULATION_CONFIG.precision.percentage)}%`;
}

/**
 * Format large numbers with Indian numbering system
 * @param value - Numeric value
 * @returns Formatted number string (e.g., 1,00,000)
 */
export function formatIndianNumber(value: number): string {
  return new Intl.NumberFormat('en-IN').format(value);
}

/**
 * Validate date range
 * @param startDate - Start date
 * @param endDate - End date
 * @returns Validation result
 */
export function validateDateRange(startDate: Date, endDate: Date): {
  isValid: boolean;
  error?: string;
} {
  const now = new Date();

  if (startDate >= endDate) {
    return { isValid: false, error: 'Start date must be before end date' };
  }

  if (startDate > now) {
    return { isValid: false, error: 'Start date cannot be in the future' };
  }

  if (endDate > now) {
    return { isValid: false, error: 'End date cannot be in the future' };
  }

  // Angel One API historical data limits
  const angelOneMinDate = new Date('2015-01-01'); // Angel One typically provides data from 2015
  if (startDate < angelOneMinDate) {
    return {
      isValid: false,
      error: `Start date cannot be earlier than ${angelOneMinDate.toLocaleDateString()}. Angel One API historical data is limited.`
    };
  }

  // Maximum date range validation (Angel One may have limits on large ranges)
  const daysDifference = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
  const maxDays = 3650; // ~10 years maximum

  if (daysDifference > maxDays) {
    return {
      isValid: false,
      error: `Date range too large (${daysDifference} days). Maximum allowed is ${maxDays} days (~10 years).`
    };
  }

  // Minimum date range validation
  const minDays = 30; // At least 1 month for meaningful analysis
  if (daysDifference < minDays) {
    return {
      isValid: false,
      error: `Date range too small (${daysDifference} days). Minimum required is ${minDays} days for meaningful analysis.`
    };
  }

  return { isValid: true };
}

/**
 * Generate unique ID
 * @returns Unique string ID
 */
export function generateId(): string {
  return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Debounce function
 * @param func - Function to debounce
 * @param wait - Wait time in milliseconds
 * @returns Debounced function
 */
export function debounce<T extends (...args: unknown[]) => unknown>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * Check if market is open
 * @returns Boolean indicating if market is currently open
 */
export function isMarketOpen(): boolean {
  const now = new Date();
  const currentTime = now.toLocaleTimeString('en-IN', {
    timeZone: 'Asia/Kolkata',
    hour12: false,
  });
  
  const isWeekday = now.getDay() >= 1 && now.getDay() <= 5;
  const isWithinTradingHours = currentTime >= '09:15:00' && currentTime <= '15:30:00';
  
  return isWeekday && isWithinTradingHours;
}

/**
 * Safe JSON parse
 * @param jsonString - JSON string to parse
 * @param defaultValue - Default value if parsing fails
 * @returns Parsed object or default value
 */
export function safeJsonParse<T>(jsonString: string, defaultValue: T): T {
  try {
    return JSON.parse(jsonString);
  } catch {
    return defaultValue;
  }
}
