import { test, expect } from '@playwright/test';

/**
 * End-to-End Test: TCS Investment Analysis (Last 10 Years)
 * 
 * This test verifies the complete investment analysis workflow for TCS stock
 * from January 2015 to present, testing:
 * - Form submission and validation
 * - Real API integration with Angel One
 * - Investment calculations (CAGR, returns, etc.)
 * - Benchmark comparisons (Gold, FD, Nifty)
 * - Chart rendering and data visualization
 * - Performance metrics and insights
 */

test.describe('TCS Investment Analysis - 10 Year Performance', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the application
    await page.goto('http://localhost:3000');
    
    // Wait for the page to load completely
    await page.waitForLoadState('networkidle');
    
    // Verify the main heading is visible
    await expect(page.locator('h1')).toContainText('What If Investment Analyzer');
  });

  test('should analyze TCS investment from January 2015 to present', async ({ page }) => {
    // Test data for TCS 10-year analysis
    const investmentAmount = '500000'; // ₹5 Lakhs
    const startDate = '2015-01-01';    // 10 years ago
    const endDate = new Date().toISOString().split('T')[0]; // Today

    console.log(`Testing TCS investment analysis:`);
    console.log(`- Amount: ₹${investmentAmount}`);
    console.log(`- Period: ${startDate} to ${endDate}`);

    // Step 1: Fill the investment form
    await test.step('Fill investment analysis form', async () => {
      // Select TCS from popular stocks dropdown
      await page.locator('input[type="radio"][value="false"]').first().check();
      await page.selectOption('select', 'TCS-EQ');
      
      // Verify TCS is selected
      await expect(page.locator('select')).toHaveValue('TCS-EQ');
      
      // Fill investment amount
      await page.fill('input[type="number"]', investmentAmount);
      
      // Set start date (10 years ago)
      await page.fill('input[name="startDate"], input[id="startDate"]', startDate);
      
      // Set end date (today)
      await page.fill('input[name="endDate"], input[id="endDate"]', endDate);
      
      // Verify form is filled correctly
      await expect(page.locator('input[type="number"]')).toHaveValue(investmentAmount);
    });

    // Step 2: Submit the form and wait for analysis
    await test.step('Submit form and wait for analysis', async () => {
      // Click analyze button
      await page.click('button[type="submit"]');
      
      // Wait for loading state
      await expect(page.locator('text=Analyzing...')).toBeVisible();
      
      // Wait for results to load (up to 30 seconds for API calls)
      await page.waitForSelector('text=Analysis Results', { timeout: 30000 });
      
      // Verify we're on results page
      await expect(page.locator('h2')).toContainText('Analysis Results');
    });

    // Step 3: Verify investment summary cards
    await test.step('Verify investment summary metrics', async () => {
      // Check that all summary cards are present
      await expect(page.locator('text=Initial Investment')).toBeVisible();
      await expect(page.locator('text=Current Value')).toBeVisible();
      await expect(page.locator('text=Total Return')).toBeVisible();
      await expect(page.locator('text=CAGR')).toBeVisible();
      
      // Verify initial investment amount
      const initialInvestmentCard = page.locator('text=Initial Investment').locator('..').locator('..');
      await expect(initialInvestmentCard).toContainText('₹5,00,000');
      
      // Verify current value is greater than initial (TCS should have grown)
      const currentValueText = await page.locator('text=Current Value').locator('..').locator('..').locator('p').last().textContent();
      console.log(`Current Value: ${currentValueText}`);
      
      // Extract numeric value and verify it's greater than initial investment
      const currentValue = parseFloat(currentValueText?.replace(/[₹,]/g, '') || '0');
      expect(currentValue).toBeGreaterThan(500000);
      
      // Verify CAGR is positive (TCS has historically performed well)
      const cagrText = await page.locator('text=CAGR').locator('..').locator('..').locator('p').last().textContent();
      console.log(`CAGR: ${cagrText}`);
      expect(cagrText).toMatch(/\d+\.\d+%/);
    });

    // Step 4: Verify investment details section
    await test.step('Verify investment details', async () => {
      await expect(page.locator('text=Investment Details')).toBeVisible();
      
      // Check stock symbol
      await expect(page.locator('text=Stock Symbol:')).toBeVisible();
      await expect(page.locator('text=TCS-EQ')).toBeVisible();
      
      // Check investment period
      await expect(page.locator('text=Investment Period:')).toBeVisible();
      
      // Check absolute return
      await expect(page.locator('text=Absolute Return:')).toBeVisible();
      
      // Check annualized return
      await expect(page.locator('text=Annualized Return:')).toBeVisible();
    });

    // Step 5: Verify benchmark comparison
    await test.step('Verify benchmark comparisons', async () => {
      await expect(page.locator('text=Benchmark Performance')).toBeVisible();
      
      // Check that all benchmark cards are present
      await expect(page.locator('text=Gold')).toBeVisible();
      await expect(page.locator('text=Fixed Deposit')).toBeVisible();
      await expect(page.locator('text=Nifty 50')).toBeVisible();
      
      // Verify benchmark data is populated
      const goldCard = page.locator('text=Gold').locator('..').locator('..');
      await expect(goldCard).toContainText('CAGR:');
      await expect(goldCard).toContainText('Return:');
      
      // TCS should outperform FD over 10 years
      const fdCard = page.locator('text=Fixed Deposit').locator('..').locator('..');
      await expect(fdCard).toContainText('CAGR:');
    });

    // Step 6: Verify charts are rendered
    await test.step('Verify charts and visualizations', async () => {
      // Check for chart containers
      await expect(page.locator('text=Investment Growth Over Time')).toBeVisible();
      await expect(page.locator('text=Performance Comparison')).toBeVisible();
      
      // Verify chart components are loaded (they should contain SVG elements)
      const chartContainers = page.locator('.recharts-wrapper, canvas, svg');
      await expect(chartContainers.first()).toBeVisible();
    });

    // Step 7: Verify performance insights
    await test.step('Verify performance insights', async () => {
      await expect(page.locator('text=Performance Insights')).toBeVisible();
      
      // Check for detailed performance metrics
      await expect(page.locator('text=Detailed Performance Metrics')).toBeVisible();
      
      // Verify comparison table
      await expect(page.locator('text=Benchmark Comparison')).toBeVisible();
    });

    // Step 8: Test "New Analysis" functionality
    await test.step('Test new analysis workflow', async () => {
      // Click "New Analysis" button
      await page.click('text=New Analysis');
      
      // Should return to form
      await expect(page.locator('text=Stock Selection')).toBeVisible();
      await expect(page.locator('text=Investment Amount')).toBeVisible();
    });

    // Step 9: Log final results for verification
    await test.step('Log analysis results', async () => {
      // Go back to results to log final data
      await page.goBack();
      
      // Extract and log key metrics
      const currentValueText = await page.locator('text=Current Value').locator('..').locator('..').locator('p').last().textContent();
      const cagrText = await page.locator('text=CAGR').locator('..').locator('..').locator('p').last().textContent();
      const totalReturnText = await page.locator('text=Total Return').locator('..').locator('..').locator('p').last().textContent();
      
      console.log('\n=== TCS 10-Year Investment Analysis Results ===');
      console.log(`Initial Investment: ₹5,00,000`);
      console.log(`Current Value: ${currentValueText}`);
      console.log(`CAGR: ${cagrText}`);
      console.log(`Total Return: ${totalReturnText}`);
      console.log(`Period: January 2015 - ${new Date().toLocaleDateString()}`);
      console.log('===============================================\n');
    });
  });

  test('should handle form validation correctly', async ({ page }) => {
    await test.step('Test form validation', async () => {
      // Try to submit empty form
      await page.click('button[type="submit"]');
      
      // Should not proceed (form validation should prevent submission)
      await expect(page.locator('text=Analysis Results')).not.toBeVisible();
      
      // Fill only partial data and test validation
      await page.fill('input[type="number"]', '100000');
      await page.click('button[type="submit"]');
      
      // Should still not proceed without stock selection and dates
      await expect(page.locator('text=Analysis Results')).not.toBeVisible();
    });
  });

  test('should handle API errors gracefully', async ({ page }) => {
    await test.step('Test error handling', async () => {
      // Fill form with invalid stock symbol
      await page.locator('input[type="radio"]').last().check(); // Custom stock
      await page.fill('input[type="text"]', 'INVALID-STOCK');
      await page.fill('input[type="number"]', '100000');
      await page.fill('input[name="startDate"], input[id="startDate"]', '2023-01-01');
      await page.fill('input[name="endDate"], input[id="endDate"]', '2023-12-31');
      
      // Submit form
      await page.click('button[type="submit"]');
      
      // Should show error message
      await expect(page.locator('.text-red-700, .text-red-300')).toBeVisible();
    });
  });
});

// Additional test for performance benchmarking
test.describe('Performance Benchmarks', () => {
  test('should complete TCS analysis within reasonable time', async ({ page }) => {
    const startTime = Date.now();
    
    await page.goto('http://localhost:3000');
    
    // Fill and submit form quickly
    await page.selectOption('select', 'TCS-EQ');
    await page.fill('input[type="number"]', '100000');
    await page.fill('input[name="startDate"], input[id="startDate"]', '2020-01-01');
    await page.fill('input[name="endDate"], input[id="endDate"]', new Date().toISOString().split('T')[0]);
    
    await page.click('button[type="submit"]');
    await page.waitForSelector('text=Analysis Results', { timeout: 30000 });
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    console.log(`Analysis completed in ${duration}ms`);
    expect(duration).toBeLessThan(35000); // Should complete within 35 seconds
  });
});
