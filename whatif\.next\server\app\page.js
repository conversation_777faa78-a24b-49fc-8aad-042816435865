(()=>{var e={};e.id=974,e.ids=[974],e.modules={440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var n=r(1658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,n.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},554:(e,t)=>{"use strict";function r(e){return e.endsWith("/route")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isAppRouteRoute",{enumerable:!0,get:function(){return r}})},660:(e,t)=>{"use strict";function r(e){let t=5381;for(let r=0;r<e.length;r++)t=(t<<5)+t+e.charCodeAt(r)|0;return t>>>0}function n(e){return r(e).toString(36).slice(0,5)}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{djb2Hash:function(){return r},hexHash:function(){return n}})},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},1149:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))},1204:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\whatif\\\\whatif\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\app\\page.tsx","default")},1437:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{INTERCEPTION_ROUTE_MARKERS:function(){return a},extractInterceptionRouteInformation:function(){return s},isInterceptionRouteAppPath:function(){return i}});let n=r(4722),a=["(..)(..)","(.)","(..)","(...)"];function i(e){return void 0!==e.split("/").find(e=>a.find(t=>e.startsWith(t)))}function s(e){let t,r,i;for(let n of e.split("/"))if(r=a.find(e=>n.startsWith(e))){[t,i]=e.split(r,2);break}if(!t||!r||!i)throw Object.defineProperty(Error("Invalid interception route: "+e+". Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>"),"__NEXT_ERROR_CODE",{value:"E269",enumerable:!1,configurable:!0});switch(t=(0,n.normalizeAppPath)(t),r){case"(.)":i="/"===t?"/"+i:t+"/"+i;break;case"(..)":if("/"===t)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..) marker at the root level, use (.) instead."),"__NEXT_ERROR_CODE",{value:"E207",enumerable:!1,configurable:!0});i=t.split("/").slice(0,-1).concat(i).join("/");break;case"(...)":i="/"+i;break;case"(..)(..)":let s=t.split("/");if(s.length<=2)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..)(..) marker at the root level or one level up."),"__NEXT_ERROR_CODE",{value:"E486",enumerable:!1,configurable:!0});i=s.slice(0,-2).concat(i).join("/");break;default:throw Object.defineProperty(Error("Invariant: unexpected marker"),"__NEXT_ERROR_CODE",{value:"E112",enumerable:!1,configurable:!0})}return{interceptingRoute:t,interceptedRoute:i}}},1658:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{fillMetadataSegment:function(){return m},normalizeMetadataPageToRoute:function(){return p},normalizeMetadataRoute:function(){return f}});let n=r(8304),a=function(e){return e&&e.__esModule?e:{default:e}}(r(8671)),i=r(6341),s=r(4396),o=r(660),l=r(4722),c=r(2958),u=r(5499);function d(e){let t=a.default.dirname(e);if(e.endsWith("/sitemap"))return"";let r="";return t.split("/").some(e=>(0,u.isGroupSegment)(e)||(0,u.isParallelRouteSegment)(e))&&(r=(0,o.djb2Hash)(t).toString(36).slice(0,6)),r}function m(e,t,r){let n=(0,l.normalizeAppPath)(e),o=(0,s.getNamedRouteRegex)(n,{prefixRouteKeys:!1}),u=(0,i.interpolateDynamicPath)(n,t,o),{name:m,ext:f}=a.default.parse(r),p=d(a.default.posix.join(e,m)),g=p?`-${p}`:"";return(0,c.normalizePathSep)(a.default.join(u,`${m}${g}${f}`))}function f(e){if(!(0,n.isMetadataPage)(e))return e;let t=e,r="";if("/robots"===e?t+=".txt":"/manifest"===e?t+=".webmanifest":r=d(e),!t.endsWith("/route")){let{dir:e,name:n,ext:i}=a.default.parse(t);t=a.default.posix.join(e,`${n}${r?`-${r}`:""}${i}`,"route")}return t}function p(e,t){let r=e.endsWith("/route"),n=r?e.slice(0,-6):e,a=n.endsWith("/sitemap")?".xml":"";return(t?`${n}/[__metadata_id__]`:`${n}${a}`)+(r?"/route":"")}},2437:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getPathMatch",{enumerable:!0,get:function(){return a}});let n=r(5362);function a(e,t){let r=[],a=(0,n.pathToRegexp)(e,r,{delimiter:"/",sensitive:"boolean"==typeof(null==t?void 0:t.sensitive)&&t.sensitive,strict:null==t?void 0:t.strict}),i=(0,n.regexpToFunction)((null==t?void 0:t.regexModifier)?new RegExp(t.regexModifier(a.source),a.flags):a,r);return(e,n)=>{if("string"!=typeof e)return!1;let a=i(e);if(!a)return!1;if(null==t?void 0:t.removeUnnamedParams)for(let e of r)"number"==typeof e.name&&delete a.params[e.name];return{...n,...a.params}}}},2647:()=>{},2765:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))},2785:(e,t)=>{"use strict";function r(e){let t={};for(let[r,n]of e.entries()){let e=t[r];void 0===e?t[r]=n:Array.isArray(e)?e.push(n):t[r]=[e,n]}return t}function n(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function a(e){let t=new URLSearchParams;for(let[r,a]of Object.entries(e))if(Array.isArray(a))for(let e of a)t.append(r,n(e));else t.set(r,n(a));return t}function i(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];for(let t of r){for(let r of t.keys())e.delete(r);for(let[r,n]of t.entries())e.append(r,n)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{assign:function(){return i},searchParamsToUrlQuery:function(){return r},urlQueryToSearchParams:function(){return a}})},2827:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>g});var n={};r.r(n);var a={};r.r(a);var i={};r.r(i);var s={};r.r(s);var o=r(687),l=r(3210);let c=[{symbol:"SBIN-EQ",name:"State Bank of India",token:"3045"},{symbol:"RELIANCE-EQ",name:"Reliance Industries",token:"2885"},{symbol:"TCS-EQ",name:"Tata Consultancy Services",token:"11536"},{symbol:"INFY-EQ",name:"Infosys Limited",token:"1594"},{symbol:"HDFCBANK-EQ",name:"HDFC Bank",token:"1333"},{symbol:"ICICIBANK-EQ",name:"ICICI Bank",token:"4963"},{symbol:"BHARTIARTL-EQ",name:"Bharti Airtel",token:"10604"},{symbol:"ITC-EQ",name:"ITC Limited",token:"424"},{symbol:"KOTAKBANK-EQ",name:"Kotak Mahindra Bank",token:"1922"},{symbol:"LT-EQ",name:"Larsen & Toubro",token:"11483"}];function u({onSubmit:e,loading:t,error:r}){let[n,a]=(0,l.useState)({stockSymbol:"",customStock:"",investmentAmount:"",startDate:"",endDate:new Date().toISOString().split("T")[0]}),[i,s]=(0,l.useState)(!1),u=(e,t)=>{a(r=>({...r,[e]:t}))},d=c.find(e=>e.symbol===n.stockSymbol);return(0,o.jsxs)("form",{onSubmit:t=>{t.preventDefault();let r=i?n.customStock:n.stockSymbol;r&&n.investmentAmount&&n.startDate&&e({stockSymbol:r,investmentAmount:parseFloat(n.investmentAmount),startDate:new Date(n.startDate),endDate:new Date(n.endDate)})},className:"space-y-6",children:[(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,o.jsx)("div",{className:"space-y-4",children:(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Stock Selection"}),(0,o.jsxs)("div",{className:"space-y-3",children:[(0,o.jsxs)("div",{children:[(0,o.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,o.jsx)("input",{type:"radio",checked:!i,onChange:()=>s(!1),className:"text-blue-600"}),(0,o.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:"Popular Stocks"})]}),!i&&(0,o.jsxs)("select",{value:n.stockSymbol,onChange:e=>u("stockSymbol",e.target.value),className:"mt-2 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",required:!i,children:[(0,o.jsx)("option",{value:"",children:"Select a stock..."}),c.map(e=>(0,o.jsxs)("option",{value:e.symbol,children:[e.name," (",e.symbol,")"]},e.symbol))]})]}),(0,o.jsxs)("div",{children:[(0,o.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,o.jsx)("input",{type:"radio",checked:i,onChange:()=>s(!0),className:"text-blue-600"}),(0,o.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:"Custom Stock Symbol"})]}),i&&(0,o.jsx)("input",{type:"text",value:n.customStock,onChange:e=>u("customStock",e.target.value),placeholder:"e.g., WIPRO-EQ",className:"mt-2 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",required:i})]})]}),d&&!i&&(0,o.jsx)("div",{className:"mt-2 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-md",children:(0,o.jsxs)("p",{className:"text-sm text-blue-700 dark:text-blue-300",children:["Selected: ",(0,o.jsx)("strong",{children:d.name})]})})]})}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{htmlFor:"investmentAmount",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Investment Amount (₹)"}),(0,o.jsx)("input",{type:"number",id:"investmentAmount",value:n.investmentAmount,onChange:e=>u("investmentAmount",e.target.value),placeholder:"e.g., 100000",min:"1000",step:"1000",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",required:!0}),(0,o.jsx)("p",{className:"mt-1 text-xs text-gray-500 dark:text-gray-400",children:"Minimum investment: ₹1,000"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{htmlFor:"startDate",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Investment Start Date"}),(0,o.jsx)("input",{type:"date",id:"startDate",value:n.startDate,onChange:e=>u("startDate",e.target.value),max:new Date().toISOString().split("T")[0],className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",required:!0})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{htmlFor:"endDate",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Analysis End Date"}),(0,o.jsx)("input",{type:"date",id:"endDate",value:n.endDate,onChange:e=>u("endDate",e.target.value),max:new Date().toISOString().split("T")[0],className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",required:!0}),(0,o.jsx)("p",{className:"mt-1 text-xs text-gray-500 dark:text-gray-400",children:"Default: Today's date"})]})]}),r&&(0,o.jsx)("div",{className:"p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md",children:(0,o.jsx)("p",{className:"text-sm text-red-700 dark:text-red-300",children:r})}),(0,o.jsx)("div",{className:"flex justify-center",children:(0,o.jsx)("button",{type:"submit",disabled:t,className:"px-8 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:t?(0,o.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,o.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}),(0,o.jsx)("span",{children:"Analyzing..."})]}):"Analyze Investment"})}),(0,o.jsxs)("div",{className:"mt-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-md",children:[(0,o.jsx)("h4",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Quick Examples:"}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2 text-xs text-gray-600 dark:text-gray-400",children:[(0,o.jsx)("div",{children:"• ₹1,00,000 in SBIN from Jan 2023"}),(0,o.jsx)("div",{children:"• ₹50,000 in TCS from Jan 2022"}),(0,o.jsx)("div",{children:"• ₹2,00,000 in RELIANCE from Jan 2021"}),(0,o.jsx)("div",{children:"• ₹75,000 in INFY from Jan 2020"})]})]})]})}let d={precision:{currency:2,percentage:2,cagr:2}};function m(e,t="INR"){return new Intl.NumberFormat("en-IN",{style:"currency",currency:t,minimumFractionDigits:d.precision.currency,maximumFractionDigits:d.precision.currency}).format(e)}function f(e){return`${e.toFixed(d.precision.percentage)}%`}function p({data:e}){let{investmentResult:t,comparisonResult:r,comparisonSummary:l,chartData:c}=e;return(0,o.jsxs)("div",{className:"space-y-6",children:[(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg",children:[(0,o.jsx)("h3",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Initial Investment"}),(0,o.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:m(t.initialValue)})]}),(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg",children:[(0,o.jsx)("h3",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Current Value"}),(0,o.jsx)("p",{className:"text-2xl font-bold text-green-600 dark:text-green-400",children:m(t.currentValue)})]}),(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg",children:[(0,o.jsx)("h3",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Total Return"}),(0,o.jsx)("p",{className:`text-2xl font-bold ${t.totalReturn>=0?"text-green-600 dark:text-green-400":"text-red-600 dark:text-red-400"}`,children:m(t.totalReturn)})]}),(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg",children:[(0,o.jsx)("h3",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"CAGR"}),(0,o.jsx)("p",{className:`text-2xl font-bold ${t.cagr>=0?"text-green-600 dark:text-green-400":"text-red-600 dark:text-red-400"}`,children:f(t.cagr)})]})]}),(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6",children:[(0,o.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Investment Details"}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,o.jsxs)("div",{className:"space-y-3",children:[(0,o.jsxs)("div",{className:"flex justify-between",children:[(0,o.jsx)("span",{className:"text-gray-600 dark:text-gray-400",children:"Stock Symbol:"}),(0,o.jsx)("span",{className:"font-medium text-gray-900 dark:text-white",children:t.scenario.stockSymbol})]}),(0,o.jsxs)("div",{className:"flex justify-between",children:[(0,o.jsx)("span",{className:"text-gray-600 dark:text-gray-400",children:"Investment Period:"}),(0,o.jsxs)("span",{className:"font-medium text-gray-900 dark:text-white",children:[t.scenario.startDate.toLocaleDateString()," - ",t.scenario.endDate.toLocaleDateString()]})]}),(0,o.jsxs)("div",{className:"flex justify-between",children:[(0,o.jsx)("span",{className:"text-gray-600 dark:text-gray-400",children:"Absolute Return:"}),(0,o.jsx)("span",{className:`font-medium ${t.absoluteReturn>=0?"text-green-600 dark:text-green-400":"text-red-600 dark:text-red-400"}`,children:f(t.absoluteReturn)})]})]}),(0,o.jsxs)("div",{className:"space-y-3",children:[(0,o.jsxs)("div",{className:"flex justify-between",children:[(0,o.jsx)("span",{className:"text-gray-600 dark:text-gray-400",children:"Annualized Return:"}),(0,o.jsx)("span",{className:`font-medium ${t.annualizedReturn>=0?"text-green-600 dark:text-green-400":"text-red-600 dark:text-red-400"}`,children:f(t.annualizedReturn)})]}),(0,o.jsxs)("div",{className:"flex justify-between",children:[(0,o.jsx)("span",{className:"text-gray-600 dark:text-gray-400",children:"Investment Amount:"}),(0,o.jsx)("span",{className:"font-medium text-gray-900 dark:text-white",children:m(t.scenario.investmentAmount)})]}),(0,o.jsxs)("div",{className:"flex justify-between",children:[(0,o.jsx)("span",{className:"text-gray-600 dark:text-gray-400",children:"Profit/Loss:"}),(0,o.jsxs)("span",{className:`font-medium ${t.totalReturn>=0?"text-green-600 dark:text-green-400":"text-red-600 dark:text-red-400"}`,children:[t.totalReturn>=0?"+":"",m(t.totalReturn)]})]})]})]})]}),l&&(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6",children:[(0,o.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Performance Insights"}),(0,o.jsx)("div",{className:"space-y-2",children:l.insights?.map((e,t)=>(0,o.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,o.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"}),(0,o.jsx)("p",{className:"text-gray-700 dark:text-gray-300",children:e})]},t))})]}),(0,o.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[c?.timeSeriesData&&(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6",children:[(0,o.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Investment Growth Over Time"}),(0,o.jsx)(a.TimeSeriesChart,{data:c.timeSeriesData})]}),c?.barChartData&&(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6",children:[(0,o.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Performance Comparison"}),(0,o.jsx)(i.ComparisonBarChart,{data:c.barChartData})]})]}),l&&(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6",children:[(0,o.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Detailed Performance Metrics"}),(0,o.jsx)(s.PerformanceMetrics,{data:{bestPerformer:{name:1===l.investment.rank?l.investment.name:l.benchmarks[0]?.name||"N/A",cagr:1===l.investment.rank?l.investment.cagr:l.benchmarks[0]?.cagr||0,absoluteReturn:1===l.investment.rank?l.investment.absoluteReturn:l.benchmarks[0]?.absoluteReturn||0},worstPerformer:{name:l.benchmarks[l.benchmarks.length-1]?.name||"N/A",cagr:l.benchmarks[l.benchmarks.length-1]?.cagr||0,absoluteReturn:l.benchmarks[l.benchmarks.length-1]?.absoluteReturn||0},averageCagr:(l.investment.cagr+l.benchmarks.reduce((e,t)=>e+t.cagr,0))/(l.benchmarks.length+1),volatilityRanking:[{name:"Fixed Deposit",volatility:0},{name:"Gold",volatility:15},{name:"Nifty 50",volatility:20},{name:l.investment.name,volatility:25}].sort((e,t)=>e.volatility-t.volatility)},investmentAmount:t.scenario.investmentAmount,investmentPeriod:`${t.scenario.startDate.toLocaleDateString()} - ${t.scenario.endDate.toLocaleDateString()}`})]}),l&&(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6",children:[(0,o.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Benchmark Comparison"}),(0,o.jsx)(n.ComparisonTable,{data:[l.investment,...l.benchmarks],investmentName:l.investment.name,title:"Investment vs Benchmarks",showOutperformance:!0})]}),(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6",children:[(0,o.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Benchmark Performance"}),(0,o.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:Object.entries(r.benchmarks).map(([e,t])=>(0,o.jsxs)("div",{className:"p-4 border border-gray-200 dark:border-gray-700 rounded-lg",children:[(0,o.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white mb-2",children:"GOLD"===e?"Gold":"FD"===e?"Fixed Deposit":"NIFTY"===e?"Nifty 50":e}),(0,o.jsxs)("div",{className:"space-y-1 text-sm",children:[(0,o.jsxs)("div",{className:"flex justify-between",children:[(0,o.jsx)("span",{className:"text-gray-600 dark:text-gray-400",children:"Initial:"}),(0,o.jsx)("span",{className:"text-gray-900 dark:text-white",children:m(t.initialValue)})]}),(0,o.jsxs)("div",{className:"flex justify-between",children:[(0,o.jsx)("span",{className:"text-gray-600 dark:text-gray-400",children:"Current:"}),(0,o.jsx)("span",{className:"text-gray-900 dark:text-white",children:m(t.currentValue)})]}),(0,o.jsxs)("div",{className:"flex justify-between",children:[(0,o.jsx)("span",{className:"text-gray-600 dark:text-gray-400",children:"CAGR:"}),(0,o.jsx)("span",{className:`${t.cagr>=0?"text-green-600 dark:text-green-400":"text-red-600 dark:text-red-400"}`,children:f(t.cagr)})]}),(0,o.jsxs)("div",{className:"flex justify-between",children:[(0,o.jsx)("span",{className:"text-gray-600 dark:text-gray-400",children:"Return:"}),(0,o.jsx)("span",{className:`${t.absoluteReturn>=0?"text-green-600 dark:text-green-400":"text-red-600 dark:text-red-400"}`,children:f(t.absoluteReturn)})]})]})]},e))})]}),(0,o.jsx)("div",{className:"flex justify-center",children:(0,o.jsx)("button",{className:"px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors",children:"Save This Scenario"})})]})}function g(){let[e,t]=(0,l.useState)(null),[r,n]=(0,l.useState)(!1),[a,i]=(0,l.useState)(null),s=async e=>{n(!0),i(null);try{let r=await fetch("/api/analyze",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!r.ok)throw Error("Failed to analyze investment");let n=await r.json();t(n)}catch(e){i(e instanceof Error?e.message:"An error occurred")}finally{n(!1)}};return(0,o.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800",children:(0,o.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,o.jsxs)("div",{className:"text-center mb-8",children:[(0,o.jsx)("h1",{className:"text-4xl font-bold text-gray-900 dark:text-white mb-2",children:"What If Investment Analyzer"}),(0,o.jsx)("p",{className:"text-lg text-gray-600 dark:text-gray-300",children:"Analyze your investment scenarios with real market data and benchmark comparisons"})]}),(0,o.jsx)("div",{className:"max-w-7xl mx-auto",children:e?(0,o.jsxs)("div",{className:"space-y-6",children:[(0,o.jsxs)("div",{className:"flex justify-between items-center",children:[(0,o.jsx)("h2",{className:"text-2xl font-semibold text-gray-900 dark:text-white",children:"Analysis Results"}),(0,o.jsx)("button",{onClick:()=>{t(null),i(null)},className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"New Analysis"})]}),(0,o.jsx)(p,{data:e})]}):(0,o.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6",children:(0,o.jsx)(u,{onSubmit:s,loading:r,error:a})})})]})})}},2958:(e,t)=>{"use strict";function r(e){return e.replace(/\\/g,"/")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathSep",{enumerable:!0,get:function(){return r}})},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3293:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"escapeStringRegexp",{enumerable:!0,get:function(){return a}});let r=/[|\\{}()[\]^$+*?.-]/,n=/[|\\{}()[\]^$+*?.-]/g;function a(e){return r.test(e)?e.replace(n,"\\$&"):e}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3736:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseRelativeUrl",{enumerable:!0,get:function(){return a}}),r(4827);let n=r(2785);function a(e,t,r){void 0===r&&(r=!0);let a=new URL("http://n"),i=t?new URL(t,a):e.startsWith(".")?new URL("http://n"):a,{pathname:s,searchParams:o,search:l,hash:c,href:u,origin:d}=new URL(e,i);if(d!==a.origin)throw Object.defineProperty(Error("invariant: invalid relative URL, router received "+e),"__NEXT_ERROR_CODE",{value:"E159",enumerable:!1,configurable:!0});return{pathname:s,query:r?(0,n.searchParamsToUrlQuery)(o):void 0,search:l,hash:c,href:u.slice(d.length)}}},3873:e=>{"use strict";e.exports=require("path")},4116:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>s.a,__next_app__:()=>d,pages:()=>u,routeModule:()=>m,tree:()=>c});var n=r(5239),a=r(8088),i=r(8170),s=r.n(i),o=r(893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let c={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,1204)),"C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\app\\page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,u=["C:\\Users\\<USER>\\Documents\\augment-projects\\whatif\\whatif\\src\\app\\page.tsx"],d={require:r,loadChunk:()=>Promise.resolve()},m=new n.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},4396:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getNamedMiddlewareRegex:function(){return g},getNamedRouteRegex:function(){return p},getRouteRegex:function(){return d},parseParameter:function(){return l}});let n=r(6143),a=r(1437),i=r(3293),s=r(2887),o=/^([^[]*)\[((?:\[[^\]]*\])|[^\]]+)\](.*)$/;function l(e){let t=e.match(o);return t?c(t[2]):c(e)}function c(e){let t=e.startsWith("[")&&e.endsWith("]");t&&(e=e.slice(1,-1));let r=e.startsWith("...");return r&&(e=e.slice(3)),{key:e,repeat:r,optional:t}}function u(e,t,r){let n={},l=1,u=[];for(let d of(0,s.removeTrailingSlash)(e).slice(1).split("/")){let e=a.INTERCEPTION_ROUTE_MARKERS.find(e=>d.startsWith(e)),s=d.match(o);if(e&&s&&s[2]){let{key:t,optional:r,repeat:a}=c(s[2]);n[t]={pos:l++,repeat:a,optional:r},u.push("/"+(0,i.escapeStringRegexp)(e)+"([^/]+?)")}else if(s&&s[2]){let{key:e,repeat:t,optional:a}=c(s[2]);n[e]={pos:l++,repeat:t,optional:a},r&&s[1]&&u.push("/"+(0,i.escapeStringRegexp)(s[1]));let o=t?a?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)";r&&s[1]&&(o=o.substring(1)),u.push(o)}else u.push("/"+(0,i.escapeStringRegexp)(d));t&&s&&s[3]&&u.push((0,i.escapeStringRegexp)(s[3]))}return{parameterizedRoute:u.join(""),groups:n}}function d(e,t){let{includeSuffix:r=!1,includePrefix:n=!1,excludeOptionalTrailingSlash:a=!1}=void 0===t?{}:t,{parameterizedRoute:i,groups:s}=u(e,r,n),o=i;return a||(o+="(?:/)?"),{re:RegExp("^"+o+"$"),groups:s}}function m(e){let t,{interceptionMarker:r,getSafeRouteKey:n,segment:a,routeKeys:s,keyPrefix:o,backreferenceDuplicateKeys:l}=e,{key:u,optional:d,repeat:m}=c(a),f=u.replace(/\W/g,"");o&&(f=""+o+f);let p=!1;(0===f.length||f.length>30)&&(p=!0),isNaN(parseInt(f.slice(0,1)))||(p=!0),p&&(f=n());let g=f in s;o?s[f]=""+o+u:s[f]=u;let h=r?(0,i.escapeStringRegexp)(r):"";return t=g&&l?"\\k<"+f+">":m?"(?<"+f+">.+?)":"(?<"+f+">[^/]+?)",d?"(?:/"+h+t+")?":"/"+h+t}function f(e,t,r,l,c){let u,d=(u=0,()=>{let e="",t=++u;for(;t>0;)e+=String.fromCharCode(97+(t-1)%26),t=Math.floor((t-1)/26);return e}),f={},p=[];for(let u of(0,s.removeTrailingSlash)(e).slice(1).split("/")){let e=a.INTERCEPTION_ROUTE_MARKERS.some(e=>u.startsWith(e)),s=u.match(o);if(e&&s&&s[2])p.push(m({getSafeRouteKey:d,interceptionMarker:s[1],segment:s[2],routeKeys:f,keyPrefix:t?n.NEXT_INTERCEPTION_MARKER_PREFIX:void 0,backreferenceDuplicateKeys:c}));else if(s&&s[2]){l&&s[1]&&p.push("/"+(0,i.escapeStringRegexp)(s[1]));let e=m({getSafeRouteKey:d,segment:s[2],routeKeys:f,keyPrefix:t?n.NEXT_QUERY_PARAM_PREFIX:void 0,backreferenceDuplicateKeys:c});l&&s[1]&&(e=e.substring(1)),p.push(e)}else p.push("/"+(0,i.escapeStringRegexp)(u));r&&s&&s[3]&&p.push((0,i.escapeStringRegexp)(s[3]))}return{namedParameterizedRoute:p.join(""),routeKeys:f}}function p(e,t){var r,n,a;let i=f(e,t.prefixRouteKeys,null!=(r=t.includeSuffix)&&r,null!=(n=t.includePrefix)&&n,null!=(a=t.backreferenceDuplicateKeys)&&a),s=i.namedParameterizedRoute;return t.excludeOptionalTrailingSlash||(s+="(?:/)?"),{...d(e,t),namedRegex:"^"+s+"$",routeKeys:i.routeKeys}}function g(e,t){let{parameterizedRoute:r}=u(e,!1,!1),{catchAll:n=!0}=t;if("/"===r)return{namedRegex:"^/"+(n?".*":"")+"$"};let{namedParameterizedRoute:a}=f(e,!1,!1,!1,!1);return{namedRegex:"^"+a+(n?"(?:(/.*)?)":"")+"$"}}},4399:()=>{},4431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c,metadata:()=>l});var n=r(7413),a=r(2376),i=r.n(a),s=r(8726),o=r.n(s);r(1135);let l={title:"Create Next App",description:"Generated by create next app"};function c({children:e}){return(0,n.jsx)("html",{lang:"en",children:(0,n.jsx)("body",{className:`${i().variable} ${o().variable} antialiased`,children:e})})}},4722:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{normalizeAppPath:function(){return i},normalizeRscURL:function(){return s}});let n=r(5531),a=r(5499);function i(e){return(0,n.ensureLeadingSlash)(e.split("/").reduce((e,t,r,n)=>!t||(0,a.isGroupSegment)(t)||"@"===t[0]||("page"===t||"route"===t)&&r===n.length-1?e:e+"/"+t,""))}function s(e){return e.replace(/\.rsc($|\?)/,"$1")}},4827:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DecodeError:function(){return p},MiddlewareNotFoundError:function(){return y},MissingStaticPage:function(){return x},NormalizeError:function(){return g},PageNotFoundError:function(){return h},SP:function(){return m},ST:function(){return f},WEB_VITALS:function(){return r},execOnce:function(){return n},getDisplayName:function(){return l},getLocationOrigin:function(){return s},getURL:function(){return o},isAbsoluteUrl:function(){return i},isResSent:function(){return c},loadGetInitialProps:function(){return d},normalizeRepeatedSlashes:function(){return u},stringifyError:function(){return b}});let r=["CLS","FCP","FID","INP","LCP","TTFB"];function n(e){let t,r=!1;return function(){for(var n=arguments.length,a=Array(n),i=0;i<n;i++)a[i]=arguments[i];return r||(r=!0,t=e(...a)),t}}let a=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,i=e=>a.test(e);function s(){let{protocol:e,hostname:t,port:r}=window.location;return e+"//"+t+(r?":"+r:"")}function o(){let{href:e}=window.location,t=s();return e.substring(t.length)}function l(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function c(e){return e.finished||e.headersSent}function u(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function d(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await d(t.Component,t.ctx)}:{};let n=await e.getInitialProps(t);if(r&&c(r))return n;if(!n)throw Object.defineProperty(Error('"'+l(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n}let m="undefined"!=typeof performance,f=m&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class p extends Error{}class g extends Error{}class h extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class x extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class y extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function b(e){return JSON.stringify({message:e.message,stack:e.stack})}},5362:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{function e(e,t){void 0===t&&(t={});for(var r=function(e){for(var t=[],r=0;r<e.length;){var n=e[r];if("*"===n||"+"===n||"?"===n){t.push({type:"MODIFIER",index:r,value:e[r++]});continue}if("\\"===n){t.push({type:"ESCAPED_CHAR",index:r++,value:e[r++]});continue}if("{"===n){t.push({type:"OPEN",index:r,value:e[r++]});continue}if("}"===n){t.push({type:"CLOSE",index:r,value:e[r++]});continue}if(":"===n){for(var a="",i=r+1;i<e.length;){var s=e.charCodeAt(i);if(s>=48&&s<=57||s>=65&&s<=90||s>=97&&s<=122||95===s){a+=e[i++];continue}break}if(!a)throw TypeError("Missing parameter name at "+r);t.push({type:"NAME",index:r,value:a}),r=i;continue}if("("===n){var o=1,l="",i=r+1;if("?"===e[i])throw TypeError('Pattern cannot start with "?" at '+i);for(;i<e.length;){if("\\"===e[i]){l+=e[i++]+e[i++];continue}if(")"===e[i]){if(0==--o){i++;break}}else if("("===e[i]&&(o++,"?"!==e[i+1]))throw TypeError("Capturing groups are not allowed at "+i);l+=e[i++]}if(o)throw TypeError("Unbalanced pattern at "+r);if(!l)throw TypeError("Missing pattern at "+r);t.push({type:"PATTERN",index:r,value:l}),r=i;continue}t.push({type:"CHAR",index:r,value:e[r++]})}return t.push({type:"END",index:r,value:""}),t}(e),n=t.prefixes,i=void 0===n?"./":n,s="[^"+a(t.delimiter||"/#?")+"]+?",o=[],l=0,c=0,u="",d=function(e){if(c<r.length&&r[c].type===e)return r[c++].value},m=function(e){var t=d(e);if(void 0!==t)return t;var n=r[c];throw TypeError("Unexpected "+n.type+" at "+n.index+", expected "+e)},f=function(){for(var e,t="";e=d("CHAR")||d("ESCAPED_CHAR");)t+=e;return t};c<r.length;){var p=d("CHAR"),g=d("NAME"),h=d("PATTERN");if(g||h){var x=p||"";-1===i.indexOf(x)&&(u+=x,x=""),u&&(o.push(u),u=""),o.push({name:g||l++,prefix:x,suffix:"",pattern:h||s,modifier:d("MODIFIER")||""});continue}var y=p||d("ESCAPED_CHAR");if(y){u+=y;continue}if(u&&(o.push(u),u=""),d("OPEN")){var x=f(),b=d("NAME")||"",v=d("PATTERN")||"",j=f();m("CLOSE"),o.push({name:b||(v?l++:""),pattern:b&&!v?s:v,prefix:x,suffix:j,modifier:d("MODIFIER")||""});continue}m("END")}return o}function r(e,t){void 0===t&&(t={});var r=i(t),n=t.encode,a=void 0===n?function(e){return e}:n,s=t.validate,o=void 0===s||s,l=e.map(function(e){if("object"==typeof e)return RegExp("^(?:"+e.pattern+")$",r)});return function(t){for(var r="",n=0;n<e.length;n++){var i=e[n];if("string"==typeof i){r+=i;continue}var s=t?t[i.name]:void 0,c="?"===i.modifier||"*"===i.modifier,u="*"===i.modifier||"+"===i.modifier;if(Array.isArray(s)){if(!u)throw TypeError('Expected "'+i.name+'" to not repeat, but got an array');if(0===s.length){if(c)continue;throw TypeError('Expected "'+i.name+'" to not be empty')}for(var d=0;d<s.length;d++){var m=a(s[d],i);if(o&&!l[n].test(m))throw TypeError('Expected all "'+i.name+'" to match "'+i.pattern+'", but got "'+m+'"');r+=i.prefix+m+i.suffix}continue}if("string"==typeof s||"number"==typeof s){var m=a(String(s),i);if(o&&!l[n].test(m))throw TypeError('Expected "'+i.name+'" to match "'+i.pattern+'", but got "'+m+'"');r+=i.prefix+m+i.suffix;continue}if(!c){var f=u?"an array":"a string";throw TypeError('Expected "'+i.name+'" to be '+f)}}return r}}function n(e,t,r){void 0===r&&(r={});var n=r.decode,a=void 0===n?function(e){return e}:n;return function(r){var n=e.exec(r);if(!n)return!1;for(var i=n[0],s=n.index,o=Object.create(null),l=1;l<n.length;l++)!function(e){if(void 0!==n[e]){var r=t[e-1];"*"===r.modifier||"+"===r.modifier?o[r.name]=n[e].split(r.prefix+r.suffix).map(function(e){return a(e,r)}):o[r.name]=a(n[e],r)}}(l);return{path:i,index:s,params:o}}}function a(e){return e.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function i(e){return e&&e.sensitive?"":"i"}function s(e,t,r){void 0===r&&(r={});for(var n=r.strict,s=void 0!==n&&n,o=r.start,l=r.end,c=r.encode,u=void 0===c?function(e){return e}:c,d="["+a(r.endsWith||"")+"]|$",m="["+a(r.delimiter||"/#?")+"]",f=void 0===o||o?"^":"",p=0;p<e.length;p++){var g=e[p];if("string"==typeof g)f+=a(u(g));else{var h=a(u(g.prefix)),x=a(u(g.suffix));if(g.pattern)if(t&&t.push(g),h||x)if("+"===g.modifier||"*"===g.modifier){var y="*"===g.modifier?"?":"";f+="(?:"+h+"((?:"+g.pattern+")(?:"+x+h+"(?:"+g.pattern+"))*)"+x+")"+y}else f+="(?:"+h+"("+g.pattern+")"+x+")"+g.modifier;else f+="("+g.pattern+")"+g.modifier;else f+="(?:"+h+x+")"+g.modifier}}if(void 0===l||l)s||(f+=m+"?"),f+=r.endsWith?"(?="+d+")":"$";else{var b=e[e.length-1],v="string"==typeof b?m.indexOf(b[b.length-1])>-1:void 0===b;s||(f+="(?:"+m+"(?="+d+"))?"),v||(f+="(?="+m+"|"+d+")")}return new RegExp(f,i(r))}function o(t,r,n){if(t instanceof RegExp){if(!r)return t;var a=t.source.match(/\((?!\?)/g);if(a)for(var l=0;l<a.length;l++)r.push({name:l,prefix:"",suffix:"",modifier:"",pattern:""});return t}return Array.isArray(t)?RegExp("(?:"+t.map(function(e){return o(e,r,n).source}).join("|")+")",i(n)):s(e(t,n),r,n)}Object.defineProperty(t,"__esModule",{value:!0}),t.parse=e,t.compile=function(t,n){return r(e(t,n),n)},t.tokensToFunction=r,t.match=function(e,t){var r=[];return n(o(e,r,t),r,t)},t.regexpToFunction=n,t.tokensToRegexp=s,t.pathToRegexp=o})(),e.exports=t})()},5526:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{compileNonPath:function(){return u},matchHas:function(){return c},parseDestination:function(){return d},prepareDestination:function(){return m}});let n=r(5362),a=r(3293),i=r(6759),s=r(1437),o=r(8212);function l(e){return e.replace(/__ESC_COLON_/gi,":")}function c(e,t,r,n){void 0===r&&(r=[]),void 0===n&&(n=[]);let a={},i=r=>{let n,i=r.key;switch(r.type){case"header":i=i.toLowerCase(),n=e.headers[i];break;case"cookie":n="cookies"in e?e.cookies[r.key]:(0,o.getCookieParser)(e.headers)()[r.key];break;case"query":n=t[i];break;case"host":{let{host:t}=(null==e?void 0:e.headers)||{};n=null==t?void 0:t.split(":",1)[0].toLowerCase()}}if(!r.value&&n)return a[function(e){let t="";for(let r=0;r<e.length;r++){let n=e.charCodeAt(r);(n>64&&n<91||n>96&&n<123)&&(t+=e[r])}return t}(i)]=n,!0;if(n){let e=RegExp("^"+r.value+"$"),t=Array.isArray(n)?n.slice(-1)[0].match(e):n.match(e);if(t)return Array.isArray(t)&&(t.groups?Object.keys(t.groups).forEach(e=>{a[e]=t.groups[e]}):"host"===r.type&&t[0]&&(a.host=t[0])),!0}return!1};return!(!r.every(e=>i(e))||n.some(e=>i(e)))&&a}function u(e,t){if(!e.includes(":"))return e;for(let r of Object.keys(t))e.includes(":"+r)&&(e=e.replace(RegExp(":"+r+"\\*","g"),":"+r+"--ESCAPED_PARAM_ASTERISKS").replace(RegExp(":"+r+"\\?","g"),":"+r+"--ESCAPED_PARAM_QUESTION").replace(RegExp(":"+r+"\\+","g"),":"+r+"--ESCAPED_PARAM_PLUS").replace(RegExp(":"+r+"(?!\\w)","g"),"--ESCAPED_PARAM_COLON"+r));return e=e.replace(/(:|\*|\?|\+|\(|\)|\{|\})/g,"\\$1").replace(/--ESCAPED_PARAM_PLUS/g,"+").replace(/--ESCAPED_PARAM_COLON/g,":").replace(/--ESCAPED_PARAM_QUESTION/g,"?").replace(/--ESCAPED_PARAM_ASTERISKS/g,"*"),(0,n.compile)("/"+e,{validate:!1})(t).slice(1)}function d(e){let t=e.destination;for(let r of Object.keys({...e.params,...e.query}))r&&(t=t.replace(RegExp(":"+(0,a.escapeStringRegexp)(r),"g"),"__ESC_COLON_"+r));let r=(0,i.parseUrl)(t),n=r.pathname;n&&(n=l(n));let s=r.href;s&&(s=l(s));let o=r.hostname;o&&(o=l(o));let c=r.hash;return c&&(c=l(c)),{...r,pathname:n,hostname:o,href:s,hash:c}}function m(e){let t,r,a=Object.assign({},e.query),i=d(e),{hostname:o,query:c}=i,m=i.pathname;i.hash&&(m=""+m+i.hash);let f=[],p=[];for(let e of((0,n.pathToRegexp)(m,p),p))f.push(e.name);if(o){let e=[];for(let t of((0,n.pathToRegexp)(o,e),e))f.push(t.name)}let g=(0,n.compile)(m,{validate:!1});for(let[r,a]of(o&&(t=(0,n.compile)(o,{validate:!1})),Object.entries(c)))Array.isArray(a)?c[r]=a.map(t=>u(l(t),e.params)):"string"==typeof a&&(c[r]=u(l(a),e.params));let h=Object.keys(e.params).filter(e=>"nextInternalLocale"!==e);if(e.appendParamsToQuery&&!h.some(e=>f.includes(e)))for(let t of h)t in c||(c[t]=e.params[t]);if((0,s.isInterceptionRouteAppPath)(m))for(let t of m.split("/")){let r=s.INTERCEPTION_ROUTE_MARKERS.find(e=>t.startsWith(e));if(r){"(..)(..)"===r?(e.params["0"]="(..)",e.params["1"]="(..)"):e.params["0"]=r;break}}try{let[n,a]=(r=g(e.params)).split("#",2);t&&(i.hostname=t(e.params)),i.pathname=n,i.hash=(a?"#":"")+(a||""),delete i.search}catch(e){if(e.message.match(/Expected .*? to not repeat, but got an array/))throw Object.defineProperty(Error("To use a multi-match in the destination you must add `*` at the end of the param name to signify it should repeat. https://nextjs.org/docs/messages/invalid-multi-match"),"__NEXT_ERROR_CODE",{value:"E329",enumerable:!1,configurable:!0});throw e}return i.query={...a,...i.query},{newUrl:r,destQuery:c,parsedDestination:i}}},5531:(e,t)=>{"use strict";function r(e){return e.startsWith("/")?e:"/"+e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ensureLeadingSlash",{enumerable:!0,get:function(){return r}})},5604:(e,t,r)=>{Promise.resolve().then(r.bind(r,2827))},6341:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getPreviouslyRevalidatedTags:function(){return x},getUtils:function(){return h},interpolateDynamicPath:function(){return p},normalizeDynamicRouteParams:function(){return g},normalizeVercelUrl:function(){return f}});let n=r(9551),a=r(1959),i=r(2437),s=r(4396),o=r(8034),l=r(5526),c=r(2887),u=r(4722),d=r(6143),m=r(7912);function f(e,t,r){let a=(0,n.parse)(e.url,!0);for(let e of(delete a.search,Object.keys(a.query))){let n=e!==d.NEXT_QUERY_PARAM_PREFIX&&e.startsWith(d.NEXT_QUERY_PARAM_PREFIX),i=e!==d.NEXT_INTERCEPTION_MARKER_PREFIX&&e.startsWith(d.NEXT_INTERCEPTION_MARKER_PREFIX);(n||i||t.includes(e)||r&&Object.keys(r.groups).includes(e))&&delete a.query[e]}e.url=(0,n.format)(a)}function p(e,t,r){if(!r)return e;for(let n of Object.keys(r.groups)){let a,{optional:i,repeat:s}=r.groups[n],o=`[${s?"...":""}${n}]`;i&&(o=`[${o}]`);let l=t[n];a=Array.isArray(l)?l.map(e=>e&&encodeURIComponent(e)).join("/"):l?encodeURIComponent(l):"",e=e.replaceAll(o,a)}return e}function g(e,t,r,n){let a={};for(let i of Object.keys(t.groups)){let s=e[i];"string"==typeof s?s=(0,u.normalizeRscURL)(s):Array.isArray(s)&&(s=s.map(u.normalizeRscURL));let o=r[i],l=t.groups[i].optional;if((Array.isArray(o)?o.some(e=>Array.isArray(s)?s.some(t=>t.includes(e)):null==s?void 0:s.includes(e)):null==s?void 0:s.includes(o))||void 0===s&&!(l&&n))return{params:{},hasValidParams:!1};l&&(!s||Array.isArray(s)&&1===s.length&&("index"===s[0]||s[0]===`[[...${i}]]`))&&(s=void 0,delete e[i]),s&&"string"==typeof s&&t.groups[i].repeat&&(s=s.split("/")),s&&(a[i]=s)}return{params:a,hasValidParams:!0}}function h({page:e,i18n:t,basePath:r,rewrites:n,pageIsDynamic:u,trailingSlash:d,caseSensitive:h}){let x,y,b;return u&&(x=(0,s.getNamedRouteRegex)(e,{prefixRouteKeys:!1}),b=(y=(0,o.getRouteMatcher)(x))(e)),{handleRewrites:function(s,o){let m={},f=o.pathname,p=n=>{let c=(0,i.getPathMatch)(n.source+(d?"(/)?":""),{removeUnnamedParams:!0,strict:!0,sensitive:!!h});if(!o.pathname)return!1;let p=c(o.pathname);if((n.has||n.missing)&&p){let e=(0,l.matchHas)(s,o.query,n.has,n.missing);e?Object.assign(p,e):p=!1}if(p){let{parsedDestination:i,destQuery:s}=(0,l.prepareDestination)({appendParamsToQuery:!0,destination:n.destination,params:p,query:o.query});if(i.protocol)return!0;if(Object.assign(m,s,p),Object.assign(o.query,i.query),delete i.query,Object.assign(o,i),!(f=o.pathname))return!1;if(r&&(f=f.replace(RegExp(`^${r}`),"")||"/"),t){let e=(0,a.normalizeLocalePath)(f,t.locales);f=e.pathname,o.query.nextInternalLocale=e.detectedLocale||p.nextInternalLocale}if(f===e)return!0;if(u&&y){let e=y(f);if(e)return o.query={...o.query,...e},!0}}return!1};for(let e of n.beforeFiles||[])p(e);if(f!==e){let t=!1;for(let e of n.afterFiles||[])if(t=p(e))break;if(!t&&!(()=>{let t=(0,c.removeTrailingSlash)(f||"");return t===(0,c.removeTrailingSlash)(e)||(null==y?void 0:y(t))})()){for(let e of n.fallback||[])if(t=p(e))break}}return m},defaultRouteRegex:x,dynamicRouteMatcher:y,defaultRouteMatches:b,getParamsFromRouteMatches:function(e){if(!x)return null;let{groups:t,routeKeys:r}=x,n=(0,o.getRouteMatcher)({re:{exec:e=>{let n=Object.fromEntries(new URLSearchParams(e));for(let[e,t]of Object.entries(n)){let r=(0,m.normalizeNextQueryParam)(e);r&&(n[r]=t,delete n[e])}let a={};for(let e of Object.keys(r)){let i=r[e];if(!i)continue;let s=t[i],o=n[e];if(!s.optional&&!o)return null;a[s.pos]=o}return a}},groups:t})(e);return n||null},normalizeDynamicRouteParams:(e,t)=>x&&b?g(e,x,b,t):{params:{},hasValidParams:!1},normalizeVercelUrl:(e,t)=>f(e,t,x),interpolateDynamicPath:(e,t)=>p(e,t,x)}}function x(e,t){return"string"==typeof e[d.NEXT_CACHE_REVALIDATED_TAGS_HEADER]&&e[d.NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER]===t?e[d.NEXT_CACHE_REVALIDATED_TAGS_HEADER].split(","):[]}},6415:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{t.parse=function(t,r){if("string"!=typeof t)throw TypeError("argument str must be a string");for(var a={},i=t.split(n),s=(r||{}).decode||e,o=0;o<i.length;o++){var l=i[o],c=l.indexOf("=");if(!(c<0)){var u=l.substr(0,c).trim(),d=l.substr(++c,l.length).trim();'"'==d[0]&&(d=d.slice(1,-1)),void 0==a[u]&&(a[u]=function(e,t){try{return t(e)}catch(t){return e}}(d,s))}}return a},t.serialize=function(e,t,n){var i=n||{},s=i.encode||r;if("function"!=typeof s)throw TypeError("option encode is invalid");if(!a.test(e))throw TypeError("argument name is invalid");var o=s(t);if(o&&!a.test(o))throw TypeError("argument val is invalid");var l=e+"="+o;if(null!=i.maxAge){var c=i.maxAge-0;if(isNaN(c)||!isFinite(c))throw TypeError("option maxAge is invalid");l+="; Max-Age="+Math.floor(c)}if(i.domain){if(!a.test(i.domain))throw TypeError("option domain is invalid");l+="; Domain="+i.domain}if(i.path){if(!a.test(i.path))throw TypeError("option path is invalid");l+="; Path="+i.path}if(i.expires){if("function"!=typeof i.expires.toUTCString)throw TypeError("option expires is invalid");l+="; Expires="+i.expires.toUTCString()}if(i.httpOnly&&(l+="; HttpOnly"),i.secure&&(l+="; Secure"),i.sameSite)switch("string"==typeof i.sameSite?i.sameSite.toLowerCase():i.sameSite){case!0:case"strict":l+="; SameSite=Strict";break;case"lax":l+="; SameSite=Lax";break;case"none":l+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return l};var e=decodeURIComponent,r=encodeURIComponent,n=/; */,a=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),e.exports=t})()},6759:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseUrl",{enumerable:!0,get:function(){return i}});let n=r(2785),a=r(3736);function i(e){if(e.startsWith("/"))return(0,a.parseRelativeUrl)(e);let t=new URL(e);return{hash:t.hash,hostname:t.hostname,href:t.href,pathname:t.pathname,port:t.port,protocol:t.protocol,query:(0,n.searchParamsToUrlQuery)(t.searchParams),search:t.search}}},8034:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getRouteMatcher",{enumerable:!0,get:function(){return a}});let n=r(4827);function a(e){let{re:t,groups:r}=e;return e=>{let a=t.exec(e);if(!a)return!1;let i=e=>{try{return decodeURIComponent(e)}catch(e){throw Object.defineProperty(new n.DecodeError("failed to decode param"),"__NEXT_ERROR_CODE",{value:"E528",enumerable:!1,configurable:!0})}},s={};for(let[e,t]of Object.entries(r)){let r=a[t.pos];void 0!==r&&(t.repeat?s[e]=r.split("/").map(e=>i(e)):s[e]=i(r))}return s}}},8212:(e,t,r)=>{"use strict";function n(e){return function(){let{cookie:t}=e;if(!t)return{};let{parse:n}=r(6415);return n(Array.isArray(t)?t.join("; "):t)}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getCookieParser",{enumerable:!0,get:function(){return n}})},8304:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DEFAULT_METADATA_ROUTE_EXTENSIONS:function(){return o},STATIC_METADATA_IMAGES:function(){return s},getExtensionRegexString:function(){return l},isMetadataPage:function(){return d},isMetadataRoute:function(){return m},isMetadataRouteFile:function(){return c},isStaticMetadataRoute:function(){return u}});let n=r(2958),a=r(4722),i=r(554),s={icon:{filename:"icon",extensions:["ico","jpg","jpeg","png","svg"]},apple:{filename:"apple-icon",extensions:["jpg","jpeg","png"]},favicon:{filename:"favicon",extensions:["ico"]},openGraph:{filename:"opengraph-image",extensions:["jpg","jpeg","png","gif"]},twitter:{filename:"twitter-image",extensions:["jpg","jpeg","png","gif"]}},o=["js","jsx","ts","tsx"],l=(e,t)=>t&&0!==t.length?`(?:\\.(${e.join("|")})|(\\.(${t.join("|")})))`:`(\\.(?:${e.join("|")}))`;function c(e,t,r){let a=(r?"":"?")+"$",i=`\\d?${r?"":"(-\\w{6})?"}`,o=[RegExp(`^[\\\\/]robots${l(t.concat("txt"),null)}${a}`),RegExp(`^[\\\\/]manifest${l(t.concat("webmanifest","json"),null)}${a}`),RegExp("^[\\\\/]favicon\\.ico$"),RegExp(`[\\\\/]sitemap${l(["xml"],t)}${a}`),RegExp(`[\\\\/]${s.icon.filename}${i}${l(s.icon.extensions,t)}${a}`),RegExp(`[\\\\/]${s.apple.filename}${i}${l(s.apple.extensions,t)}${a}`),RegExp(`[\\\\/]${s.openGraph.filename}${i}${l(s.openGraph.extensions,t)}${a}`),RegExp(`[\\\\/]${s.twitter.filename}${i}${l(s.twitter.extensions,t)}${a}`)],c=(0,n.normalizePathSep)(e);return o.some(e=>e.test(c))}function u(e){let t=e.replace(/\/route$/,"");return(0,i.isAppRouteRoute)(e)&&c(t,[],!0)&&"/robots.txt"!==t&&"/manifest.webmanifest"!==t&&!t.endsWith("/sitemap.xml")}function d(e){return!(0,i.isAppRouteRoute)(e)&&c(e,[],!1)}function m(e){let t=(0,a.normalizeAppPath)(e).replace(/^\/?app\//,"").replace("/[__metadata_id__]","").replace(/\/route$/,"");return"/"!==t[0]&&(t="/"+t),(0,i.isAppRouteRoute)(e)&&c(t,[],!1)}},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")},9572:(e,t,r)=>{Promise.resolve().then(r.bind(r,1204))}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[447,145],()=>r(4116));module.exports=n})();