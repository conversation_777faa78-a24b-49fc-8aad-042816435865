'use client';

import { useState } from 'react';
import { InvestmentAnalysisForm } from '@/components/InvestmentAnalysisForm';
import { AnalysisResults } from '@/components/AnalysisResults';
import { InvestmentScenario, InvestmentResult, ComparisonResult } from '@/lib/types';

interface AnalysisData {
  investmentResult: InvestmentResult;
  comparisonResult: ComparisonResult;
  comparisonSummary: any;
  chartData: any;
}

export default function Home() {
  const [analysisData, setAnalysisData] = useState<AnalysisData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleAnalysisSubmit = async (scenario: Omit<InvestmentScenario, 'id' | 'createdAt'>) => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/analyze', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(scenario),
      });

      if (!response.ok) {
        throw new Error('Failed to analyze investment');
      }

      const data = await response.json();
      setAnalysisData(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleReset = () => {
    setAnalysisData(null);
    setError(null);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-2">
            What If Investment Analyzer
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-300">
            Analyze your investment scenarios with real market data and benchmark comparisons
          </p>
        </div>

        {/* Main Content */}
        <div className="max-w-7xl mx-auto">
          {!analysisData ? (
            /* Input Form */
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
              <InvestmentAnalysisForm
                onSubmit={handleAnalysisSubmit}
                loading={loading}
                error={error}
              />
            </div>
          ) : (
            /* Results Display */
            <div className="space-y-6">
              <div className="flex justify-between items-center">
                <h2 className="text-2xl font-semibold text-gray-900 dark:text-white">
                  Analysis Results
                </h2>
                <button
                  onClick={handleReset}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  New Analysis
                </button>
              </div>

              <AnalysisResults data={analysisData} />
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
