'use client';

import { InvestmentResult, ComparisonResult } from '@/lib/types';
import { ComparisonTable } from '@/components/tables/ComparisonTable';
import { TimeSeriesChart } from '@/components/charts/TimeSeriesChart';
import { ComparisonBarChart } from '@/components/charts/ComparisonBarChart';
import { PerformanceMetrics } from '@/components/charts/PerformanceMetrics';
import { formatCurrency, formatPercentage } from '@/lib/utils';

interface AnalysisResultsProps {
  data: {
    investmentResult: InvestmentResult;
    comparisonResult: ComparisonResult;
    comparisonSummary: any;
    chartData: any;
  };
}

export function AnalysisResults({ data }: AnalysisResultsProps) {
  const { investmentResult, comparisonResult, comparisonSummary, chartData } = data;

  return (
    <div className="space-y-6">
      {/* Investment Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg">
          <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Initial Investment</h3>
          <p className="text-2xl font-bold text-gray-900 dark:text-white">
            {formatCurrency(investmentResult.initialValue)}
          </p>
        </div>
        
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg">
          <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Current Value</h3>
          <p className="text-2xl font-bold text-green-600 dark:text-green-400">
            {formatCurrency(investmentResult.currentValue)}
          </p>
        </div>
        
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg">
          <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Total Return</h3>
          <p className={`text-2xl font-bold ${investmentResult.totalReturn >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
            {formatCurrency(investmentResult.totalReturn)}
          </p>
        </div>
        
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg">
          <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">CAGR</h3>
          <p className={`text-2xl font-bold ${investmentResult.cagr >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
            {formatPercentage(investmentResult.cagr)}
          </p>
        </div>
      </div>

      {/* Investment Details */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Investment Details</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">Stock Symbol:</span>
              <span className="font-medium text-gray-900 dark:text-white">{investmentResult.scenario.stockSymbol}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">Investment Period:</span>
              <span className="font-medium text-gray-900 dark:text-white">
                {investmentResult.scenario.startDate.toLocaleDateString()} - {investmentResult.scenario.endDate.toLocaleDateString()}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">Absolute Return:</span>
              <span className={`font-medium ${investmentResult.absoluteReturn >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                {formatPercentage(investmentResult.absoluteReturn)}
              </span>
            </div>
          </div>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">Annualized Return:</span>
              <span className={`font-medium ${investmentResult.annualizedReturn >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                {formatPercentage(investmentResult.annualizedReturn)}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">Investment Amount:</span>
              <span className="font-medium text-gray-900 dark:text-white">{formatCurrency(investmentResult.scenario.investmentAmount)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">Profit/Loss:</span>
              <span className={`font-medium ${investmentResult.totalReturn >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                {investmentResult.totalReturn >= 0 ? '+' : ''}{formatCurrency(investmentResult.totalReturn)}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Performance Metrics */}
      {comparisonSummary && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Performance Insights</h3>
          <div className="space-y-2">
            {comparisonSummary.insights?.map((insight: string, index: number) => (
              <div key={index} className="flex items-start space-x-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                <p className="text-gray-700 dark:text-gray-300">{insight}</p>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Time Series Chart */}
        {chartData?.timeSeriesData && (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Investment Growth Over Time</h3>
            <TimeSeriesChart data={chartData.timeSeriesData} />
          </div>
        )}

        {/* Comparison Bar Chart */}
        {chartData?.barChartData && (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Performance Comparison</h3>
            <ComparisonBarChart data={chartData.barChartData} />
          </div>
        )}
      </div>

      {/* Performance Metrics Component */}
      {comparisonSummary && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Detailed Performance Metrics</h3>
          <PerformanceMetrics
            data={{
              bestPerformer: {
                name: comparisonSummary.investment.rank === 1 ? comparisonSummary.investment.name : comparisonSummary.benchmarks[0]?.name || 'N/A',
                cagr: comparisonSummary.investment.rank === 1 ? comparisonSummary.investment.cagr : comparisonSummary.benchmarks[0]?.cagr || 0,
                absoluteReturn: comparisonSummary.investment.rank === 1 ? comparisonSummary.investment.absoluteReturn : comparisonSummary.benchmarks[0]?.absoluteReturn || 0,
              },
              worstPerformer: {
                name: comparisonSummary.benchmarks[comparisonSummary.benchmarks.length - 1]?.name || 'N/A',
                cagr: comparisonSummary.benchmarks[comparisonSummary.benchmarks.length - 1]?.cagr || 0,
                absoluteReturn: comparisonSummary.benchmarks[comparisonSummary.benchmarks.length - 1]?.absoluteReturn || 0,
              },
              averageCagr: (comparisonSummary.investment.cagr + comparisonSummary.benchmarks.reduce((sum, b) => sum + b.cagr, 0)) / (comparisonSummary.benchmarks.length + 1),
              volatilityRanking: [
                { name: 'Fixed Deposit', volatility: 0 },
                { name: 'Gold', volatility: 15 },
                { name: 'Nifty 50', volatility: 20 },
                { name: comparisonSummary.investment.name, volatility: 25 },
              ].sort((a, b) => a.volatility - b.volatility),
            }}
            investmentAmount={investmentResult.scenario.investmentAmount}
            investmentPeriod={`${investmentResult.scenario.startDate.toLocaleDateString()} - ${investmentResult.scenario.endDate.toLocaleDateString()}`}
          />
        </div>
      )}

      {/* Comparison Table */}
      {comparisonSummary && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Benchmark Comparison</h3>
          <ComparisonTable
            data={[comparisonSummary.investment, ...comparisonSummary.benchmarks]}
            investmentName={comparisonSummary.investment.name}
            title="Investment vs Benchmarks"
            showOutperformance={true}
          />
        </div>
      )}

      {/* Benchmark Details */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Benchmark Performance</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {Object.entries(comparisonResult.benchmarks).map(([key, benchmark]) => (
            <div key={key} className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
              <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                {key === 'GOLD' ? 'Gold' : key === 'FD' ? 'Fixed Deposit' : key === 'NIFTY' ? 'Nifty 50' : key}
              </h4>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Initial:</span>
                  <span className="text-gray-900 dark:text-white">{formatCurrency(benchmark.initialValue)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Current:</span>
                  <span className="text-gray-900 dark:text-white">{formatCurrency(benchmark.currentValue)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">CAGR:</span>
                  <span className={`${benchmark.cagr >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                    {formatPercentage(benchmark.cagr)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Return:</span>
                  <span className={`${benchmark.absoluteReturn >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                    {formatPercentage(benchmark.absoluteReturn)}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Save Scenario Button */}
      <div className="flex justify-center">
        <button className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
          Save This Scenario
        </button>
      </div>
    </div>
  );
}
