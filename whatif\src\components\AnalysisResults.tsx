'use client';

import { InvestmentResult, ComparisonResult } from '@/lib/types';
import ComparisonTable from '@/components/tables/ComparisonTable';
import TimeSeriesChart from '@/components/charts/TimeSeriesChart';
import ComparisonBarChart from '@/components/charts/ComparisonBarChart';
import PerformanceMetrics from '@/components/charts/PerformanceMetrics';
import { formatCurrency, formatPercentage } from '@/lib/utils';

interface AnalysisResultsProps {
  data: {
    investmentResult: InvestmentResult;
    comparisonResult: ComparisonResult;
    comparisonSummary: any;
    chartData: any;
  };
}

export function AnalysisResults({ data }: AnalysisResultsProps) {
  const { investmentResult, comparisonResult, comparisonSummary, chartData } = data;

  return (
    <div className="space-y-6">
      {/* Investment Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg">
          <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Initial Investment</h3>
          <p className="text-2xl font-bold text-gray-900 dark:text-white">
            {formatCurrency(investmentResult.initialValue)}
          </p>
        </div>
        
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg">
          <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Current Value</h3>
          <p className="text-2xl font-bold text-green-600 dark:text-green-400">
            {formatCurrency(investmentResult.currentValue)}
          </p>
        </div>
        
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg">
          <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Total Return</h3>
          <p className={`text-2xl font-bold ${investmentResult.totalReturn >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
            {formatCurrency(investmentResult.totalReturn)}
          </p>
        </div>
        
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg">
          <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">CAGR</h3>
          <p className={`text-2xl font-bold ${investmentResult.cagr >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
            {formatPercentage(investmentResult.cagr)}
          </p>
        </div>
      </div>

      {/* Investment Details */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Investment Details</h3>
        <div className="mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
          <div className="flex items-center gap-2 text-sm text-blue-800 dark:text-blue-200">
            <span className="text-green-600">✅</span>
            <span className="font-medium">Real Market Data:</span>
            <span>Stock prices from Angel One API</span>
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">Stock Symbol:</span>
              <span className="font-medium text-gray-900 dark:text-white">{investmentResult.scenario.stockSymbol}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">Investment Period:</span>
              <span className="font-medium text-gray-900 dark:text-white">
                {new Date(investmentResult.scenario.startDate).toLocaleDateString()} - {new Date(investmentResult.scenario.endDate).toLocaleDateString()}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">Absolute Return:</span>
              <span className={`font-medium ${investmentResult.absoluteReturn >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                {formatPercentage(investmentResult.absoluteReturn)}
              </span>
            </div>
          </div>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">Annualized Return:</span>
              <span className={`font-medium ${investmentResult.annualizedReturn >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                {formatPercentage(investmentResult.annualizedReturn)}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">Investment Amount:</span>
              <span className="font-medium text-gray-900 dark:text-white">{formatCurrency(investmentResult.scenario.investmentAmount)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">Profit/Loss:</span>
              <span className={`font-medium ${investmentResult.totalReturn >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                {investmentResult.totalReturn >= 0 ? '+' : ''}{formatCurrency(investmentResult.totalReturn)}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Performance Metrics */}
      {comparisonSummary && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Performance Insights</h3>
          <div className="space-y-2">
            {comparisonSummary.insights?.map((insight: string, index: number) => (
              <div key={index} className="flex items-start space-x-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                <p className="text-gray-700 dark:text-gray-300">{insight}</p>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Time Series Chart */}
        {chartData?.timeSeriesData && (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Investment Growth Over Time</h3>
            <TimeSeriesChart data={chartData.timeSeriesData} />
          </div>
        )}

        {/* Comparison Bar Chart */}
        {chartData?.barChartData && (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Performance Comparison</h3>
            <ComparisonBarChart data={chartData.barChartData} />
          </div>
        )}
      </div>

      {/* Performance Metrics Component */}
      {comparisonSummary && (() => {
        // Create array of all investments including the main investment
        const allInvestments = [
          {
            name: comparisonSummary.investment.name,
            cagr: comparisonSummary.investment.cagr,
            absoluteReturn: comparisonSummary.investment.absoluteReturn,
          },
          ...comparisonSummary.benchmarks.map(b => ({
            name: b.name,
            cagr: b.cagr,
            absoluteReturn: b.absoluteReturn,
          }))
        ];

        // Find actual best and worst performers by CAGR
        const bestPerformer = allInvestments.reduce((best, current) =>
          current.cagr > best.cagr ? current : best
        );

        const worstPerformer = allInvestments.reduce((worst, current) =>
          current.cagr < worst.cagr ? current : worst
        );

        return (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Detailed Performance Metrics</h3>
            <PerformanceMetrics
              data={{
                bestPerformer,
                worstPerformer,
                averageCagr: allInvestments.reduce((sum, inv) => sum + inv.cagr, 0) / allInvestments.length,
                volatilityRanking: [
                  { name: 'Fixed Deposit', volatility: 0 },
                  { name: 'Gold', volatility: 15 },
                  { name: 'Nifty 50', volatility: 20 },
                  { name: comparisonSummary.investment.name, volatility: 25 },
                ].sort((a, b) => a.volatility - b.volatility),
              }}
              investmentAmount={investmentResult.scenario.investmentAmount}
              investmentPeriod={`${new Date(investmentResult.scenario.startDate).toLocaleDateString()} - ${new Date(investmentResult.scenario.endDate).toLocaleDateString()}`}
            />
          </div>
        );
      })()}

      {/* Comparison Table */}
      {comparisonSummary && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Benchmark Comparison</h3>
          <div className="mb-4 grid grid-cols-1 md:grid-cols-2 gap-3">
            <div className="p-3 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
              <div className="flex items-center gap-2 text-sm text-green-800 dark:text-green-200">
                <span className="text-green-600">✅</span>
                <span className="font-medium">Real Data:</span>
                <span>Stock & Nifty 50</span>
              </div>
            </div>
            <div className="p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
              <div className="flex items-center gap-2 text-sm text-yellow-800 dark:text-yellow-200">
                <span className="text-yellow-600">⚠️</span>
                <span className="font-medium">Estimated:</span>
                <span>Gold & Fixed Deposit</span>
              </div>
            </div>
          </div>
          <ComparisonTable
            data={[comparisonSummary.investment, ...comparisonSummary.benchmarks]}
            investmentName={comparisonSummary.investment.name}
            title="Investment vs Benchmarks"
            showOutperformance={true}
          />
        </div>
      )}

      {/* Benchmark Details */}
      {comparisonSummary && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Benchmark Performance</h3>
          <div className="mb-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
            <div className="flex items-center gap-2 text-sm text-yellow-800 dark:text-yellow-200">
              <span className="text-yellow-600">⚠️</span>
              <span className="font-medium">Note:</span>
              <span>Benchmark data uses consistent calculation methodology as comparison table</span>
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {comparisonSummary.benchmarks.map((benchmark, index) => (
              <div key={benchmark.name} className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                  {benchmark.name}
                </h4>
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Initial:</span>
                    <span className="text-gray-900 dark:text-white">{formatCurrency(benchmark.initialValue)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Current:</span>
                    <span className="text-gray-900 dark:text-white">{formatCurrency(benchmark.currentValue)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">CAGR:</span>
                    <span className={`${benchmark.cagr >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                      {formatPercentage(benchmark.cagr)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Return:</span>
                    <span className={`${benchmark.absoluteReturn >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                      {formatPercentage(benchmark.absoluteReturn)}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Save Scenario Button */}
      <div className="flex justify-center">
        <button className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
          Save This Scenario
        </button>
      </div>
    </div>
  );
}
