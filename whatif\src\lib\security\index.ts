// Security utilities for the What If investment analysis tool

import { STORAGE_CONFIG } from '../config';

/**
 * Validate environment variables
 * @returns Validation result with missing variables
 */
export function validateEnvironment(): {
  isValid: boolean;
  missingVars: string[];
  warnings: string[];
} {
  const requiredVars = [
    'NEXT_PUBLIC_ANGEL_ONE_API_URL',
    'NEXT_PUBLIC_APP_NAME',
    'NEXT_PUBLIC_APP_VERSION',
  ];

  const optionalVars = [
    'ANGEL_ONE_API_KEY',
    'ANGEL_ONE_CLIENT_ID',
    'ANGEL_ONE_PASSWORD',
    'ANGEL_ONE_TOTP_SECRET',
  ];

  const missingVars: string[] = [];
  const warnings: string[] = [];

  // Check required variables
  requiredVars.forEach(varName => {
    if (!process.env[varName]) {
      missingVars.push(varName);
    }
  });

  // Check optional but important variables
  optionalVars.forEach(varName => {
    if (!process.env[varName]) {
      warnings.push(`${varName} is not set - API functionality will be limited`);
    }
  });

  // Check for development secrets in production
  if (process.env.NODE_ENV === 'production') {
    if (process.env.NEXTAUTH_SECRET === 'development_secret_change_in_production') {
      missingVars.push('NEXTAUTH_SECRET (using development value in production)');
    }
  }

  return {
    isValid: missingVars.length === 0,
    missingVars,
    warnings,
  };
}

/**
 * Sanitize sensitive data for logging
 * @param data - Data object to sanitize
 * @param visited - Set to track visited objects (prevents circular references)
 * @returns Sanitized data object
 */
export function sanitizeForLogging(
  data: Record<string, unknown>,
  visited: WeakSet<object> = new WeakSet()
): Record<string, unknown> {
  const sensitiveKeys = [
    'password',
    'token',
    'secret',
    'key',
    'auth',
    'credential',
    'totp',
  ];

  // Handle non-object types
  if (typeof data !== 'object' || data === null) {
    return { value: data };
  }

  // Handle circular references
  if (visited.has(data)) {
    return { '[Circular Reference]': true };
  }
  visited.add(data);

  const sanitized: Record<string, unknown> = {};

  try {
    Object.keys(data).forEach(key => {
      const lowerKey = key.toLowerCase();
      const isSensitive = sensitiveKeys.some(sensitiveKey =>
        lowerKey.includes(sensitiveKey)
      );

      if (isSensitive) {
        sanitized[key] = '[REDACTED]';
      } else if (typeof data[key] === 'object' && data[key] !== null) {
        // Handle arrays
        if (Array.isArray(data[key])) {
          sanitized[key] = '[Array]';
        } else {
          sanitized[key] = sanitizeForLogging(data[key] as Record<string, unknown>, visited);
        }
      } else {
        sanitized[key] = data[key];
      }
    });
  } catch (error) {
    return { '[Sanitization Error]': 'Unable to sanitize object' };
  }

  return sanitized;
}

/**
 * Simple encryption for local storage (basic obfuscation)
 * Note: This is not cryptographically secure, just basic obfuscation
 * @param text - Text to encrypt
 * @returns Encrypted text
 */
export function simpleEncrypt(text: string): string {
  if (!STORAGE_CONFIG.encryption.enabled) {
    return text;
  }

  // Simple base64 encoding with character shifting (not secure, just obfuscation)
  const shifted = text
    .split('')
    .map(char => String.fromCharCode(char.charCodeAt(0) + 3))
    .join('');
  
  return btoa(shifted);
}

/**
 * Simple decryption for local storage
 * @param encryptedText - Encrypted text to decrypt
 * @returns Decrypted text
 */
export function simpleDecrypt(encryptedText: string): string {
  if (!STORAGE_CONFIG.encryption.enabled) {
    return encryptedText;
  }

  try {
    const decoded = atob(encryptedText);
    return decoded
      .split('')
      .map(char => String.fromCharCode(char.charCodeAt(0) - 3))
      .join('');
  } catch {
    return encryptedText; // Return as-is if decryption fails
  }
}

/**
 * Validate API key format
 * @param apiKey - API key to validate
 * @returns Validation result
 */
export function validateApiKey(apiKey: string): {
  isValid: boolean;
  error?: string;
} {
  if (!apiKey || typeof apiKey !== 'string') {
    return { isValid: false, error: 'API key is required' };
  }

  if (apiKey.length < 10) {
    return { isValid: false, error: 'API key is too short' };
  }

  if (apiKey.includes(' ')) {
    return { isValid: false, error: 'API key should not contain spaces' };
  }

  return { isValid: true };
}

/**
 * Rate limiting utility
 */
export class RateLimiter {
  private requests: number[] = [];
  private readonly maxRequests: number;
  private readonly timeWindow: number; // in milliseconds

  constructor(maxRequests: number, timeWindowSeconds: number) {
    this.maxRequests = maxRequests;
    this.timeWindow = timeWindowSeconds * 1000;
  }

  /**
   * Check if request is allowed
   * @returns Whether request is allowed
   */
  isAllowed(): boolean {
    const now = Date.now();
    
    // Remove old requests outside the time window
    this.requests = this.requests.filter(time => now - time < this.timeWindow);
    
    // Check if we're under the limit
    if (this.requests.length < this.maxRequests) {
      this.requests.push(now);
      return true;
    }
    
    return false;
  }

  /**
   * Get time until next request is allowed
   * @returns Milliseconds until next request
   */
  getTimeUntilReset(): number {
    if (this.requests.length === 0) return 0;
    
    const oldestRequest = Math.min(...this.requests);
    const timeUntilReset = this.timeWindow - (Date.now() - oldestRequest);
    
    return Math.max(0, timeUntilReset);
  }
}
